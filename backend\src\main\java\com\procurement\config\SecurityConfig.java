package com.procurement.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfigurationSource;

/**
 * Spring Security配置类
 * 
 * <AUTHOR> Platform Team
 * @version 1.0.0
 * @since 2025-06-29
 */
@Configuration
@EnableWebSecurity
public class SecurityConfig extends WebSecurityConfigurerAdapter {

    private final CorsConfigurationSource corsConfigurationSource;

    public SecurityConfig(CorsConfigurationSource corsConfigurationSource) {
        this.corsConfigurationSource = corsConfigurationSource;
    }

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http
            // 禁用CSRF
            .csrf().disable()
            
            // 启用CORS
            .cors().configurationSource(corsConfigurationSource)
            
            .and()
            
            // 配置会话管理
            .sessionManagement()
            .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            
            .and()
            
            // 配置请求授权
            .authorizeRequests()
            
            // 放行公共接口
            .antMatchers(
                "/health/**",           // 健康检查
                "/auth/**",             // 认证接口
                "/swagger-ui.html",     // Swagger UI
                "/swagger-ui/**",       // Swagger UI资源
                "/v3/api-docs/**",      // OpenAPI文档
                "/swagger-resources/**", // Swagger资源
                "/webjars/**",          // WebJars资源
                "/druid/**",            // Druid监控
                "/actuator/**",         // Spring Boot Actuator
                "/error"                // 错误页面
            ).permitAll()
            
            // 其他请求需要认证
            .anyRequest().authenticated()
            
            .and()
            
            // 禁用默认登录页面
            .formLogin().disable()
            
            // 禁用HTTP Basic认证
            .httpBasic().disable();
    }

    /**
     * 密码编码器
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
}
