# 采购数字化综合管理平台详细设计说明书

**文档版本：** 1.0  
**编写日期：** 2025年6月  
**文档类型：** 系统详细设计文档（DDD）

## 1. 引言

### 1.1 文档目的与范围

详细设计说明书是系统开发过程中的重要技术文档，它在概要设计的基础上，深入到每个模块的具体实现细节。如果说概要设计是建筑的施工图纸，那么详细设计就是每个房间的装修图纸，精确到每个插座的位置、每根管线的走向。

本文档将为开发团队提供代码级别的设计指导，包括类的详细定义、方法的具体实现、数据库表的完整结构、接口的精确规范等。通过这份文档，开发人员能够直接编写代码，测试人员能够设计测试用例，运维人员能够理解系统的技术架构。

### 1.2 设计依据

本详细设计基于以下文档和分析结果：
- 《采购数字化综合管理平台系统概要设计说明书》
- 采购台账Excel模板分析（包含33个业务字段）
- 业务需求规格说明书
- 技术方案设计书

采购台账分析显示系统需要处理33个核心字段，涵盖项目基本信息、人员管理、时间节点、财务数据、合同信息和供应商信息等六大业务领域。

### 1.3 技术框架选择

基于现代企业级应用的技术要求和团队技术栈，系统采用以下技术框架：

**后端技术栈：**
- Spring Boot 2.7.x（应用框架）
- Spring Security（安全框架）
- Spring Data JPA（数据访问层）
- MySQL 8.0（关系型数据库）
- Redis 6.x（缓存数据库）
- Maven（项目管理工具）

**前端技术栈：**
- Vue.js 3.x（前端框架）
- Element Plus（UI组件库）
- Axios（HTTP客户端）
- Webpack（构建工具）

**部署与运维：**
- Docker（容器化部署）
- Nginx（Web服务器）
- Jenkins（持续集成）

## 2. 系统架构详细设计

### 2.1 分层架构详细说明

系统采用经典的四层架构模式，每层都有明确的职责划分和接口定义。

**表现层（Presentation Layer）：**
表现层负责用户界面的展示和用户交互的处理。这一层就像是商店的前台，负责接待客户并展示商品。主要组件包括：

- Controller控制器：处理HTTP请求，调用业务层服务
- DTO数据传输对象：定义前后端数据交换格式
- 统一响应格式：标准化API响应结构
- 异常处理器：统一处理系统异常

**业务层（Business Layer）：**
业务层是系统的核心，实现所有的业务逻辑。这一层就像是工厂的生产车间，负责具体的产品制造。主要组件包括：

- Service业务服务：实现具体业务逻辑
- 业务规则引擎：处理复杂的业务规则
- 事务管理：确保数据操作的一致性
- 缓存管理：提高数据访问性能

**数据访问层（Data Access Layer）：**
数据访问层负责与数据库的交互，提供数据的增删改查服务。这一层就像是仓库的管理系统，负责货物的存储和调取。主要组件包括：

- Repository数据仓库：定义数据访问接口
- Entity实体类：映射数据库表结构
- 查询构建器：动态构建复杂查询
- 数据库连接池：管理数据库连接

**基础设施层（Infrastructure Layer）：**
基础设施层提供系统运行所需的各种技术支撑。这一层就像是城市的基础设施，为整个城市的运转提供支撑。主要组件包括：

- 配置管理：系统配置的统一管理
- 日志管理：系统运行日志的记录和分析
- 监控管理：系统性能和健康状态监控
- 安全管理：认证、授权、加密等安全功能

### 2.2 微服务架构设计

虽然当前版本采用单体架构，但在设计时充分考虑了向微服务架构演进的可能性。系统内部按照业务边界进行模块划分，每个模块相对独立，为将来的服务拆分做好准备。

**服务边界划分：**
- 项目管理服务：处理采购项目相关业务
- 人员协同服务：处理双人制管理等人员相关业务
- 流程控制服务：处理时间节点和流程管控业务
- 财务管理服务：处理预算、付款等财务业务
- 合同管理服务：处理合同相关业务
- 供应商管理服务：处理供应商相关业务

每个服务都有独立的数据模型和业务逻辑，服务之间通过标准化的接口进行通信。

## 3. 数据库详细设计

### 3.1 数据库表结构设计

基于采购台账的33个字段分析，系统设计了完整的数据库表结构。每个表都经过精心设计，既要满足当前业务需求，又要为未来扩展预留空间。

#### 3.1.1 项目管理相关表

**采购项目主表（pm_project）**
```sql
CREATE TABLE pm_project (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '项目ID',
    project_name VARCHAR(255) NOT NULL COMMENT '采购项目/品类名称',
    project_type VARCHAR(50) NOT NULL COMMENT '项目类型(货物/服务/工程)',
    budget_amount DECIMAL(15,2) NOT NULL COMMENT '预算金额（元）',
    procurement_method VARCHAR(50) NOT NULL COMMENT '采购方式',
    organization_form VARCHAR(50) NOT NULL COMMENT '采购组织形式',
    is_government_procurement TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否政府采购',
    is_classified TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否涉密',
    transaction_amount DECIMAL(15,2) COMMENT '成交金额（元）',
    project_status VARCHAR(50) NOT NULL DEFAULT 'CREATED' COMMENT '项目状态',
    demand_department VARCHAR(100) NOT NULL COMMENT '采购需求部门',
    procurement_department VARCHAR(100) NOT NULL COMMENT '采购实施部门',
    created_by BIGINT NOT NULL COMMENT '创建人',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by BIGINT COMMENT '更新人',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    version INT NOT NULL DEFAULT 1 COMMENT '版本号',
    is_deleted TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
    
    INDEX idx_project_name (project_name),
    INDEX idx_project_type (project_type),
    INDEX idx_procurement_method (procurement_method),
    INDEX idx_created_time (created_time),
    INDEX idx_project_status (project_status)
) COMMENT='采购项目主表';
```

**项目状态变更记录表（pm_project_status_log）**
```sql
CREATE TABLE pm_project_status_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '记录ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    old_status VARCHAR(50) COMMENT '原状态',
    new_status VARCHAR(50) NOT NULL COMMENT '新状态',
    change_reason VARCHAR(500) COMMENT '变更原因',
    operator_id BIGINT NOT NULL COMMENT '操作人ID',
    operator_name VARCHAR(100) NOT NULL COMMENT '操作人姓名',
    change_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '变更时间',
    
    FOREIGN KEY (project_id) REFERENCES pm_project(id),
    INDEX idx_project_id (project_id),
    INDEX idx_change_time (change_time)
) COMMENT='项目状态变更记录表';
```

#### 3.1.2 人员协同相关表

**人员信息表（sys_user）**
```sql
CREATE TABLE sys_user (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码(加密)',
    real_name VARCHAR(100) NOT NULL COMMENT '真实姓名',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    department VARCHAR(100) COMMENT '所属部门',
    position VARCHAR(100) COMMENT '职位',
    is_active TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
    has_classified_permission TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否有涉密权限',
    last_login_time DATETIME COMMENT '最后登录时间',
    password_update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '密码更新时间',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_username (username),
    INDEX idx_real_name (real_name),
    INDEX idx_department (department)
) COMMENT='用户信息表';
```

**双人制操作记录表（sys_dual_operation）**
```sql
CREATE TABLE sys_dual_operation (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '操作ID',
    operation_type VARCHAR(50) NOT NULL COMMENT '操作类型',
    business_id BIGINT NOT NULL COMMENT '关联业务ID',
    business_type VARCHAR(50) NOT NULL COMMENT '业务类型',
    first_operator_id BIGINT NOT NULL COMMENT '第一操作人ID',
    first_operator_name VARCHAR(100) NOT NULL COMMENT '第一操作人姓名',
    first_operation_time DATETIME NOT NULL COMMENT '第一次操作时间',
    first_operation_content TEXT COMMENT '第一次操作内容',
    second_operator_id BIGINT COMMENT '第二操作人ID',
    second_operator_name VARCHAR(100) COMMENT '第二操作人姓名',
    second_operation_time DATETIME COMMENT '第二次操作时间',
    second_operation_content TEXT COMMENT '第二次操作内容',
    operation_status VARCHAR(20) NOT NULL DEFAULT 'PENDING' COMMENT '操作状态(PENDING/CONFIRMED/REJECTED)',
    complete_time DATETIME COMMENT '完成时间',
    
    FOREIGN KEY (first_operator_id) REFERENCES sys_user(id),
    FOREIGN KEY (second_operator_id) REFERENCES sys_user(id),
    INDEX idx_business_id (business_id),
    INDEX idx_operation_type (operation_type),
    INDEX idx_first_operation_time (first_operation_time)
) COMMENT='双人制操作记录表';
```

**项目人员关联表（pm_project_personnel）**
```sql
CREATE TABLE pm_project_personnel (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '关联ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    user_name VARCHAR(100) NOT NULL COMMENT '用户姓名',
    role_type VARCHAR(50) NOT NULL COMMENT '角色类型(INSPECTOR/SUPERVISOR/IMPLEMENTER)',
    assignment_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',
    is_active TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否有效',
    
    FOREIGN KEY (project_id) REFERENCES pm_project(id),
    FOREIGN KEY (user_id) REFERENCES sys_user(id),
    UNIQUE KEY uk_project_user_role (project_id, user_id, role_type),
    INDEX idx_project_id (project_id),
    INDEX idx_user_id (user_id)
) COMMENT='项目人员关联表';
```

#### 3.1.3 流程控制相关表

**时间节点表（pm_project_timeline）**
```sql
CREATE TABLE pm_project_timeline (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '节点ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    node_type VARCHAR(50) NOT NULL COMMENT '节点类型',
    node_name VARCHAR(100) NOT NULL COMMENT '节点名称',
    planned_date DATE COMMENT '计划日期',
    actual_date DATE COMMENT '实际日期',
    is_completed TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否完成',
    completion_note VARCHAR(500) COMMENT '完成说明',
    operator_id BIGINT COMMENT '操作人ID',
    operator_name VARCHAR(100) COMMENT '操作人姓名',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (project_id) REFERENCES pm_project(id),
    INDEX idx_project_id (project_id),
    INDEX idx_node_type (node_type),
    INDEX idx_planned_date (planned_date),
    INDEX idx_actual_date (actual_date)
) COMMENT='项目时间节点表';
```

**工作日历表（sys_work_calendar）**
```sql
CREATE TABLE sys_work_calendar (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日历ID',
    calendar_date DATE NOT NULL UNIQUE COMMENT '日期',
    is_workday TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否工作日',
    holiday_type VARCHAR(50) COMMENT '节假日类型',
    holiday_name VARCHAR(100) COMMENT '节假日名称',
    year INT NOT NULL COMMENT '年份',
    month INT NOT NULL COMMENT '月份',
    day_of_week INT NOT NULL COMMENT '星期几(1-7)',
    
    INDEX idx_calendar_date (calendar_date),
    INDEX idx_year_month (year, month),
    INDEX idx_is_workday (is_workday)
) COMMENT='工作日历表';
```

**预警配置表（sys_alert_config）**
```sql
CREATE TABLE sys_alert_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '配置ID',
    alert_type VARCHAR(50) NOT NULL COMMENT '预警类型',
    alert_name VARCHAR(100) NOT NULL COMMENT '预警名称',
    business_type VARCHAR(50) NOT NULL COMMENT '业务类型',
    trigger_condition TEXT NOT NULL COMMENT '触发条件(JSON格式)',
    advance_days INT NOT NULL DEFAULT 0 COMMENT '提前预警天数',
    alert_level VARCHAR(20) NOT NULL COMMENT '预警级别(INFO/WARN/ERROR/URGENT)',
    is_active TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
    notification_methods VARCHAR(100) COMMENT '通知方式(EMAIL/SMS/SYSTEM)',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_alert_type (alert_type),
    INDEX idx_business_type (business_type),
    INDEX idx_is_active (is_active)
) COMMENT='预警配置表';
```

#### 3.1.4 财务管理相关表

**付款计划表（fm_payment_plan）**
```sql
CREATE TABLE fm_payment_plan (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '计划ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    contract_id BIGINT COMMENT '合同ID',
    plan_sequence INT NOT NULL COMMENT '计划序号',
    plan_amount DECIMAL(15,2) NOT NULL COMMENT '计划付款金额',
    plan_date DATE NOT NULL COMMENT '计划付款日期',
    payment_condition TEXT COMMENT '付款条件',
    plan_status VARCHAR(20) NOT NULL DEFAULT 'PLANNED' COMMENT '计划状态',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (project_id) REFERENCES pm_project(id),
    INDEX idx_project_id (project_id),
    INDEX idx_plan_date (plan_date),
    INDEX idx_plan_status (plan_status)
) COMMENT='付款计划表';
```

**付款记录表（fm_payment_record）**
```sql
CREATE TABLE fm_payment_record (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '记录ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    plan_id BIGINT COMMENT '计划ID',
    payment_amount DECIMAL(15,2) NOT NULL COMMENT '实际付款金额',
    payment_date DATE NOT NULL COMMENT '实际付款日期',
    payment_method VARCHAR(50) COMMENT '付款方式',
    payment_note VARCHAR(500) COMMENT '付款说明',
    approver_id BIGINT COMMENT '审批人ID',
    approver_name VARCHAR(100) COMMENT '审批人姓名',
    approval_time DATETIME COMMENT '审批时间',
    payment_status VARCHAR(20) NOT NULL DEFAULT 'PENDING' COMMENT '付款状态',
    voucher_number VARCHAR(100) COMMENT '凭证号',
    created_by BIGINT NOT NULL COMMENT '创建人',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (project_id) REFERENCES pm_project(id),
    FOREIGN KEY (plan_id) REFERENCES fm_payment_plan(id),
    INDEX idx_project_id (project_id),
    INDEX idx_payment_date (payment_date),
    INDEX idx_payment_status (payment_status)
) COMMENT='付款记录表';
```

**预算管理表（fm_budget）**
```sql
CREATE TABLE fm_budget (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '预算ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    budget_type VARCHAR(50) NOT NULL COMMENT '预算类型',
    budget_source VARCHAR(100) COMMENT '预算来源',
    budget_subject VARCHAR(100) COMMENT '预算科目',
    original_amount DECIMAL(15,2) NOT NULL COMMENT '原始预算金额',
    adjusted_amount DECIMAL(15,2) NOT NULL COMMENT '调整后预算金额',
    used_amount DECIMAL(15,2) NOT NULL DEFAULT 0 COMMENT '已使用金额',
    remaining_amount DECIMAL(15,2) NOT NULL COMMENT '剩余金额',
    budget_year INT NOT NULL COMMENT '预算年度',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (project_id) REFERENCES pm_project(id),
    INDEX idx_project_id (project_id),
    INDEX idx_budget_year (budget_year),
    INDEX idx_budget_type (budget_type)
) COMMENT='预算管理表';
```

#### 3.1.5 合同管理相关表

**合同信息表（cm_contract）**
```sql
CREATE TABLE cm_contract (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '合同ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    contract_name VARCHAR(255) NOT NULL COMMENT '合同名称',
    contract_number VARCHAR(100) NOT NULL UNIQUE COMMENT '合同编号',
    contract_amount DECIMAL(15,2) NOT NULL COMMENT '合同金额',
    signing_date DATE COMMENT '合同签订日期',
    performance_period VARCHAR(100) COMMENT '合同履行期限',
    start_date DATE COMMENT '履行开始日期',
    end_date DATE COMMENT '履行结束日期',
    supplier_id BIGINT NOT NULL COMMENT '供应商ID',
    contract_status VARCHAR(20) NOT NULL DEFAULT 'DRAFT' COMMENT '合同状态',
    legal_opinion_number VARCHAR(100) COMMENT '法律意见书编号',
    legal_review_date DATE COMMENT '法律审核日期',
    contract_file_path VARCHAR(500) COMMENT '合同文件路径',
    created_by BIGINT NOT NULL COMMENT '创建人',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (project_id) REFERENCES pm_project(id),
    UNIQUE KEY uk_project_id (project_id),
    INDEX idx_contract_number (contract_number),
    INDEX idx_signing_date (signing_date),
    INDEX idx_supplier_id (supplier_id)
) COMMENT='合同信息表';
```

**合同履行记录表（cm_contract_performance）**
```sql
CREATE TABLE cm_contract_performance (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '履行记录ID',
    contract_id BIGINT NOT NULL COMMENT '合同ID',
    performance_date DATE NOT NULL COMMENT '履行日期',
    performance_content TEXT NOT NULL COMMENT '履行内容',
    performance_status VARCHAR(50) NOT NULL COMMENT '履行状态',
    quality_assessment VARCHAR(20) COMMENT '质量评价',
    progress_percentage DECIMAL(5,2) COMMENT '完成进度百分比',
    supervisor_id BIGINT NOT NULL COMMENT '监督人员ID',
    supervisor_name VARCHAR(100) NOT NULL COMMENT '监督人员姓名',
    performance_note TEXT COMMENT '履行备注',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (contract_id) REFERENCES cm_contract(id),
    INDEX idx_contract_id (contract_id),
    INDEX idx_performance_date (performance_date),
    INDEX idx_supervisor_id (supervisor_id)
) COMMENT='合同履行记录表';
```

#### 3.1.6 供应商管理相关表

**供应商信息表（sm_supplier）**
```sql
CREATE TABLE sm_supplier (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '供应商ID',
    supplier_name VARCHAR(255) NOT NULL COMMENT '供应商名称',
    unified_social_credit_code VARCHAR(50) UNIQUE COMMENT '统一社会信用代码',
    legal_representative VARCHAR(100) COMMENT '法定代表人',
    registered_capital DECIMAL(15,2) COMMENT '注册资本',
    establishment_date DATE COMMENT '成立日期',
    business_scope TEXT COMMENT '经营范围',
    contact_address VARCHAR(500) COMMENT '联系地址',
    contact_person VARCHAR(100) COMMENT '联系人',
    contact_phone VARCHAR(20) COMMENT '联系电话',
    contact_email VARCHAR(100) COMMENT '联系邮箱',
    supplier_type VARCHAR(50) COMMENT '供应商类型',
    supplier_level VARCHAR(20) COMMENT '供应商等级',
    is_blacklisted TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否黑名单',
    blacklist_reason TEXT COMMENT '黑名单原因',
    is_active TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_supplier_name (supplier_name),
    INDEX idx_unified_social_credit_code (unified_social_credit_code),
    INDEX idx_supplier_type (supplier_type),
    INDEX idx_is_active (is_active)
) COMMENT='供应商信息表';
```

**供应商报价表（sm_supplier_quotation）**
```sql
CREATE TABLE sm_supplier_quotation (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '报价ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    supplier_id BIGINT NOT NULL COMMENT '供应商ID',
    quotation_amount DECIMAL(15,2) NOT NULL COMMENT '报价金额',
    quotation_date DATE NOT NULL COMMENT '报价日期',
    quotation_valid_until DATE COMMENT '报价有效期',
    technical_specification TEXT COMMENT '技术规格说明',
    service_commitment TEXT COMMENT '服务承诺',
    delivery_period VARCHAR(100) COMMENT '交货期',
    payment_terms TEXT COMMENT '付款条件',
    quotation_rank INT COMMENT '报价排名',
    is_selected TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否中选',
    quotation_note TEXT COMMENT '报价备注',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (project_id) REFERENCES pm_project(id),
    FOREIGN KEY (supplier_id) REFERENCES sm_supplier(id),
    INDEX idx_project_id (project_id),
    INDEX idx_supplier_id (supplier_id),
    INDEX idx_quotation_date (quotation_date)
) COMMENT='供应商报价表';
```

### 3.2 数据库约束与触发器设计

为确保数据完整性和业务规则的严格执行，系统设计了完善的数据库约束和触发器。

**付款金额一致性约束触发器：**
```sql
DELIMITER $$
CREATE TRIGGER tr_payment_amount_check 
BEFORE INSERT ON fm_payment_record
FOR EACH ROW
BEGIN
    DECLARE total_paid DECIMAL(15,2) DEFAULT 0;
    DECLARE contract_amount DECIMAL(15,2) DEFAULT 0;
    
    -- 获取合同金额
    SELECT p.transaction_amount INTO contract_amount
    FROM pm_project p
    WHERE p.id = NEW.project_id;
    
    -- 计算已付金额
    SELECT COALESCE(SUM(payment_amount), 0) INTO total_paid
    FROM fm_payment_record
    WHERE project_id = NEW.project_id AND payment_status = 'COMPLETED';
    
    -- 检查付款总额不能超过合同金额
    IF (total_paid + NEW.payment_amount) > contract_amount THEN
        SIGNAL SQLSTATE '45000' 
        SET MESSAGE_TEXT = '付款总额不能超过合同金额';
    END IF;
END$$
DELIMITER ;
```

**项目状态变更记录触发器：**
```sql
DELIMITER $$
CREATE TRIGGER tr_project_status_log
AFTER UPDATE ON pm_project
FOR EACH ROW
BEGIN
    IF OLD.project_status != NEW.project_status THEN
        INSERT INTO pm_project_status_log (
            project_id, old_status, new_status, 
            operator_id, operator_name, change_time
        ) VALUES (
            NEW.id, OLD.project_status, NEW.project_status,
            NEW.updated_by, 
            (SELECT real_name FROM sys_user WHERE id = NEW.updated_by),
            NOW()
        );
    END IF;
END$$
DELIMITER ;
```

### 3.3 数据字典设计

系统建立统一的数据字典管理机制，确保枚举值的一致性和可维护性。

**数据字典表（sys_dictionary）**
```sql
CREATE TABLE sys_dictionary (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '字典ID',
    dict_type VARCHAR(50) NOT NULL COMMENT '字典类型',
    dict_code VARCHAR(50) NOT NULL COMMENT '字典编码',
    dict_value VARCHAR(200) NOT NULL COMMENT '字典值',
    dict_label VARCHAR(200) NOT NULL COMMENT '字典标签',
    sort_order INT NOT NULL DEFAULT 0 COMMENT '排序',
    is_active TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
    parent_code VARCHAR(50) COMMENT '父级编码',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    UNIQUE KEY uk_dict_type_code (dict_type, dict_code),
    INDEX idx_dict_type (dict_type),
    INDEX idx_parent_code (parent_code)
) COMMENT='数据字典表';
```

**关键数据字典初始化数据：**
```sql
-- 项目类型
INSERT INTO sys_dictionary (dict_type, dict_code, dict_value, dict_label, sort_order) VALUES
('PROJECT_TYPE', 'GOODS', '货物', '货物', 1),
('PROJECT_TYPE', 'SERVICE', '服务', '服务', 2),
('PROJECT_TYPE', 'ENGINEERING', '工程', '工程', 3);

-- 采购方式
INSERT INTO sys_dictionary (dict_type, dict_code, dict_value, dict_label, sort_order) VALUES
('PROCUREMENT_METHOD', 'PUBLIC_TENDER', '公开招标', '公开招标', 1),
('PROCUREMENT_METHOD', 'INVITE_TENDER', '邀请招标', '邀请招标', 2),
('PROCUREMENT_METHOD', 'COMPETITIVE_NEGOTIATION', '竞争性磋商', '竞争性磋商', 3),
('PROCUREMENT_METHOD', 'INQUIRY', '询价', '询价', 4),
('PROCUREMENT_METHOD', 'SINGLE_SOURCE', '单一来源', '单一来源', 5);

-- 项目状态
INSERT INTO sys_dictionary (dict_type, dict_code, dict_value, dict_label, sort_order) VALUES
('PROJECT_STATUS', 'CREATED', '已创建', '已创建', 1),
('PROJECT_STATUS', 'APPROVED', '审核通过', '审核通过', 2),
('PROJECT_STATUS', 'IMPLEMENTING', '采购实施', '采购实施', 3),
('PROJECT_STATUS', 'CONTRACTED', '合同签订', '合同签订', 4),
('PROJECT_STATUS', 'PERFORMING', '履约监督', '履约监督', 5),
('PROJECT_STATUS', 'ACCEPTED', '验收完成', '验收完成', 6),
('PROJECT_STATUS', 'COMPLETED', '项目完成', '项目完成', 7);
```

## 4. 业务逻辑详细设计

### 4.1 项目管理模块详细设计

项目管理模块是整个系统的核心枢纽，负责采购项目从立项到完成的全生命周期管理。

#### 4.1.1 项目实体类设计

**Project实体类：**
```java
@Entity
@Table(name = "pm_project")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Project {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "project_name", nullable = false, length = 255)
    @NotBlank(message = "项目名称不能为空")
    private String projectName;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "project_type", nullable = false)
    @NotNull(message = "项目类型不能为空")
    private ProjectType projectType;
    
    @Column(name = "budget_amount", nullable = false, precision = 15, scale = 2)
    @NotNull(message = "预算金额不能为空")
    @DecimalMin(value = "0.01", message = "预算金额必须大于0")
    private BigDecimal budgetAmount;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "procurement_method", nullable = false)
    @NotNull(message = "采购方式不能为空")
    private ProcurementMethod procurementMethod;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "organization_form", nullable = false)
    @NotNull(message = "组织形式不能为空")
    private OrganizationForm organizationForm;
    
    @Column(name = "is_government_procurement", nullable = false)
    private Boolean isGovernmentProcurement = false;
    
    @Column(name = "is_classified", nullable = false)
    private Boolean isClassified = false;
    
    @Column(name = "transaction_amount", precision = 15, scale = 2)
    private BigDecimal transactionAmount;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "project_status", nullable = false)
    private ProjectStatus projectStatus = ProjectStatus.CREATED;
    
    @Column(name = "demand_department", nullable = false, length = 100)
    @NotBlank(message = "需求部门不能为空")
    private String demandDepartment;
    
    @Column(name = "procurement_department", nullable = false, length = 100)
    @NotBlank(message = "采购实施部门不能为空")
    private String procurementDepartment;
    
    // 审计字段
    @Column(name = "created_by", nullable = false)
    private Long createdBy;
    
    @CreationTimestamp
    @Column(name = "created_time", nullable = false)
    private LocalDateTime createdTime;
    
    @Column(name = "updated_by")
    private Long updatedBy;
    
    @UpdateTimestamp
    @Column(name = "updated_time")
    private LocalDateTime updatedTime;
    
    @Version
    @Column(name = "version", nullable = false)
    private Integer version = 1;
    
    @Column(name = "is_deleted", nullable = false)
    private Boolean isDeleted = false;
    
    // 关联关系
    @OneToMany(mappedBy = "project", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<ProjectPersonnel> projectPersonnel = new ArrayList<>();
    
    @OneToMany(mappedBy = "project", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<ProjectTimeline> timelines = new ArrayList<>();
    
    @OneToOne(mappedBy = "project", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Contract contract;
}
```

#### 4.1.2 项目服务类设计

**ProjectService接口定义：**
```java
public interface ProjectService {
    
    /**
     * 创建新项目
     * @param projectDTO 项目信息
     * @return 创建的项目
     */
    ProjectVO createProject(ProjectCreateDTO projectDTO);
    
    /**
     * 更新项目信息
     * @param id 项目ID
     * @param projectDTO 更新信息
     * @return 更新后的项目
     */
    ProjectVO updateProject(Long id, ProjectUpdateDTO projectDTO);
    
    /**
     * 变更项目状态
     * @param id 项目ID
     * @param newStatus 新状态
     * @param reason 变更原因
     * @return 更新后的项目
     */
    ProjectVO changeProjectStatus(Long id, ProjectStatus newStatus, String reason);
    
    /**
     * 分页查询项目
     * @param queryDTO 查询条件
     * @param pageable 分页参数
     * @return 项目列表
     */
    Page<ProjectVO> queryProjects(ProjectQueryDTO queryDTO, Pageable pageable);
    
    /**
     * 获取项目详情
     * @param id 项目ID
     * @return 项目详情
     */
    ProjectDetailVO getProjectDetail(Long id);
    
    /**
     * 删除项目（逻辑删除）
     * @param id 项目ID
     */
    void deleteProject(Long id);
    
    /**
     * 批量导入项目
     * @param file Excel文件
     * @return 导入结果
     */
    ImportResultVO batchImportProjects(MultipartFile file);
}
```

**ProjectServiceImpl实现类：**
```java
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class ProjectServiceImpl implements ProjectService {
    
    @Autowired
    private ProjectRepository projectRepository;
    
    @Autowired
    private ProjectStatusLogRepository statusLogRepository;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private ProjectMapper projectMapper;
    
    @Override
    public ProjectVO createProject(ProjectCreateDTO projectDTO) {
        log.info("开始创建项目: {}", projectDTO.getProjectName());
        
        // 1. 参数验证
        validateProjectCreateDTO(projectDTO);
        
        // 2. 检查项目名称重复
        if (projectRepository.existsByProjectNameAndIsDeletedFalse(projectDTO.getProjectName())) {
            throw new BusinessException(ErrorCode.PROJECT_NAME_DUPLICATE);
        }
        
        // 3. 转换DTO为实体
        Project project = projectMapper.toEntity(projectDTO);
        project.setCreatedBy(SecurityUtils.getCurrentUserId());
        
        // 4. 涉密项目特殊处理
        if (project.getIsClassified()) {
            validateClassifiedProjectPermission();
        }
        
        // 5. 保存项目
        Project savedProject = projectRepository.save(project);
        
        // 6. 记录状态变更日志
        recordStatusChange(savedProject.getId(), null, ProjectStatus.CREATED, "项目创建");
        
        log.info("项目创建成功，ID: {}", savedProject.getId());
        return projectMapper.toVO(savedProject);
    }
    
    @Override
    public ProjectVO changeProjectStatus(Long id, ProjectStatus newStatus, String reason) {
        log.info("开始变更项目状态，项目ID: {}, 新状态: {}", id, newStatus);
        
        // 1. 获取项目
        Project project = getProjectById(id);
        ProjectStatus oldStatus = project.getProjectStatus();
        
        // 2. 验证状态转换是否合法
        validateStatusTransition(oldStatus, newStatus);
        
        // 3. 涉密项目权限检查
        if (project.getIsClassified()) {
            validateClassifiedProjectPermission();
        }
        
        // 4. 特殊状态变更业务逻辑
        handleSpecialStatusChange(project, newStatus);
        
        // 5. 更新状态
        project.setProjectStatus(newStatus);
        project.setUpdatedBy(SecurityUtils.getCurrentUserId());
        Project savedProject = projectRepository.save(project);
        
        // 6. 记录状态变更日志
        recordStatusChange(id, oldStatus, newStatus, reason);
        
        log.info("项目状态变更成功，项目ID: {}, 从 {} 变更为 {}", id, oldStatus, newStatus);
        return projectMapper.toVO(savedProject);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<ProjectVO> queryProjects(ProjectQueryDTO queryDTO, Pageable pageable) {
        log.debug("开始查询项目列表，查询条件: {}", queryDTO);
        
        // 1. 构建查询条件
        Specification<Project> spec = buildQuerySpecification(queryDTO);
        
        // 2. 执行查询
        Page<Project> projectPage = projectRepository.findAll(spec, pageable);
        
        // 3. 转换为VO
        return projectPage.map(projectMapper::toVO);
    }
    
    /**
     * 验证项目创建DTO
     */
    private void validateProjectCreateDTO(ProjectCreateDTO projectDTO) {
        // 预算金额验证
        if (projectDTO.getBudgetAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException(ErrorCode.INVALID_BUDGET_AMOUNT);
        }
        
        // 政府采购与涉密项目的组合验证
        if (projectDTO.getIsGovernmentProcurement() && projectDTO.getIsClassified()) {
            throw new BusinessException(ErrorCode.GOVERNMENT_CLASSIFIED_CONFLICT);
        }
    }
    
    /**
     * 验证状态转换是否合法
     */
    private void validateStatusTransition(ProjectStatus oldStatus, ProjectStatus newStatus) {
        Map<ProjectStatus, Set<ProjectStatus>> allowedTransitions = buildStatusTransitionMap();
        
        Set<ProjectStatus> allowedNext = allowedTransitions.get(oldStatus);
        if (allowedNext == null || !allowedNext.contains(newStatus)) {
            throw new BusinessException(ErrorCode.INVALID_STATUS_TRANSITION, 
                String.format("不能从状态 %s 转换到 %s", oldStatus, newStatus));
        }
    }
    
    /**
     * 构建状态转换规则映射
     */
    private Map<ProjectStatus, Set<ProjectStatus>> buildStatusTransitionMap() {
        Map<ProjectStatus, Set<ProjectStatus>> transitions = new HashMap<>();
        
        transitions.put(ProjectStatus.CREATED, 
            Set.of(ProjectStatus.APPROVED, ProjectStatus.CANCELLED));
        transitions.put(ProjectStatus.APPROVED, 
            Set.of(ProjectStatus.IMPLEMENTING, ProjectStatus.CANCELLED));
        transitions.put(ProjectStatus.IMPLEMENTING, 
            Set.of(ProjectStatus.CONTRACTED, ProjectStatus.CANCELLED));
        transitions.put(ProjectStatus.CONTRACTED, 
            Set.of(ProjectStatus.PERFORMING));
        transitions.put(ProjectStatus.PERFORMING, 
            Set.of(ProjectStatus.ACCEPTED));
        transitions.put(ProjectStatus.ACCEPTED, 
            Set.of(ProjectStatus.COMPLETED));
        
        return transitions;
    }
    
    /**
     * 处理特殊状态变更的业务逻辑
     */
    private void handleSpecialStatusChange(Project project, ProjectStatus newStatus) {
        switch (newStatus) {
            case IMPLEMENTING:
                // 进入采购实施状态时，检查必要条件
                validateImplementingConditions(project);
                break;
            case CONTRACTED:
                // 进入合同签订状态时，检查成交金额
                if (project.getTransactionAmount() == null) {
                    throw new BusinessException(ErrorCode.TRANSACTION_AMOUNT_REQUIRED);
                }
                break;
            case COMPLETED:
                // 项目完成时的检查
                validateCompletionConditions(project);
                break;
        }
    }
}
```

### 4.2 人员协同模块详细设计

人员协同模块负责处理双人制管理、多人验收等复杂的人员协同业务。

#### 4.2.1 双人制操作服务设计

**DualOperationService接口：**
```java
public interface DualOperationService {
    
    /**
     * 发起双人制操作
     * @param operationDTO 操作信息
     * @return 操作记录
     */
    DualOperationVO initiateDualOperation(DualOperationInitiateDTO operationDTO);
    
    /**
     * 确认双人制操作
     * @param operationId 操作ID
     * @param confirmDTO 确认信息
     * @return 操作结果
     */
    DualOperationVO confirmDualOperation(Long operationId, DualOperationConfirmDTO confirmDTO);
    
    /**
     * 查询待确认的双人制操作
     * @param userId 用户ID
     * @return 待确认操作列表
     */
    List<DualOperationVO> getPendingOperations(Long userId);
    
    /**
     * 检查操作是否需要双人制
     * @param operationType 操作类型
     * @param businessType 业务类型
     * @return 是否需要双人制
     */
    boolean isDualOperationRequired(String operationType, String businessType);
}
```

**DualOperationServiceImpl实现：**
```java
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class DualOperationServiceImpl implements DualOperationService {
    
    @Autowired
    private DualOperationRepository dualOperationRepository;
    
    @Autowired
    private DualOperationMapper dualOperationMapper;
    
    @Autowired
    private NotificationService notificationService;
    
    @Override
    public DualOperationVO initiateDualOperation(DualOperationInitiateDTO operationDTO) {
        log.info("发起双人制操作: {}", operationDTO.getOperationType());
        
        // 1. 验证操作发起者权限
        Long currentUserId = SecurityUtils.getCurrentUserId();
        validateOperationPermission(currentUserId, operationDTO);
        
        // 2. 创建双人制操作记录
        DualOperation operation = new DualOperation();
        operation.setOperationType(operationDTO.getOperationType());
        operation.setBusinessId(operationDTO.getBusinessId());
        operation.setBusinessType(operationDTO.getBusinessType());
        operation.setFirstOperatorId(currentUserId);
        operation.setFirstOperatorName(SecurityUtils.getCurrentUserName());
        operation.setFirstOperationTime(LocalDateTime.now());
        operation.setFirstOperationContent(operationDTO.getOperationContent());
        operation.setOperationStatus(DualOperationStatus.PENDING);
        
        // 3. 保存操作记录
        DualOperation savedOperation = dualOperationRepository.save(operation);
        
        // 4. 发送通知给可能的第二操作人
        notifyPotentialSecondOperators(savedOperation);
        
        log.info("双人制操作发起成功，操作ID: {}", savedOperation.getId());
        return dualOperationMapper.toVO(savedOperation);
    }
    
    @Override
    public DualOperationVO confirmDualOperation(Long operationId, DualOperationConfirmDTO confirmDTO) {
        log.info("确认双人制操作，操作ID: {}", operationId);
        
        // 1. 获取操作记录
        DualOperation operation = getDualOperationById(operationId);
        
        // 2. 验证操作状态
        if (operation.getOperationStatus() != DualOperationStatus.PENDING) {
            throw new BusinessException(ErrorCode.OPERATION_NOT_PENDING);
        }
        
        // 3. 验证第二操作人
        Long currentUserId = SecurityUtils.getCurrentUserId();
        validateSecondOperator(operation, currentUserId);
        
        // 4. 更新操作记录
        operation.setSecondOperatorId(currentUserId);
        operation.setSecondOperatorName(SecurityUtils.getCurrentUserName());
        operation.setSecondOperationTime(LocalDateTime.now());
        operation.setSecondOperationContent(confirmDTO.getOperationContent());
        operation.setOperationStatus(confirmDTO.getIsConfirmed() ? 
            DualOperationStatus.CONFIRMED : DualOperationStatus.REJECTED);
        operation.setCompleteTime(LocalDateTime.now());
        
        // 5. 保存更新
        DualOperation savedOperation = dualOperationRepository.save(operation);
        
        // 6. 如果确认成功，执行后续业务逻辑
        if (confirmDTO.getIsConfirmed()) {
            executeBusinessLogic(savedOperation);
        }
        
        // 7. 发送结果通知
        notificationService.sendOperationResult(savedOperation);
        
        log.info("双人制操作确认完成，操作ID: {}, 结果: {}", 
            operationId, operation.getOperationStatus());
        return dualOperationMapper.toVO(savedOperation);
    }
    
    /**
     * 验证第二操作人的有效性
     */
    private void validateSecondOperator(DualOperation operation, Long secondOperatorId) {
        // 1. 不能是同一个人
        if (operation.getFirstOperatorId().equals(secondOperatorId)) {
            throw new BusinessException(ErrorCode.SAME_OPERATOR_NOT_ALLOWED);
        }
        
        // 2. 检查第二操作人的权限
        if (!hasOperationPermission(secondOperatorId, operation.getOperationType())) {
            throw new BusinessException(ErrorCode.INSUFFICIENT_PERMISSION);
        }
        
        // 3. 检查时间间隔（防止串通）
        LocalDateTime now = LocalDateTime.now();
        long minutesBetween = ChronoUnit.MINUTES.between(operation.getFirstOperationTime(), now);
        if (minutesBetween < 5) { // 最少间隔5分钟
            throw new BusinessException(ErrorCode.OPERATION_TOO_CLOSE);
        }
    }
    
    /**
     * 执行双人制确认后的业务逻辑
     */
    private void executeBusinessLogic(DualOperation operation) {
        switch (operation.getOperationType()) {
            case "PROJECT_STATUS_CHANGE":
                handleProjectStatusChange(operation);
                break;
            case "PAYMENT_APPROVAL":
                handlePaymentApproval(operation);
                break;
            case "CONTRACT_SIGNING":
                handleContractSigning(operation);
                break;
            default:
                log.warn("未知的双人制操作类型: {}", operation.getOperationType());
        }
    }
}
```

### 4.3 流程控制模块详细设计

流程控制模块负责管理采购过程中的时间节点和预警机制。

#### 4.3.1 时间节点管理服务

**TimelineService接口：**
```java
public interface TimelineService {
    
    /**
     * 创建项目时间节点
     * @param projectId 项目ID
     * @param timelineDTO 时间节点信息
     * @return 创建的时间节点
     */
    TimelineVO createTimeline(Long projectId, TimelineCreateDTO timelineDTO);
    
    /**
     * 更新时间节点
     * @param timelineId 时间节点ID
     * @param timelineDTO 更新信息
     * @return 更新后的时间节点
     */
    TimelineVO updateTimeline(Long timelineId, TimelineUpdateDTO timelineDTO);
    
    /**
     * 完成时间节点
     * @param timelineId 时间节点ID
     * @param completionNote 完成说明
     * @return 更新后的时间节点
     */
    TimelineVO completeTimeline(Long timelineId, String completionNote);
    
    /**
     * 获取项目时间线
     * @param projectId 项目ID
     * @return 时间节点列表
     */
    List<TimelineVO> getProjectTimeline(Long projectId);
    
    /**
     * 计算付款期限
     * @param contractSignDate 合同签订日期
     * @param paymentTermDays 付款期限天数
     * @return 付款截止日期
     */
    LocalDate calculatePaymentDeadline(LocalDate contractSignDate, int paymentTermDays);
    
    /**
     * 检查超期项目
     * @return 超期项目列表
     */
    List<OverdueProjectVO> checkOverdueProjects();
}
```

**付款期限计算的核心算法：**
```java
@Component
@Slf4j
public class PaymentDeadlineCalculator {
    
    @Autowired
    private WorkCalendarRepository workCalendarRepository;
    
    /**
     * 计算付款期限（基于工作日）
     * 按照《保障中小企业款项支付条例》要求
     */
    public LocalDate calculatePaymentDeadline(LocalDate startDate, int workDays) {
        log.debug("计算付款期限，起始日期: {}, 工作日天数: {}", startDate, workDays);
        
        LocalDate currentDate = startDate;
        int remainingDays = workDays;
        
        // 从起始日期的下一个工作日开始计算
        currentDate = getNextWorkday(currentDate);
        
        while (remainingDays > 0) {
            if (isWorkday(currentDate)) {
                remainingDays--;
            }
            if (remainingDays > 0) {
                currentDate = currentDate.plusDays(1);
            }
        }
        
        LocalDate deadline = currentDate;
        log.debug("计算完成，付款期限: {}", deadline);
        return deadline;
    }
    
    /**
     * 判断是否为工作日
     */
    private boolean isWorkday(LocalDate date) {
        Optional<WorkCalendar> calendar = workCalendarRepository.findByCalendarDate(date);
        
        if (calendar.isPresent()) {
            return calendar.get().getIsWorkday();
        }
        
        // 如果日历中没有数据，按照默认规则判断（周一到周五为工作日）
        DayOfWeek dayOfWeek = date.getDayOfWeek();
        return dayOfWeek != DayOfWeek.SATURDAY && dayOfWeek != DayOfWeek.SUNDAY;
    }
    
    /**
     * 获取下一个工作日
     */
    private LocalDate getNextWorkday(LocalDate date) {
        LocalDate nextDate = date.plusDays(1);
        while (!isWorkday(nextDate)) {
            nextDate = nextDate.plusDays(1);
        }
        return nextDate;
    }
    
    /**
     * 计算两个日期之间的工作日天数
     */
    public int calculateWorkdaysBetween(LocalDate startDate, LocalDate endDate) {
        if (startDate.isAfter(endDate)) {
            return 0;
        }
        
        int workdays = 0;
        LocalDate currentDate = startDate;
        
        while (!currentDate.isAfter(endDate)) {
            if (isWorkday(currentDate)) {
                workdays++;
            }
            currentDate = currentDate.plusDays(1);
        }
        
        return workdays;
    }
}
```

#### 4.3.2 预警机制设计

**AlertService预警服务：**
```java
@Service
@Slf4j
public class AlertServiceImpl implements AlertService {
    
    @Autowired
    private AlertConfigRepository alertConfigRepository;
    
    @Autowired
    private AlertRecordRepository alertRecordRepository;
    
    @Autowired
    private ProjectRepository projectRepository;
    
    @Autowired
    private PaymentDeadlineCalculator deadlineCalculator;
    
    @Autowired
    private NotificationService notificationService;
    
    /**
     * 检查并生成预警
     * 定时任务每天执行
     */
    @Scheduled(cron = "0 0 8 * * ?") // 每天早上8点执行
    public void checkAndGenerateAlerts() {
        log.info("开始执行预警检查任务");
        
        try {
            // 1. 检查付款期限预警
            checkPaymentDeadlineAlerts();
            
            // 2. 检查合同履行期限预警
            checkContractPerformanceAlerts();
            
            // 3. 检查项目进度预警
            checkProjectProgressAlerts();
            
            // 4. 检查资质到期预警
            checkQualificationExpiryAlerts();
            
            log.info("预警检查任务执行完成");
        } catch (Exception e) {
            log.error("预警检查任务执行失败", e);
        }
    }
    
    /**
     * 检查付款期限预警
     */
    private void checkPaymentDeadlineAlerts() {
        log.debug("开始检查付款期限预警");
        
        // 获取所有未完成付款的项目
        List<Project> unpaidProjects = projectRepository.findUnpaidProjects();
        
        for (Project project : unpaidProjects) {
            try {
                checkProjectPaymentAlert(project);
            } catch (Exception e) {
                log.error("检查项目付款预警失败，项目ID: {}", project.getId(), e);
            }
        }
    }
    
    /**
     * 检查单个项目的付款预警
     */
    private void checkProjectPaymentAlert(Project project) {
        // 获取合同信息
        Contract contract = project.getContract();
        if (contract == null || contract.getSigningDate() == null) {
            return;
        }
        
        // 计算付款截止日期
        LocalDate paymentDeadline = deadlineCalculator.calculatePaymentDeadline(
            contract.getSigningDate(), getPaymentTermDays(project));
        
        LocalDate today = LocalDate.now();
        long daysUntilDeadline = ChronoUnit.DAYS.between(today, paymentDeadline);
        
        // 根据剩余天数确定预警级别
        AlertLevel alertLevel = determineAlertLevel(daysUntilDeadline);
        
        if (alertLevel != null) {
            generatePaymentAlert(project, alertLevel, daysUntilDeadline, paymentDeadline);
        }
    }
    
    /**
     * 确定预警级别
     */
    private AlertLevel determineAlertLevel(long daysUntilDeadline) {
        if (daysUntilDeadline < 0) {
            return AlertLevel.URGENT; // 已超期
        } else if (daysUntilDeadline <= 3) {
            return AlertLevel.ERROR; // 3天内到期
        } else if (daysUntilDeadline <= 7) {
            return AlertLevel.WARN; // 7天内到期
        } else if (daysUntilDeadline <= 15) {
            return AlertLevel.INFO; // 15天内到期
        }
        return null; // 不需要预警
    }
    
    /**
     * 生成付款预警
     */
    private void generatePaymentAlert(Project project, AlertLevel alertLevel, 
                                    long daysUntilDeadline, LocalDate paymentDeadline) {
        
        // 检查是否已经发送过相同级别的预警
        boolean alreadyAlerted = alertRecordRepository.existsByProjectIdAndAlertTypeAndAlertLevel(
            project.getId(), AlertType.PAYMENT_DEADLINE, alertLevel);
        
        if (alreadyAlerted) {
            return;
        }
        
        // 创建预警记录
        AlertRecord alertRecord = new AlertRecord();
        alertRecord.setProjectId(project.getId());
        alertRecord.setAlertType(AlertType.PAYMENT_DEADLINE);
        alertRecord.setAlertLevel(alertLevel);
        alertRecord.setAlertTitle(buildPaymentAlertTitle(alertLevel, daysUntilDeadline));
        alertRecord.setAlertContent(buildPaymentAlertContent(project, paymentDeadline, daysUntilDeadline));
        alertRecord.setAlertTime(LocalDateTime.now());
        alertRecord.setIsHandled(false);
        
        AlertRecord savedAlert = alertRecordRepository.save(alertRecord);
        
        // 发送通知
        sendAlertNotification(savedAlert, project);
        
        log.info("生成付款预警成功，项目: {}, 预警级别: {}, 剩余天数: {}", 
            project.getProjectName(), alertLevel, daysUntilDeadline);
    }
    
    /**
     * 构建预警标题
     */
    private String buildPaymentAlertTitle(AlertLevel alertLevel, long daysUntilDeadline) {
        switch (alertLevel) {
            case URGENT:
                return String.format("付款超期预警（已超期%d天）", Math.abs(daysUntilDeadline));
            case ERROR:
                return String.format("付款紧急预警（%d天内到期）", daysUntilDeadline);
            case WARN:
                return String.format("付款临近预警（%d天内到期）", daysUntilDeadline);
            case INFO:
                return String.format("付款提醒（%d天内到期）", daysUntilDeadline);
            default:
                return "付款期限预警";
        }
    }
    
    /**
     * 发送预警通知
     */
    private void sendAlertNotification(AlertRecord alertRecord, Project project) {
        // 确定通知接收人
        List<Long> recipientIds = determineAlertRecipients(project, alertRecord.getAlertType());
        
        // 构建通知内容
        NotificationDTO notification = new NotificationDTO();
        notification.setTitle(alertRecord.getAlertTitle());
        notification.setContent(alertRecord.getAlertContent());
        notification.setType(NotificationType.ALERT);
        notification.setLevel(alertRecord.getAlertLevel());
        notification.setRecipientIds(recipientIds);
        notification.setRelatedBusinessId(project.getId());
        notification.setRelatedBusinessType("PROJECT");
        
        // 发送通知
        notificationService.sendNotification(notification);
    }
}
```

## 5. 用户界面详细设计

### 5.1 前端架构设计

系统前端采用Vue.js 3.x + Element Plus的技术栈，遵循组件化开发模式，确保界面的一致性和可维护性。

#### 5.1.1 前端项目结构

```
src/
├── assets/              # 静态资源
│   ├── images/         # 图片文件
│   ├── styles/         # 样式文件
│   └── fonts/          # 字体文件
├── components/          # 公共组件
│   ├── common/         # 通用组件
│   ├── form/           # 表单组件
│   └── layout/         # 布局组件
├── views/              # 页面视图
│   ├── project/        # 项目管理页面
│   ├── personnel/      # 人员协同页面
│   ├── workflow/       # 流程控制页面
│   ├── finance/        # 财务管理页面
│   ├── contract/       # 合同管理页面
│   └── supplier/       # 供应商管理页面
├── router/             # 路由配置
├── store/              # 状态管理
├── api/                # API接口
├── utils/              # 工具函数
└── main.js             # 入口文件
```

#### 5.1.2 核心组件设计

**项目管理主界面组件（ProjectManagement.vue）：**
```vue
<template>
  <div class="project-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>项目管理</h2>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog">
          <el-icon><Plus /></el-icon>
          新建项目
        </el-button>
        <el-button @click="showImportDialog">
          <el-icon><Upload /></el-icon>
          批量导入
        </el-button>
        <el-button @click="exportProjects">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </div>
    </div>

    <!-- 查询条件 -->
    <el-card class="search-card">
      <ProjectSearchForm 
        @search="handleSearch" 
        @reset="handleReset"
        :loading="loading"
      />
    </el-card>

    <!-- 项目列表 -->
    <el-card class="table-card">
      <ProjectTable 
        :data="projectList"
        :loading="loading"
        :pagination="pagination"
        @page-change="handlePageChange"
        @edit="handleEdit"
        @delete="handleDelete"
        @view-detail="handleViewDetail"
        @status-change="handleStatusChange"
      />
    </el-card>

    <!-- 创建/编辑对话框 -->
    <ProjectFormDialog 
      v-model:visible="formDialogVisible"
      :form-data="currentProject"
      :mode="formMode"
      @save="handleSave"
    />

    <!-- 项目详情对话框 -->
    <ProjectDetailDialog 
      v-model:visible="detailDialogVisible"
      :project-id="currentProjectId"
    />

    <!-- 状态变更对话框 -->
    <StatusChangeDialog 
      v-model:visible="statusDialogVisible"
      :project="currentProject"
      @confirm="handleStatusChangeConfirm"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Upload, Download } from '@element-plus/icons-vue'
import ProjectSearchForm from './components/ProjectSearchForm.vue'
import ProjectTable from './components/ProjectTable.vue'
import ProjectFormDialog from './components/ProjectFormDialog.vue'
import ProjectDetailDialog from './components/ProjectDetailDialog.vue'
import StatusChangeDialog from './components/StatusChangeDialog.vue'
import { projectApi } from '@/api/project'

// 响应式数据
const loading = ref(false)
const projectList = ref([])
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 查询条件
const searchParams = reactive({
  projectName: '',
  projectType: '',
  procurementMethod: '',
  projectStatus: '',
  dateRange: []
})

// 对话框控制
const formDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const statusDialogVisible = ref(false)

// 当前操作的项目
const currentProject = ref({})
const currentProjectId = ref(null)
const formMode = ref('create') // create | edit

// 页面加载时获取数据
onMounted(() => {
  fetchProjectList()
})

/**
 * 获取项目列表
 */
const fetchProjectList = async () => {
  try {
    loading.value = true
    
    const params = {
      page: pagination.page - 1, // 后端从0开始
      size: pagination.size,
      ...searchParams
    }
    
    const response = await projectApi.getProjectList(params)
    
    projectList.value = response.data.content
    pagination.total = response.data.totalElements
    
  } catch (error) {
    ElMessage.error('获取项目列表失败')
    console.error('获取项目列表失败:', error)
  } finally {
    loading.value = false
  }
}

/**
 * 处理搜索
 */
const handleSearch = (searchForm) => {
  Object.assign(searchParams, searchForm)
  pagination.page = 1
  fetchProjectList()
}

/**
 * 处理重置
 */
const handleReset = () => {
  Object.keys(searchParams).forEach(key => {
    searchParams[key] = ''
  })
  searchParams.dateRange = []
  pagination.page = 1
  fetchProjectList()
}

/**
 * 处理分页变化
 */
const handlePageChange = (page, size) => {
  pagination.page = page
  pagination.size = size
  fetchProjectList()
}

/**
 * 显示创建项目对话框
 */
const showCreateDialog = () => {
  currentProject.value = {}
  formMode.value = 'create'
  formDialogVisible.value = true
}

/**
 * 处理编辑
 */
const handleEdit = (project) => {
  currentProject.value = { ...project }
  formMode.value = 'edit'
  formDialogVisible.value = true
}

/**
 * 处理保存
 */
const handleSave = async (projectData) => {
  try {
    if (formMode.value === 'create') {
      await projectApi.createProject(projectData)
      ElMessage.success('项目创建成功')
    } else {
      await projectApi.updateProject(currentProject.value.id, projectData)
      ElMessage.success('项目更新成功')
    }
    
    formDialogVisible.value = false
    fetchProjectList()
    
  } catch (error) {
    ElMessage.error('保存失败')
    console.error('保存项目失败:', error)
  }
}

/**
 * 处理删除
 */
const handleDelete = async (project) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除项目"${project.projectName}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await projectApi.deleteProject(project.id)
    ElMessage.success('删除成功')
    fetchProjectList()
    
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
      console.error('删除项目失败:', error)
    }
  }
}

/**
 * 查看项目详情
 */
const handleViewDetail = (project) => {
  currentProjectId.value = project.id
  detailDialogVisible.value = true
}

/**
 * 处理状态变更
 */
const handleStatusChange = (project) => {
  currentProject.value = project
  statusDialogVisible.value = true
}

/**
 * 确认状态变更
 */
const handleStatusChangeConfirm = async (statusData) => {
  try {
    await projectApi.changeProjectStatus(
      currentProject.value.id, 
      statusData.newStatus, 
      statusData.reason
    )
    
    ElMessage.success('状态变更成功')
    statusDialogVisible.value = false
    fetchProjectList()
    
  } catch (error) {
    ElMessage.error('状态变更失败')
    console.error('状态变更失败:', error)
  }
}

/**
 * 导出项目数据
 */
const exportProjects = async () => {
  try {
    const response = await projectApi.exportProjects(searchParams)
    
    // 处理文件下载
    const blob = new Blob([response.data])
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `项目数据_${new Date().toISOString().slice(0, 10)}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('导出成功')
    
  } catch (error) {
    ElMessage.error('导出失败')
    console.error('导出失败:', error)
  }
}
</script>

<style scoped>
.project-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.search-card,
.table-card {
  margin-bottom: 20px;
}

.table-card {
  min-height: 400px;
}
</style>
```

#### 5.1.3 双人制操作界面设计

**双人制操作确认组件（DualOperationConfirm.vue）：**
```vue
<template>
  <div class="dual-operation-confirm">
    <el-card class="operation-card">
      <template #header>
        <div class="card-header">
          <h3>双人制操作确认</h3>
          <el-tag :type="getStatusTagType(operation.operationStatus)">
            {{ getStatusText(operation.operationStatus) }}
          </el-tag>
        </div>
      </template>

      <!-- 操作基本信息 -->
      <div class="operation-info">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="操作类型">
            {{ getOperationTypeText(operation.operationType) }}
          </el-descriptions-item>
          <el-descriptions-item label="业务对象">
            {{ operation.businessType }}
          </el-descriptions-item>
          <el-descriptions-item label="第一操作人">
            {{ operation.firstOperatorName }}
          </el-descriptions-item>
          <el-descriptions-item label="操作时间">
            {{ formatDateTime(operation.firstOperationTime) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 第一次操作内容 -->
      <div class="operation-content">
        <h4>操作内容</h4>
        <el-card class="content-card">
          <div v-html="operation.firstOperationContent"></div>
        </el-card>
      </div>

      <!-- 确认操作区域 -->
      <div class="confirm-section" v-if="operation.operationStatus === 'PENDING'">
        <h4>第二人确认</h4>
        <el-form 
          ref="confirmFormRef" 
          :model="confirmForm" 
          :rules="confirmRules"
          label-width="120px"
        >
          <el-form-item label="确认意见" prop="operationContent">
            <el-input
              v-model="confirmForm.operationContent"
              type="textarea"
              :rows="4"
              placeholder="请填写确认意见..."
            />
          </el-form-item>
          
          <el-form-item label="确认结果" prop="isConfirmed">
            <el-radio-group v-model="confirmForm.isConfirmed">
              <el-radio :label="true">同意</el-radio>
              <el-radio :label="false">拒绝</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>

        <div class="confirm-actions">
          <el-button @click="handleCancel">取消</el-button>
          <el-button 
            type="primary" 
            @click="handleConfirm"
            :loading="confirming"
          >
            确认提交
          </el-button>
        </div>
      </div>

      <!-- 已完成的操作结果 -->
      <div class="result-section" v-else>
        <h4>操作结果</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="第二操作人">
            {{ operation.secondOperatorName }}
          </el-descriptions-item>
          <el-descriptions-item label="确认时间">
            {{ formatDateTime(operation.secondOperationTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="确认意见" :span="2">
            {{ operation.secondOperationContent }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { dualOperationApi } from '@/api/dualOperation'

const props = defineProps({
  operationId: {
    type: Number,
    required: true
  }
})

const emit = defineEmits(['confirmed', 'cancel'])

// 响应式数据
const operation = ref({})
const confirming = ref(false)
const confirmFormRef = ref()

// 确认表单
const confirmForm = reactive({
  operationContent: '',
  isConfirmed: true
})

// 表单验证规则
const confirmRules = {
  operationContent: [
    { required: true, message: '请填写确认意见', trigger: 'blur' },
    { min: 10, message: '确认意见至少10个字符', trigger: 'blur' }
  ],
  isConfirmed: [
    { required: true, message: '请选择确认结果', trigger: 'change' }
  ]
}

/**
 * 获取操作详情
 */
const fetchOperationDetail = async () => {
  try {
    const response = await dualOperationApi.getOperationDetail(props.operationId)
    operation.value = response.data
  } catch (error) {
    ElMessage.error('获取操作详情失败')
    console.error('获取操作详情失败:', error)
  }
}

/**
 * 处理确认操作
 */
const handleConfirm = async () => {
  try {
    // 表单验证
    const valid = await confirmFormRef.value.validate()
    if (!valid) return

    // 二次确认
    const action = confirmForm.isConfirmed ? '同意' : '拒绝'
    await ElMessageBox.confirm(
      `确定要${action}这个操作吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    confirming.value = true

    // 提交确认
    await dualOperationApi.confirmOperation(props.operationId, confirmForm)
    
    ElMessage.success('确认成功')
    emit('confirmed')

  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('确认失败')
      console.error('确认操作失败:', error)
    }
  } finally {
    confirming.value = false
  }
}

/**
 * 处理取消
 */
const handleCancel = () => {
  emit('cancel')
}

/**
 * 获取状态标签类型
 */
const getStatusTagType = (status) => {
  const typeMap = {
    'PENDING': 'warning',
    'CONFIRMED': 'success',
    'REJECTED': 'danger'
  }
  return typeMap[status] || 'info'
}

/**
 * 获取状态文本
 */
const getStatusText = (status) => {
  const textMap = {
    'PENDING': '待确认',
    'CONFIRMED': '已确认',
    'REJECTED': '已拒绝'
  }
  return textMap[status] || '未知状态'
}

/**
 * 获取操作类型文本
 */
const getOperationTypeText = (type) => {
  const textMap = {
    'PROJECT_STATUS_CHANGE': '项目状态变更',
    'PAYMENT_APPROVAL': '付款审批',
    'CONTRACT_SIGNING': '合同签订'
  }
  return textMap[type] || type
}

/**
 * 格式化日期时间
 */
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 组件挂载时获取数据
fetchOperationDetail()
</script>

<style scoped>
.dual-operation-confirm {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
}

.operation-info,
.operation-content,
.confirm-section,
.result-section {
  margin-bottom: 20px;
}

.operation-content h4,
.confirm-section h4,
.result-section h4 {
  margin-bottom: 10px;
  color: #606266;
  font-size: 16px;
}

.content-card {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
}

.confirm-actions {
  text-align: right;
  margin-top: 20px;
}

.confirm-actions .el-button {
  margin-left: 10px;
}
</style>
```

### 5.2 移动端适配设计

考虑到现代办公的移动化需求，系统设计了响应式布局，支持在平板和手机上进行基本的查看和审批操作。

#### 5.2.1 响应式布局设计

**全局响应式样式：**
```scss
// 响应式断点定义
$breakpoints: (
  'xs': 0,
  'sm': 576px,
  'md': 768px,
  'lg': 992px,
  'xl': 1200px,
  'xxl': 1600px
);

// 响应式混入
@mixin respond-to($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (min-width: map-get($breakpoints, $breakpoint)) {
      @content;
    }
  }
}

// 移动端优先的基础样式
.container {
  width: 100%;
  padding: 0 15px;
  margin: 0 auto;

  @include respond-to('sm') {
    max-width: 540px;
  }

  @include respond-to('md') {
    max-width: 720px;
  }

  @include respond-to('lg') {
    max-width: 960px;
  }

  @include respond-to('xl') {
    max-width: 1140px;
  }
}

// 表格响应式设计
.responsive-table {
  @include respond-to('md') {
    .el-table__cell {
      padding: 8px 10px;
    }
  }

  // 小屏幕下隐藏次要列
  @media (max-width: 768px) {
    .secondary-column {
      display: none;
    }
  }
}

// 表单响应式设计
.responsive-form {
  .el-form-item {
    @include respond-to('md') {
      margin-bottom: 18px;
    }
  }

  .el-form-item__label {
    @media (max-width: 768px) {
      width: 100px !important;
      text-align: left;
    }
  }
}

// 按钮组响应式设计
.responsive-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;

  @media (max-width: 576px) {
    flex-direction: column;
    
    .el-button {
      width: 100%;
    }
  }
}
```

**移动端项目列表组件：**
```vue
<template>
  <div class="mobile-project-list">
    <!-- 搜索栏 -->
    <div class="mobile-search">
      <el-input
        v-model="searchKeyword"
        placeholder="搜索项目名称..."
        clearable
        @input="handleSearch"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
      <el-button @click="showFilterDialog">
        <el-icon><Filter /></el-icon>
      </el-button>
    </div>

    <!-- 项目卡片列表 -->
    <div class="project-cards">
      <div 
        v-for="project in projectList" 
        :key="project.id"
        class="project-card"
        @click="viewProjectDetail(project)"
      >
        <div class="card-header">
          <h3 class="project-name">{{ project.projectName }}</h3>
          <el-tag 
            :type="getStatusTagType(project.projectStatus)"
            size="small"
          >
            {{ getStatusText(project.projectStatus) }}
          </el-tag>
        </div>
        
        <div class="card-content">
          <div class="info-row">
            <span class="label">项目类型：</span>
            <span class="value">{{ project.projectType }}</span>
          </div>
          <div class="info-row">
            <span class="label">预算金额：</span>
            <span class="value amount">{{ formatAmount(project.budgetAmount) }}</span>
          </div>
          <div class="info-row">
            <span class="label">需求部门：</span>
            <span class="value">{{ project.demandDepartment }}</span>
          </div>
        </div>

        <div class="card-actions">
          <el-button size="small" @click.stop="editProject(project)">编辑</el-button>
          <el-button 
            size="small" 
            type="primary" 
            @click.stop="viewTimeline(project)"
          >
            进度
          </el-button>
        </div>
      </div>
    </div>

    <!-- 加载更多 -->
    <div class="load-more" v-if="hasMore">
      <el-button 
        @click="loadMore" 
        :loading="loading"
        style="width: 100%"
      >
        加载更多
      </el-button>
    </div>

    <!-- 筛选对话框 -->
    <el-drawer v-model="filterVisible" title="筛选条件" size="80%">
      <MobileFilterForm 
        @filter="handleFilter"
        @reset="handleFilterReset"
      />
    </el-drawer>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { Search, Filter } from '@element-plus/icons-vue'
import MobileFilterForm from './MobileFilterForm.vue'

// 响应式数据
const projectList = ref([])
const loading = ref(false)
const hasMore = ref(true)
const searchKeyword = ref('')
const filterVisible = ref(false)

// 分页参数
const pagination = {
  page: 0,
  size: 20
}

/**
 * 处理搜索
 */
const handleSearch = () => {
  pagination.page = 0
  projectList.value = []
  fetchProjectList(true)
}

/**
 * 获取项目列表
 */
const fetchProjectList = async (reset = false) => {
  try {
    loading.value = true
    
    if (reset) {
      pagination.page = 0
      projectList.value = []
    }

    const params = {
      page: pagination.page,
      size: pagination.size,
      keyword: searchKeyword.value
    }

    const response = await projectApi.getProjectList(params)
    const newData = response.data.content

    if (reset) {
      projectList.value = newData
    } else {
      projectList.value.push(...newData)
    }

    hasMore.value = !response.data.last
    pagination.page++

  } catch (error) {
    console.error('获取项目列表失败:', error)
  } finally {
    loading.value = false
  }
}

/**
 * 加载更多
 */
const loadMore = () => {
  if (!loading.value && hasMore.value) {
    fetchProjectList()
  }
}

// 组件挂载时加载数据
onMounted(() => {
  fetchProjectList(true)
})
</script>

<style scoped>
.mobile-project-list {
  padding: 10px;
}

.mobile-search {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
  position: sticky;
  top: 0;
  background: white;
  z-index: 10;
  padding: 10px 0;
}

.project-cards {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.project-card {
  background: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e4e7ed;
  cursor: pointer;
  transition: all 0.3s ease;
}

.project-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.project-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0;
  flex: 1;
  margin-right: 10px;
  line-height: 1.4;
}

.card-content {
  margin-bottom: 15px;
}

.info-row {
  display: flex;
  margin-bottom: 8px;
  font-size: 14px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.label {
  color: #909399;
  min-width: 80px;
  flex-shrink: 0;
}

.value {
  color: #606266;
  flex: 1;
}

.value.amount {
  font-weight: 600;
  color: #E6A23C;
}

.card-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.load-more {
  margin-top: 20px;
  text-align: center;
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .project-card {
    background: #2d2d2d;
    border-color: #4d4d4d;
  }
  
  .project-name {
    color: #ffffff;
  }
  
  .label {
    color: #b4b4b4;
  }
  
  .value {
    color: #d4d4d4;
  }
}
</style>
```

## 6. 系统集成与部署设计

### 6.1 CI/CD流水线设计

系统采用现代化的持续集成和持续部署流程，确保代码质量和部署效率。

#### 6.1.1 Jenkins流水线配置

**Jenkinsfile配置：**
```groovy
pipeline {
    agent any
    
    environment {
        // 环境变量定义
        DOCKER_REGISTRY = 'your-registry.com'
        IMAGE_NAME = 'procurement-platform'
        SONAR_TOKEN = credentials('sonar-token')
        DB_CREDENTIALS = credentials('db-credentials')
    }
    
    stages {
        stage('代码检出') {
            steps {
                echo '正在检出代码...'
                checkout scm
                
                script {
                    // 获取版本信息
                    env.BUILD_VERSION = sh(
                        script: "git describe --tags --always",
                        returnStdout: true
                    ).trim()
                    
                    env.COMMIT_ID = sh(
                        script: "git rev-parse --short HEAD",
                        returnStdout: true
                    ).trim()
                }
                
                echo "构建版本: ${env.BUILD_VERSION}"
                echo "提交ID: ${env.COMMIT_ID}"
            }
        }
        
        stage('环境准备') {
            parallel {
                stage('后端环境') {
                    steps {
                        echo '准备后端构建环境...'
                        sh '''
                            cd backend
                            chmod +x mvnw
                            ./mvnw clean compile
                        '''
                    }
                }
                
                stage('前端环境') {
                    steps {
                        echo '准备前端构建环境...'
                        sh '''
                            cd frontend
                            npm install --registry=https://registry.npmmirror.com
                        '''
                    }
                }
            }
        }
        
        stage('代码质量检查') {
            parallel {
                stage('后端代码检查') {
                    steps {
                        echo '执行后端代码质量检查...'
                        sh '''
                            cd backend
                            
                            # 运行单元测试
                            ./mvnw test
                            
                            # SonarQube代码扫描
                            ./mvnw sonar:sonar \
                                -Dsonar.host.url=http://sonarqube:9000 \
                                -Dsonar.login=${SONAR_TOKEN}
                        '''
                    }
                    
                    post {
                        always {
                            // 发布测试报告
                            publishTestResults testResultsPattern: 'backend/target/surefire-reports/*.xml'
                            
                            // 发布代码覆盖率报告
                            publishCoverage adapters: [
                                jacocoAdapter('backend/target/site/jacoco/jacoco.xml')
                            ]
                        }
                    }
                }
                
                stage('前端代码检查') {
                    steps {
                        echo '执行前端代码质量检查...'
                        sh '''
                            cd frontend
                            
                            # ESLint代码检查
                            npm run lint
                            
                            # 运行单元测试
                            npm run test:unit
                        '''
                    }
                    
                    post {
                        always {
                            // 发布前端测试报告
                            publishTestResults testResultsPattern: 'frontend/test-results.xml'
                        }
                    }
                }
            }
        }
        
        stage('构建应用') {
            parallel {
                stage('构建后端') {
                    steps {
                        echo '构建后端应用...'
                        sh '''
                            cd backend
                            ./mvnw clean package -DskipTests
                        '''
                    }
                }
                
                stage('构建前端') {
                    steps {
                        echo '构建前端应用...'
                        sh '''
                            cd frontend
                            npm run build:prod
                        '''
                    }
                }
            }
        }
        
        stage('构建镜像') {
            steps {
                echo '构建Docker镜像...'
                script {
                    // 构建后端镜像
                    def backendImage = docker.build(
                        "${DOCKER_REGISTRY}/${IMAGE_NAME}-backend:${BUILD_VERSION}",
                        "-f backend/Dockerfile backend/"
                    )
                    
                    // 构建前端镜像
                    def frontendImage = docker.build(
                        "${DOCKER_REGISTRY}/${IMAGE_NAME}-frontend:${BUILD_VERSION}",
                        "-f frontend/Dockerfile frontend/"
                    )
                    
                    // 推送到镜像仓库
                    docker.withRegistry("https://${DOCKER_REGISTRY}", 'docker-registry-credentials') {
                        backendImage.push()
                        backendImage.push('latest')
                        
                        frontendImage.push()
                        frontendImage.push('latest')
                    }
                }
            }
        }
        
        stage('安全扫描') {
            steps {
                echo '执行安全扫描...'
                sh '''
                    # 依赖安全扫描
                    cd backend
                    ./mvnw org.owasp:dependency-check-maven:check
                    
                    # 镜像安全扫描
                    trivy image ${DOCKER_REGISTRY}/${IMAGE_NAME}-backend:${BUILD_VERSION}
                '''
            }
            
            post {
                always {
                    // 发布安全扫描报告
                    publishHTML([
                        allowMissing: false,
                        alwaysLinkToLastBuild: true,
                        keepAll: true,
                        reportDir: 'backend/target',
                        reportFiles: 'dependency-check-report.html',
                        reportName: '依赖安全扫描报告'
                    ])
                }
            }
        }
        
        stage('部署到测试环境') {
            when {
                branch 'develop'
            }
            steps {
                echo '部署到测试环境...'
                sh '''
                    # 更新测试环境配置
                    envsubst < deployment/test/docker-compose.yml.template > deployment/test/docker-compose.yml
                    
                    # 部署到测试环境
                    cd deployment/test
                    docker-compose down
                    docker-compose pull
                    docker-compose up -d
                    
                    # 等待服务启动
                    sleep 30
                    
                    # 健康检查
                    curl -f http://test.procurement.local/health || exit 1
                '''
            }
        }
        
        stage('自动化测试') {
            when {
                branch 'develop'
            }
            steps {
                echo '执行自动化测试...'
                sh '''
                    # 接口测试
                    cd tests/api
                    npm install
                    npm run test -- --env=test
                    
                    # UI自动化测试
                    cd ../ui
                    npm install
                    npm run test:e2e -- --env=test
                '''
            }
            
            post {
                always {
                    // 发布测试报告
                    publishTestResults testResultsPattern: 'tests/**/test-results.xml'
                    
                    // 保存测试截图
                    archiveArtifacts artifacts: 'tests/ui/screenshots/**/*.png', fingerprint: true
                }
            }
        }
        
        stage('部署到生产环境') {
            when {
                branch 'master'
            }
            steps {
                script {
                    // 需要人工确认
                    def deployApproval = input(
                        message: '确认部署到生产环境？',
                        parameters: [
                            choice(choices: ['Deploy', 'Abort'], description: '选择操作', name: 'ACTION')
                        ]
                    )
                    
                    if (deployApproval == 'Deploy') {
                        echo '部署到生产环境...'
                        sh '''
                            # 备份生产环境
                            ./scripts/backup-production.sh
                            
                            # 滚动更新部署
                            cd deployment/production
                            ./deploy.sh ${BUILD_VERSION}
                            
                            # 部署后验证
                            ./scripts/post-deploy-check.sh
                        '''
                    } else {
                        error('部署已取消')
                    }
                }
            }
        }
    }
    
    post {
        always {
            echo '清理工作空间...'
            cleanWs()
        }
        
        success {
            echo '流水线执行成功！'
            // 发送成功通知
            emailext (
                subject: "构建成功: ${env.JOB_NAME} - ${env.BUILD_NUMBER}",
                body: "构建版本: ${env.BUILD_VERSION}\n提交ID: ${env.COMMIT_ID}\n构建日志: ${env.BUILD_URL}",
                to: "${env.CHANGE_AUTHOR_EMAIL}"
            )
        }
        
        failure {
            echo '流水线执行失败！'
            // 发送失败通知
            emailext (
                subject: "构建失败: ${env.JOB_NAME} - ${env.BUILD_NUMBER}",
                body: "构建失败，请检查构建日志: ${env.BUILD_URL}",
                to: "${env.CHANGE_AUTHOR_EMAIL}"
            )
        }
    }
}
```

#### 6.1.2 Docker容器化配置

**后端Dockerfile：**
```dockerfile
# 多阶段构建
FROM maven:3.8.4-openjdk-17-slim AS builder

# 设置工作目录
WORKDIR /app

# 复制pom文件
COPY pom.xml .
COPY .mvn .mvn
COPY mvnw .

# 下载依赖（利用Docker缓存层）
RUN ./mvnw dependency:go-offline -B

# 复制源代码
COPY src ./src

# 构建应用
RUN ./mvnw clean package -DskipTests

# 运行阶段
FROM openjdk:17-jre-slim

# 安装必要工具
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 创建应用用户
RUN groupadd -r appuser && useradd -r -g appuser appuser

# 设置工作目录
WORKDIR /app

# 复制构建产物
COPY --from=builder /app/target/*.jar app.jar

# 创建日志目录
RUN mkdir -p /app/logs && chown -R appuser:appuser /app

# 切换到应用用户
USER appuser

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/actuator/health || exit 1

# 暴露端口
EXPOSE 8080

# JVM参数优化
ENV JAVA_OPTS="-Xms512m -Xmx2g -XX:+UseG1GC -XX:+UseContainerSupport -XX:MaxRAMPercentage=75"

# 启动命令
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]
```

**前端Dockerfile：**
```dockerfile
# 构建阶段
FROM node:18-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY . .

# 构建应用
RUN npm run build:prod

# 运行阶段
FROM nginx:1.21-alpine

# 安装必要工具
RUN apk add --no-cache curl

# 复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制nginx配置
COPY nginx.conf /etc/nginx/nginx.conf
COPY default.conf /etc/nginx/conf.d/default.conf

# 创建nginx用户
RUN addgroup -g 1001 -S nginx && \
    adduser -S -D -H -u 1001 -h /var/cache/nginx -s /sbin/nologin -G nginx -g nginx nginx

# 设置文件权限
RUN chown -R nginx:nginx /usr/share/nginx/html && \
    chown -R nginx:nginx /var/cache/nginx && \
    chown -R nginx:nginx /var/log/nginx && \
    chown -R nginx:nginx /etc/nginx/conf.d

# 创建PID文件目录
RUN mkdir -p /var/run/nginx && \
    chown -R nginx:nginx /var/run/nginx

# 切换到nginx用户
USER nginx

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:80/health || exit 1

# 暴露端口
EXPOSE 80

# 启动命令
CMD ["nginx", "-g", "daemon off;"]
```

### 6.2 生产环境部署架构

#### 6.2.1 Docker Compose生产配置

**docker-compose.prod.yml：**
```yaml
version: '3.8'

services:
  # 前端服务
  frontend:
    image: ${DOCKER_REGISTRY}/procurement-platform-frontend:${VERSION}
    container_name: procurement-frontend
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./nginx/logs:/var/log/nginx
    environment:
      - TZ=Asia/Shanghai
    networks:
      - procurement-network
    depends_on:
      - backend
    labels:
      - "com.procurement.service=frontend"
      - "com.procurement.version=${VERSION}"

  # 后端服务
  backend:
    image: ${DOCKER_REGISTRY}/procurement-platform-backend:${VERSION}
    container_name: procurement-backend
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - TZ=Asia/Shanghai
      - JAVA_OPTS=-Xms1g -Xmx4g -XX:+UseG1GC
      - DB_HOST=database
      - DB_PORT=3306
      - DB_NAME=${DB_NAME}
      - DB_USERNAME=${DB_USERNAME}
      - DB_PASSWORD=${DB_PASSWORD}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    volumes:
      - ./logs/backend:/app/logs
      - ./uploads:/app/uploads
    networks:
      - procurement-network
    depends_on:
      - database
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    labels:
      - "com.procurement.service=backend"
      - "com.procurement.version=${VERSION}"

  # 数据库服务
  database:
    image: mysql:8.0
    container_name: procurement-database
    restart: unless-stopped
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=${DB_ROOT_PASSWORD}
      - MYSQL_DATABASE=${DB_NAME}
      - MYSQL_USER=${DB_USERNAME}
      - MYSQL_PASSWORD=${DB_PASSWORD}
      - TZ=Asia/Shanghai
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/conf.d:/etc/mysql/conf.d:ro
      - ./mysql/init:/docker-entrypoint-initdb.d:ro
      - ./logs/mysql:/var/log/mysql
    command: >
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --default-time-zone='+08:00'
      --innodb-buffer-pool-size=1G
      --max-connections=500
      --slow-query-log=1
      --slow-query-log-file=/var/log/mysql/slow.log
      --long-query-time=2
    networks:
      - procurement-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    labels:
      - "com.procurement.service=database"

  # Redis缓存服务
  redis:
    image: redis:6.2-alpine
    container_name: procurement-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    environment:
      - TZ=Asia/Shanghai
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/etc/redis/redis.conf:ro
      - ./logs/redis:/var/log/redis
    command: redis-server /etc/redis/redis.conf --requirepass ${REDIS_PASSWORD}
    networks:
      - procurement-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
    labels:
      - "com.procurement.service=redis"

  # 监控服务
  prometheus:
    image: prom/prometheus:latest
    container_name: procurement-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
    networks:
      - procurement-network
    labels:
      - "com.procurement.service=monitoring"

  grafana:
    image: grafana/grafana:latest
    container_name: procurement-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning:ro
    networks:
      - procurement-network
    depends_on:
      - prometheus
    labels:
      - "com.procurement.service=monitoring"

  # 备份服务
  backup:
    image: mysql:8.0
    container_name: procurement-backup
    restart: "no"
    environment:
      - DB_HOST=database
      - DB_NAME=${DB_NAME}
      - DB_USERNAME=${DB_USERNAME}
      - DB_PASSWORD=${DB_PASSWORD}
    volumes:
      - ./backups:/backups
      - ./scripts/backup.sh:/backup.sh:ro
    networks:
      - procurement-network
    depends_on:
      - database
    entrypoint: ["/bin/bash", "/backup.sh"]
    labels:
      - "com.procurement.service=backup"

# 网络配置
networks:
  procurement-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷配置
volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
```

#### 6.2.2 自动化部署脚本

**部署脚本（deploy.sh）：**
```bash
#!/bin/bash

# 生产环境部署脚本
# 使用方法: ./deploy.sh [version]

set -e  # 遇到错误立即退出

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_NAME="procurement-platform"
COMPOSE_FILE="docker-compose.prod.yml"
ENV_FILE=".env.prod"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要工具
check_prerequisites() {
    log_info "检查部署环境..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装"
        exit 1
    fi
    
    if [[ ! -f "$ENV_FILE" ]]; then
        log_error "环境配置文件 $ENV_FILE 不存在"
        exit 1
    fi
    
    log_success "环境检查通过"
}

# 备份当前数据
backup_data() {
    log_info "备份生产数据..."
    
    local backup_dir="./backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    # 备份数据库
    if docker-compose -f "$COMPOSE_FILE" ps database | grep -q "Up"; then
        log_info "备份数据库..."
        docker-compose -f "$COMPOSE_FILE" exec -T database mysqldump \
            -u"$DB_USERNAME" -p"$DB_PASSWORD" "$DB_NAME" > "$backup_dir/database.sql"
        
        if [[ $? -eq 0 ]]; then
            log_success "数据库备份完成: $backup_dir/database.sql"
        else
            log_error "数据库备份失败"
            exit 1
        fi
    fi
    
    # 备份上传文件
    if [[ -d "./uploads" ]]; then
        log_info "备份上传文件..."
        tar -czf "$backup_dir/uploads.tar.gz" ./uploads
        log_success "文件备份完成: $backup_dir/uploads.tar.gz"
    fi
    
    # 备份配置文件
    log_info "备份配置文件..."
    cp "$ENV_FILE" "$backup_dir/"
    cp "$COMPOSE_FILE" "$backup_dir/"
    
    log_success "数据备份完成: $backup_dir"
}

# 拉取新镜像
pull_images() {
    local version=$1
    log_info "拉取镜像版本: $version"
    
    # 设置版本环境变量
    export VERSION=$version
    source "$ENV_FILE"
    
    # 拉取镜像
    docker-compose -f "$COMPOSE_FILE" pull
    
    if [[ $? -eq 0 ]]; then
        log_success "镜像拉取完成"
    else
        log_error "镜像拉取失败"
        exit 1
    fi
}

# 滚动更新服务
rolling_update() {
    log_info "开始滚动更新..."
    
    # 更新后端服务
    log_info "更新后端服务..."
    docker-compose -f "$COMPOSE_FILE" up -d --no-deps backend
    
    # 等待后端服务健康检查
    wait_for_health "backend" 120
    
    # 更新前端服务
    log_info "更新前端服务..."
    docker-compose -f "$COMPOSE_FILE" up -d --no-deps frontend
    
    # 等待前端服务启动
    wait_for_health "frontend" 60
    
    log_success "滚动更新完成"
}

# 等待服务健康检查
wait_for_health() {
    local service=$1
    local timeout=$2
    local counter=0
    
    log_info "等待 $service 服务健康检查..."
    
    while [[ $counter -lt $timeout ]]; do
        if docker-compose -f "$COMPOSE_FILE" ps "$service" | grep -q "healthy"; then
            log_success "$service 服务健康检查通过"
            return 0
        fi
        
        if docker-compose -f "$COMPOSE_FILE" ps "$service" | grep -q "unhealthy"; then
            log_error "$service 服务健康检查失败"
            return 1
        fi
        
        echo -n "."
        sleep 1
        ((counter++))
    done
    
    echo ""
    log_error "$service 服务健康检查超时"
    return 1
}

# 部署后验证
post_deploy_verification() {
    log_info "执行部署后验证..."
    
    # 检查服务状态
    log_info "检查服务状态..."
    if ! docker-compose -f "$COMPOSE_FILE" ps | grep -q "Up"; then
        log_error "部分服务未正常运行"
        docker-compose -f "$COMPOSE_FILE" ps
        return 1
    fi
    
    # API健康检查
    log_info "检查API健康状态..."
    local api_url="http://localhost:8080/actuator/health"
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f -s "$api_url" > /dev/null; then
            log_success "API健康检查通过"
            break
        fi
        
        if [[ $attempt -eq $max_attempts ]]; then
            log_error "API健康检查失败"
            return 1
        fi
        
        echo -n "."
        sleep 2
        ((attempt++))
    done
    
    # Web页面检查
    log_info "检查Web页面..."
    if curl -f -s "http://localhost/" > /dev/null; then
        log_success "Web页面访问正常"
    else
        log_error "Web页面访问失败"
        return 1
    fi
    
    # 数据库连接检查
    log_info "检查数据库连接..."
    if docker-compose -f "$COMPOSE_FILE" exec -T database mysql -u"$DB_USERNAME" -p"$DB_PASSWORD" -e "SELECT 1" "$DB_NAME" > /dev/null 2>&1; then
        log_success "数据库连接正常"
    else
        log_error "数据库连接失败"
        return 1
    fi
    
    log_success "部署后验证全部通过"
}

# 清理旧镜像
cleanup_old_images() {
    log_info "清理旧镜像..."
    
    # 删除悬空镜像
    docker image prune -f
    
    # 删除旧版本镜像（保留最近3个版本）
    docker images --format "table {{.Repository}}:{{.Tag}}\t{{.CreatedAt}}" | \
        grep "$PROJECT_NAME" | \
        sort -k2 -r | \
        tail -n +4 | \
        awk '{print $1}' | \
        xargs -r docker rmi
    
    log_success "镜像清理完成"
}

# 发送部署通知
send_notification() {
    local status=$1
    local version=$2
    
    if [[ "$status" == "success" ]]; then
        log_info "发送部署成功通知..."
        # 这里可以集成企业微信、钉钉等通知方式
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"🎉 采购平台部署成功\\n版本: $version\\n时间: $(date)\"}" \
            "$WEBHOOK_URL" 2>/dev/null || true
    else
        log_info "发送部署失败通知..."
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"❌ 采购平台部署失败\\n版本: $version\\n时间: $(date)\"}" \
            "$WEBHOOK_URL" 2>/dev/null || true
    fi
}

# 回滚函数
rollback() {
    local backup_dir=$1
    log_warning "开始回滚到之前版本..."
    
    # 停止当前服务
    docker-compose -f "$COMPOSE_FILE" down
    
    # 恢复配置文件
    if [[ -f "$backup_dir/$ENV_FILE" ]]; then
        cp "$backup_dir/$ENV_FILE" .
    fi
    
    # 恢复数据库
    if [[ -f "$backup_dir/database.sql" ]]; then
        log_info "恢复数据库..."
        docker-compose -f "$COMPOSE_FILE" up -d database
        sleep 30
        docker-compose -f "$COMPOSE_FILE" exec -T database mysql -u"$DB_USERNAME" -p"$DB_PASSWORD" "$DB_NAME" < "$backup_dir/database.sql"
    fi
    
    # 恢复文件
    if [[ -f "$backup_dir/uploads.tar.gz" ]]; then
        log_info "恢复上传文件..."
        rm -rf ./uploads
        tar -xzf "$backup_dir/uploads.tar.gz"
    fi
    
    # 启动服务
    docker-compose -f "$COMPOSE_FILE" up -d
    
    log_success "回滚完成"
}

# 主函数
main() {
    local version=${1:-"latest"}
    local backup_dir=""
    
    log_info "开始部署 $PROJECT_NAME 版本: $version"
    
    # 加载环境变量
    source "$ENV_FILE"
    export VERSION=$version
    
    # 执行部署步骤
    check_prerequisites
    
    # 备份数据
    backup_data
    backup_dir="./backups/$(ls -t ./backups | head -1)"
    
    # 拉取镜像
    if ! pull_images "$version"; then
        log_error "拉取镜像失败，部署终止"
        exit 1
    fi
    
    # 滚动更新
    if ! rolling_update; then
        log_error "滚动更新失败，开始回滚"
        rollback "$backup_dir"
        send_notification "failed" "$version"
        exit 1
    fi
    
    # 部署后验证
    if ! post_deploy_verification; then
        log_error "部署验证失败，开始回滚"
        rollback "$backup_dir"
        send_notification "failed" "$version"
        exit 1
    fi
    
    # 清理旧镜像
    cleanup_old_images
    
    # 发送成功通知
    send_notification "success" "$version"
    
    log_success "🎉 部署完成！版本: $version"
    log_info "访问地址: http://$(hostname -I | awk '{print $1}')"
    log_info "监控地址: http://$(hostname -I | awk '{print $1}'):3000"
}

# 错误处理
trap 'log_error "部署过程中发生错误，请检查日志"' ERR

# 执行主函数
main "$@"
```

## 7. 系统监控与运维设计

### 7.1 监控指标体系设计

系统建立了全方位的监控指标体系，覆盖基础设施、应用性能、业务指标等多个维度。

#### 7.1.1 Prometheus监控配置

**prometheus.yml配置：**
```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "rules/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # 应用监控
  - job_name: 'procurement-backend'
    static_configs:
      - targets: ['backend:8080']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 15s
    
  # 系统监控
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
      
  # 数据库监控
  - job_name: 'mysql-exporter'
    static_configs:
      - targets: ['mysql-exporter:9104']
      
  # Redis监控
  - job_name: 'redis-exporter'
    static_configs:
      - targets: ['redis-exporter:9121']
      
  # Nginx监控
  - job_name: 'nginx-exporter'
    static_configs:
      - targets: ['nginx-exporter:9113']
```

**自定义业务指标（在Spring Boot应用中）：**
```java
@Component
@Slf4j
public class BusinessMetrics {
    
    private final MeterRegistry meterRegistry;
    private final Counter projectCreatedCounter;
    private final Counter dualOperationCounter;
    private final Timer paymentProcessingTimer;
    private final Gauge activeProjectsGauge;
    private final Counter alertGeneratedCounter;
    
    public BusinessMetrics(MeterRegistry meterRegistry, ProjectRepository projectRepository) {
        this.meterRegistry = meterRegistry;
        
        // 项目创建计数器
        this.projectCreatedCounter = Counter.builder("projects.created.total")
            .description("Total number of projects created")
            .tag("type", "project")
            .register(meterRegistry);
            
        // 双人制操作计数器
        this.dualOperationCounter = Counter.builder("dual.operations.total")
            .description("Total number of dual operations")
            .register(meterRegistry);
            
        // 付款处理时间
        this.paymentProcessingTimer = Timer.builder("payment.processing.duration")
            .description("Payment processing duration")
            .register(meterRegistry);
            
        // 活跃项目数量
        this.activeProjectsGauge = Gauge.builder("projects.active.count")
            .description("Number of active projects")
            .register(meterRegistry, this, BusinessMetrics::getActiveProjectCount);
            
        // 预警生成计数器
        this.alertGeneratedCounter = Counter.builder("alerts.generated.total")
            .description("Total number of alerts generated")
            .register(meterRegistry);
    }
    
    /**
     * 记录项目创建事件
     */
    public void recordProjectCreated(String projectType, String procurementMethod) {
        projectCreatedCounter.increment(
            Tags.of(
                "project_type", projectType,
                "procurement_method", procurementMethod
            )
        );
        log.debug("记录项目创建指标: type={}, method={}", projectType, procurementMethod);
    }
    
    /**
     * 记录双人制操作事件
     */
    public void recordDualOperation(String operationType, String status) {
        dualOperationCounter.increment(
            Tags.of(
                "operation_type", operationType,
                "status", status
            )
        );
        log.debug("记录双人制操作指标: type={}, status={}", operationType, status);
    }
    
    /**
     * 记录付款处理时间
     */
    public void recordPaymentProcessing(Duration duration, String paymentMethod) {
        paymentProcessingTimer.record(duration, 
            Tags.of("payment_method", paymentMethod));
        log.debug("记录付款处理时间指标: duration={}ms, method={}", 
            duration.toMillis(), paymentMethod);
    }
    
    /**
     * 记录预警生成事件
     */
    public void recordAlertGenerated(String alertType, String alertLevel) {
        alertGeneratedCounter.increment(
            Tags.of(
                "alert_type", alertType,
                "alert_level", alertLevel
            )
        );
        log.debug("记录预警生成指标: type={}, level={}", alertType, alertLevel);
    }
    
    /**
     * 获取活跃项目数量
     */
    private double getActiveProjectCount() {
        try {
            return projectRepository.countByProjectStatusIn(
                Arrays.asList(
                    ProjectStatus.IMPLEMENTING,
                    ProjectStatus.CONTRACTED,
                    ProjectStatus.PERFORMING
                )
            );
        } catch (Exception e) {
            log.error("获取活跃项目数量失败", e);
            return 0;
        }
    }
    
    /**
     * 创建自定义指标
     */
    @EventListener
    public void handleProjectStatusChanged(ProjectStatusChangedEvent event) {
        Timer.Sample sample = Timer.start(meterRegistry);
        
        // 记录状态变更
        Counter.builder("project.status.changed.total")
            .description("Total number of project status changes")
            .tag("old_status", event.getOldStatus().name())
            .tag("new_status", event.getNewStatus().name())
            .register(meterRegistry)
            .increment();
            
        sample.stop(Timer.builder("project.status.change.duration")
            .description("Project status change processing time")
            .register(meterRegistry));
    }
    
    /**
     * 记录系统性能指标
     */
    @Scheduled(fixedRate = 60000) // 每分钟执行一次
    public void recordSystemMetrics() {
        // JVM内存使用情况
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        MemoryUsage heapMemory = memoryBean.getHeapMemoryUsage();
        
        Gauge.builder("jvm.memory.heap.used.ratio")
            .description("JVM heap memory usage ratio")
            .register(meterRegistry, this, 
                m -> (double) heapMemory.getUsed() / heapMemory.getMax());
        
        // 数据库连接池状态
        HikariPoolMXBean poolBean = getHikariPoolMXBean();
        if (poolBean != null) {
            Gauge.builder("db.connection.pool.active")
                .description("Active database connections")
                .register(meterRegistry, poolBean, HikariPoolMXBean::getActiveConnections);
                
            Gauge.builder("db.connection.pool.idle")
                .description("Idle database connections")
                .register(meterRegistry, poolBean, HikariPoolMXBean::getIdleConnections);
        }
    }
    
    private HikariPoolMXBean getHikariPoolMXBean() {
        try {
            MBeanServer server = ManagementFactory.getPlatformMBeanServer();
            ObjectName poolName = new ObjectName("com.zaxxer.hikari:type=Pool (HikariPool-1)");
            return JMX.newMXBeanProxy(server, poolName, HikariPoolMXBean.class);
        } catch (Exception e) {
            log.warn("无法获取HikariPool MXBean", e);
            return null;
        }
    }
}
```

#### 7.1.2 告警规则配置

**alert-rules.yml：**
```yaml
groups:
  - name: procurement-platform-alerts
    rules:
      # 应用服务告警
      - alert: ApplicationDown
        expr: up{job="procurement-backend"} == 0
        for: 1m
        labels:
          severity: critical
          service: backend
        annotations:
          summary: "采购平台后端服务不可用"
          description: "后端服务已经下线超过1分钟"
          
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
          service: backend
        annotations:
          summary: "HTTP错误率过高"
          description: "5分钟内HTTP 5xx错误率超过10%: {{ $value }}"
          
      - alert: SlowResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 3m
        labels:
          severity: warning
          service: backend
        annotations:
          summary: "响应时间过慢"
          description: "95%的请求响应时间超过2秒: {{ $value }}秒"
          
      # 系统资源告警
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "CPU使用率过高"
          description: "实例 {{ $labels.instance }} CPU使用率超过80%: {{ $value }}%"
          
      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 85
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "内存使用率过高"
          description: "实例 {{ $labels.instance }} 内存使用率超过85%: {{ $value }}%"
          
      - alert: DiskSpaceLow
        expr: (node_filesystem_avail_bytes / node_filesystem_size_bytes) * 100 < 15
        for: 5m
        labels:
          severity: critical
          service: system
        annotations:
          summary: "磁盘空间不足"
          description: "实例 {{ $labels.instance }} 磁盘剩余空间小于15%: {{ $value }}%"
          
      # 数据库告警
      - alert: DatabaseDown
        expr: mysql_up == 0
        for: 1m
        labels:
          severity: critical
          service: database
        annotations:
          summary: "数据库服务不可用"
          description: "MySQL数据库连接失败超过1分钟"
          
      - alert: DatabaseSlowQueries
        expr: rate(mysql_global_status_slow_queries[5m]) > 5
        for: 3m
        labels:
          severity: warning
          service: database
        annotations:
          summary: "数据库慢查询过多"
          description: "5分钟内慢查询速率超过5个/秒: {{ $value }}"
          
      - alert: DatabaseConnectionsHigh
        expr: mysql_global_status_threads_connected / mysql_global_variables_max_connections * 100 > 80
        for: 2m
        labels:
          severity: warning
          service: database
        annotations:
          summary: "数据库连接数过高"
          description: "数据库连接使用率超过80%: {{ $value }}%"
          
      # 业务指标告警
      - alert: ProjectCreationFailureHigh
        expr: rate(projects_created_total{result="failure"}[10m]) > 0.1
        for: 5m
        labels:
          severity: warning
          service: business
        annotations:
          summary: "项目创建失败率过高"
          description: "10分钟内项目创建失败率超过10%: {{ $value }}"
          
      - alert: PaymentProcessingDelayed
        expr: histogram_quantile(0.95, rate(payment_processing_duration_seconds_bucket[10m])) > 300
        for: 5m
        labels:
          severity: warning
          service: business
        annotations:
          summary: "付款处理时间过长"
          description: "95%的付款处理时间超过5分钟: {{ $value }}秒"
          
      - alert: DualOperationPendingTooLong
        expr: dual_operations_pending_duration_seconds > 86400
        for: 0m
        labels:
          severity: warning
          service: business
        annotations:
          summary: "双人制操作待确认时间过长"
          description: "双人制操作待确认超过24小时: {{ $value }}秒"
```

### 7.2 日志管理设计

#### 7.2.1 统一日志格式

**Logback配置（logback-spring.xml）：**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 日志格式定义 -->
    <property name="LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId},%X{spanId}] %logger{50} - %msg%n"/>
    <property name="LOG_PATH" value="./logs"/>
    
    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp/>
                <logLevel/>
                <loggerName/>
                <mdc/>
                <message/>
                <stackTrace/>
                <pattern>
                    <pattern>
                        {
                            "timestamp": "%d{yyyy-MM-dd HH:mm:ss.SSS}",
                            "level": "%level",
                            "thread": "%thread",
                            "logger": "%logger{50}",
                            "traceId": "%X{traceId:-}",
                            "spanId": "%X{spanId:-}",
                            "userId": "%X{userId:-}",
                            "ip": "%X{ip:-}",
                            "message": "%message",
                            "exception": "%exception"
                        }
                    </pattern>
                </pattern>
            </providers>
        </encoder>
    </appender>
    
    <!-- 文件输出 - 普通日志 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/application.log</file>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/application.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>30</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    
    <!-- 错误日志单独文件 -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/error.log</file>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/error.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>60</maxHistory>
        </rollingPolicy>
    </appender>
    
    <!-- 业务操作日志 -->
    <appender name="BUSINESS_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/business.log</file>
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp/>
                <mdc/>
                <message/>
                <pattern>
                    <pattern>
                        {
                            "timestamp": "%d{yyyy-MM-dd HH:mm:ss.SSS}",
                            "traceId": "%X{traceId:-}",
                            "userId": "%X{userId:-}",
                            "userName": "%X{userName:-}",
                            "ip": "%X{ip:-}",
                            "action": "%X{action:-}",
                            "businessType": "%X{businessType:-}",
                            "businessId": "%X{businessId:-}",
                            "result": "%X{result:-}",
                            "message": "%message"
                        }
                    </pattern>
                </pattern>
            </providers>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/business.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>90</maxHistory>
        </rollingPolicy>
    </appender>
    
    <!-- 安全日志 -->
    <appender name="SECURITY_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/security.log</file>
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp/>
                <mdc/>
                <message/>
                <pattern>
                    <pattern>
                        {
                            "timestamp": "%d{yyyy-MM-dd HH:mm:ss.SSS}",
                            "level": "SECURITY",
                            "traceId": "%X{traceId:-}",
                            "userId": "%X{userId:-}",
                            "userName": "%X{userName:-}",
                            "ip": "%X{ip:-}",
                            "userAgent": "%X{userAgent:-}",
                            "action": "%X{action:-}",
                            "resource": "%X{resource:-}",
                            "result": "%X{result:-}",
                            "riskLevel": "%X{riskLevel:-}",
                            "message": "%message"
                        }
                    </pattern>
                </pattern>
            </providers>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/security.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>180</maxHistory>
        </rollingPolicy>
    </appender>
    
    <!-- Logger配置 -->
    <logger name="com.procurement.platform" level="INFO"/>
    <logger name="BUSINESS" level="INFO" additivity="false">
        <appender-ref ref="BUSINESS_FILE"/>
    </logger>
    <logger name="SECURITY" level="INFO" additivity="false">
        <appender-ref ref="SECURITY_FILE"/>
    </logger>
    
    <!-- 第三方库日志级别 -->
    <logger name="org.springframework" level="WARN"/>
    <logger name="org.hibernate" level="WARN"/>
    <logger name="com.zaxxer.hikari" level="WARN"/>
    
    <!-- Root Logger -->
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
        <appender-ref ref="ERROR_FILE"/>
    </root>
</configuration>
```

#### 7.2.2 业务操作日志记录

**业务日志记录切面：**
```java
@Aspect
@Component
@Slf4j
public class BusinessLogAspect {
    
    private static final Logger BUSINESS_LOGGER = LoggerFactory.getLogger("BUSINESS");
    
    @Autowired
    private HttpServletRequest request;
    
    @Around("@annotation(businessLog)")
    public Object logBusinessOperation(ProceedingJoinPoint joinPoint, BusinessLog businessLog) throws Throwable {
        
        String traceId = MDC.get("traceId");
        if (traceId == null) {
            traceId = UUID.randomUUID().toString().replace("-", "");
            MDC.put("traceId", traceId);
        }
        
        // 获取用户信息
        String userId = SecurityUtils.getCurrentUserId().toString();
        String userName = SecurityUtils.getCurrentUserName();
        String userIp = getClientIpAddress(request);
        
        // 设置MDC
        MDC.put("userId", userId);
        MDC.put("userName", userName);
        MDC.put("ip", userIp);
        MDC.put("action", businessLog.action());
        MDC.put("businessType", businessLog.businessType());
        
        // 记录操作参数
        Object[] args = joinPoint.getArgs();
        String methodName = joinPoint.getSignature().getName();
        
        long startTime = System.currentTimeMillis();
        Object result = null;
        String resultStatus = "SUCCESS";
        String errorMessage = "";
        
        try {
            // 执行业务方法
            result = joinPoint.proceed();
            
            // 提取业务ID
            String businessId = extractBusinessId(result, args);
            if (businessId != null) {
                MDC.put("businessId", businessId);
            }
            
            return result;
            
        } catch (Exception e) {
            resultStatus = "FAILURE";
            errorMessage = e.getMessage();
            throw e;
            
        } finally {
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            MDC.put("result", resultStatus);
            
            // 记录业务日志
            BUSINESS_LOGGER.info("业务操作执行完成 - 方法: {}, 耗时: {}ms, 状态: {}, 错误: {}", 
                methodName, duration, resultStatus, errorMessage);
            
            // 清理MDC
            MDC.remove("action");
            MDC.remove("businessType");
            MDC.remove("businessId");
            MDC.remove("result");
        }
    }
    
    /**
     * 提取业务ID
     */
    private String extractBusinessId(Object result, Object[] args) {
        // 从返回结果中提取
        if (result != null) {
            if (result instanceof ProjectVO) {
                return ((ProjectVO) result).getId().toString();
            } else if (result instanceof ContractVO) {
                return ((ContractVO) result).getId().toString();
            }
        }
        
        // 从参数中提取
        for (Object arg : args) {
            if (arg instanceof Long) {
                return arg.toString();
            } else if (arg instanceof ProjectCreateDTO) {
                // 新创建的项目需要从返回结果中获取ID
                continue;
            }
        }
        
        return null;
    }
    
    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String ipAddress = request.getHeader("X-Forwarded-For");
        
        if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("Proxy-Client-IP");
        }
        if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getRemoteAddr();
        }
        
        return ipAddress;
    }
}

/**
 * 业务日志注解
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface BusinessLog {
    
    /**
     * 操作描述
     */
    String action();
    
    /**
     * 业务类型
     */
    String businessType();
    
    /**
     * 是否记录参数
     */
    boolean logParams() default false;
    
    /**
     * 是否记录返回值
     */
    boolean logResult() default false;
}
```

## 8. 总结

### 8.1 设计文档完整性总结

本详细设计说明书从技术实现的角度，全面深入地阐述了采购数字化综合管理平台的具体实现方案。文档涵盖了系统架构、数据库设计、业务逻辑实现、用户界面设计、系统集成部署、监控运维等各个技术层面，为开发团队提供了完整的技术实施指南。

**核心设计亮点：**

1. **模块化架构设计**：系统采用分层分模块的架构，每个模块职责明确，便于开发、测试和维护。

2. **完整的数据模型**：基于Excel采购台账的33个字段，设计了规范化的数据库表结构，确保数据完整性和一致性。

3. **双人制管理实现**：通过数据库约束、业务逻辑和前端控制三重保障，确保双人制操作的严格执行。

4. **智能预警机制**：建立了基于工作日历的精确时间计算和多级预警系统，有效防范业务风险。

5. **全面的安全设计**：从认证授权、数据加密、操作审计到安全监控，构建了完整的安全防护体系。

6. **现代化技术栈**：采用Spring Boot、Vue.js等主流技术框架，确保系统的技术先进性和可维护性。

### 8.2 技术创新点

1. **工作日历算法**：实现了基于《保障中小企业款项支付条例》要求的精确工作日计算算法。

2. **分期付款一致性保障**：通过数据库触发器和业务逻辑双重保障，确保分期付款金额的完全一致性。

3. **响应式界面设计**：采用移动端优先的设计理念，实现了桌面端和移动端的完美适配。

4. **CI/CD流水线**：建立了包含代码检查、安全扫描、自动化测试的完整部署流程。

5. **全方位监控体系**：集成了基础设施、应用性能、业务指标的立体化监控系统。

### 8.3 实施建议

**开发阶段建议：**

1. **迭代开发**：建议采用敏捷开发模式，按模块进行迭代开发，优先实现核心业务功能。

2. **代码质量**：严格执行代码审查制度，确保代码质量和开发规范的一致性。

3. **测试驱动**：采用测试驱动开发（TDD）模式，确保代码的可测试性和质量。

**部署阶段建议：**

1. **环境一致性**：确保开发、测试、生产环境的一致性，避免环境差异导致的问题。

2. **数据迁移**：制定详细的数据迁移方案，确保现有Excel数据的平滑迁移。

3. **用户培训**：制定完整的用户培训计划，确保用户能够熟练使用新系统。

**运维阶段建议：**

1. **监控告警**：建立7×24小时的监控告警机制，确保系统稳定运行。

2. **数据备份**：建立多层次的数据备份策略，确保数据安全。

3. **性能优化**：定期进行性能分析和优化，确保系统性能持续提升。

通过本详细设计说明书的指导，开发团队能够准确理解系统的技术要求，高效地完成系统开发和部署，最终构建出一个稳定、安全、高效的采购数字化综合管理平台。