# 采购数字化综合管理平台业务需求书（完善版）

## 一、项目背景与目标

### 1.1 项目背景

当前分行采购管理主要依赖Excel台账进行记录和管理，随着内控制度的不断完善和管理要求的持续提升，现有管理方式已无法满足新的业务需求，主要体现在以下几个方面：

**管理现状问题：**
- 数据管理分散且缺乏系统性整合，各部门信息孤岛现象依然严重，难以形成统一的数据视图
- 流程监控机制不够完善，特别是在关键节点缺乏有效的预警提醒功能
- 多部门协同工作仍然困难重重，监督力度有待进一步加强
- 数据统计分析的效率偏低，无法为管理决策提供及时有效的数据支撑

**新制度要求：**
更为重要的是，现有管理方式难以适应新的内控要求，包括：
- 采购实施双人制管理
- 验收人员多人制度（2人及以上）
- 履约监督专人负责制
- 涉密项目管理
- 分期付款精细化管理
- 采购需求部门责任追溯
- 《保障中小企业款项支付条例》合规要求

### 1.2 项目目标

**核心目标：**
1. **提高采购透明度**：实现采购信息的实时共享和查询，确保采购过程的公开、公平、公正，便于监督和管理
2. **降低采购成本**：通过系统化的采购管理和数据分析，优化采购策略，降低采购成本
3. **加强供应商管理**：建立完善的供应商信息库，对供应商进行分类管理、评估和考核
4. **提高数据准确性**：减少人工操作带来的错误，确保采购数据的准确性和完整性
5. **提升协同效率**：实现采购部门与其他相关部门之间的信息共享和协同工作

**特色目标：**
- 构建全面的采购流程数字化管理体系，实现从需求提出到付款完成的全链条管控
- 建立多层次、多角色的协同监督机制，确保内控制度有效落实
- 完善数据统一归集和信息共享机制，消除各部门间的信息壁垒
- 全面支持新的内控制度要求，包括采购实施双人制、验收多人制、履约专人监督等制度的落地实施

## 二、业务范围与边界

### 2.1 业务范围

平台需要覆盖采购管理的完整生命周期，基于采购台账结构，系统需要管理以下核心业务：

**采购全流程管理：**
- 采购计划制定与预算管理
- 采购需求管理（包括需求部门申报、需求审核和需求汇总）
- 采购申请与审批流程管理
- 供应商全生命周期管理
- 询价报价管理（支持3个及以上供应商报价）
- 合同管理（涵盖起草、审核、签订和履行全过程）
- 履约执行监控（特别强化专人监督制度）
- 验收管理（严格执行多人验收制度）
- 付款报销管理（支持分期付款精细化管理）

**特殊管理要求：**
- 涉密项目管理功能（确保敏感项目的全程安全管控）
- 双人制管理功能（确保关键岗位的制衡机制）
- 履约监督管理（建立专人负责的监督体系）
- 政府采购项目专门管理

### 2.2 用户范围

**核心用户角色：**
- **后勤服务中心**：采购执行主体，承担采购计划制定、采购实施、验收组织、付款申请等核心职责
- **财务部门**：负责预算控制、付款审核和财务监督，特别要加强对分期付款的管理
- **内审部门**：承担全流程合规审查和监督职能
- **各需求部门**：提出采购需求，承担验收责任和使用监督责任
- **法务部门**：提供合同审核和法律支持服务
- **管理层**：通过系统获得决策支持信息，对重大采购事项进行审批监督

**新增专门角色：**
- **履约监督人员**：专门负责合同履行过程的监督管理
- **涉密项目管理员**：负责敏感项目的全程安全管控

## 三、系统功能需求

### 3.1 用户管理

**用户角色管理**
- 系统支持多种用户角色：采购实施人员、财务人员、审计人员、履约监督人员、涉密项目管理员等
- 可根据实际业务需求自定义角色及其权限，灵活配置用户的角色分配
- 支持双人制作业的角色配置和权限管理

**用户信息管理**
- 提供用户信息的添加、修改、删除、查询等功能
- 包括用户的基本信息（姓名、联系方式、部门等）、登录信息、角色信息
- 支持用户密码的加密存储和重置功能，保障用户账户的安全性

**权限管理**
- 对不同角色的用户进行精细的权限控制
- 支持基于角色的权限继承和权限的动态调整
- 涉密项目的权限管理需要建立更加严格的控制机制

### 3.2 采购计划管理

**计划制定功能**
- 支持后勤服务中心制定年度采购计划，实现计划与预算的关联管理
- 建立动态调整机制以适应实际业务需要
- 采购项目按照货物、服务、工程等不同类型进行分类管理

**预算管理**
- 预算金额的设置和控制通过系统内部的预算管理模块来实现
- 支持预算数据的手工录入和维护
- 建立预算执行情况的跟踪分析功能

**审批流程**
- 根据金额大小和项目重要性设置不同的审批路径
- 重大项目要经过更高层级的审批
- 计划执行进度的跟踪需要实时反映计划完成情况

### 3.3 采购需求管理

**需求申报功能**
- 需求部门通过系统在线填报采购需求申请
- 需求信息必须包含：物品名称、详细规格型号、采购数量、预期用途、需求时间等
- 特别要求明确标注是否为涉密项目
- 建立需求信息的完整性检查机制

**需求审核流程**
- 后勤服务中心对提交的需求进行全面审核
- 包括采购必要性审核、技术规格合理性审核、库存查验等多个维度
- 支持需求退回修改和审核通过两种处理方式
- 审核意见要详细记录并可追溯查询

### 3.4 采购实施情况管理

这是系统的核心功能模块，基于采购台账结构设计，包含以下具体功能：

**采购情况录入管理**
- 提供采购情况的录入功能，支持多种方式录入（手工录入、导入Excel模板等）
- 支持批量导入采购台账数据，提高录入效率
- 建立数据校验机制，确保录入数据的准确性和完整性

**关键字段管理**
1. **基本信息**：采购项目/品类名称、项目类型、预算金额、采购方式、采购组织形式
2. **特殊属性**：是否政府采购、是否涉密、成交金额
3. **人员信息**：采购需求部门、验收人员（2人及以上）、履约监督人员、采购实施部门、采购实施人员（双人）
4. **过程管理**：签报审批日期、法律审核日期、采购实施时间、合同签订日期、验收日期
5. **供应商报价**：供应商1-3、报价1-3、最终供应商名称、采购代理机构

**多期付款管理**
- "付款日期"支持多期付款情况，需要登记每次付款日期
- 每次"付款日期"与"付款金额"对应，"付款金额"合计值与"成交金额"需要一致
- 建立付款计划和实际付款的对比分析功能

**多供应商报价管理**
- 报价情况中的"供应商"支持多于3个的情况
- 每个"供应商"需对应一个"报价"
- "成交供应商"需与至少一个"供应商"对应，如存在多个"成交供应商"，则与"供应商"一一对应

**查询统计功能**
- 根据不同条件进行灵活查询：采购项目类型、采购方式、采购实施日期、合同签订日期、验收日期、付款日期、采购代理机构、供应商、合同名称、合同编号等
- 实现采购实施情况的统计分析，如按时间段、采购项目类型、采购方式等统计采购实施的数量、金额等信息

### 3.5 涉密项目管理

涉密项目管理是基于新台账要求新增的重要功能：

**涉密标识管理**
- 系统需要在项目立项阶段就明确标识涉密属性
- 建立涉密项目的专门管理流程
- 涉密项目的参与人员需要经过专门的权限审核

**安全管控机制**
- 涉密项目的信息存储需要采用更高级别的安全措施
- 包括数据加密存储、访问日志记录、权限严格控制等安全机制
- 涉密项目的供应商管理需要建立专门的准入机制

### 3.6 双人制管理

双人制管理是基于新台账要求新增的重要功能：

**采购实施双人制**
- 在采购实施的关键环节必须有两名人员共同参与
- 系统需要强制要求录入两名采购实施人员的信息
- 建立相应的权限控制机制

**验收多人制**
- 验收环节必须有两人及以上参与
- 系统要强制要求录入多名验收人员信息
- 建立相互制约机制，防止单人操作风险

**权限与监督**
- 双人制的权限管理需要确保相关人员都具备相应的操作权限
- 双人制的操作记录需要详细记录每个人员的具体操作内容和时间
- 定期检查双人制执行情况，发现违规操作及时预警

### 3.7 供应商管理

**供应商信息库**
- 建立供应商信息库，详细记录供应商的基本信息、资质信息、产品信息
- 支持供应商信息的添加、修改、删除、查询功能
- 可对供应商进行分类管理，如按行业、产品类别、合作级别等进行分类

**供应商评估与考核**
- 设计供应商评估指标体系，包括产品质量、交货期、价格、售后服务等多个维度
- 定期对供应商进行评估和考核，根据评估结果对供应商进行分级管理
- 支持对供应商评估数据的录入、修改、查询和统计分析

**供应商合作管理**
- 记录与供应商的合作历史，包括采购订单、合同、付款记录等信息
- 提供供应商的合同管理功能，包括合同的签订、续签、变更、终止等操作

### 3.8 合同管理

**合同全生命周期管理**
- 提供采购合同的录入、修改、删除、查询功能
- 详细记录合同的基本信息、合同条款
- 可对合同进行分类管理，如按合同类型、合同金额范围等进行分类

**合同模板与审核**
- 合同模板库的管理提供标准化的合同模板
- 合同起草与审核流程建立标准化的工作流程
- 法律意见书的关联管理确保重要合同经过充分的法律审核

**合同履行管理**
- 合同履行期限的提醒功能帮助及时掌握合同履行情况
- 合同变更管理支持合同变更的标准化流程
- 已盖章合同扫描件的上传与管理实现合同文件的电子化存储

### 3.9 履约与验收管理

**履约监督制度**
- 履约监督人员制度要求为每个合同指定专门的履约监督人员
- 负责全程跟踪合同履行情况
- 建立履约计划的制定与跟踪机制

**验收管理**
- 到货或服务完成的登记需要及时准确地记录
- 验收单的在线填报支持多人参与的验收模式
- 系统要求验收人员不少于两人，确保验收工作的客观性和准确性

**质量管理**
- 验收结果的记录需要详细记录验收过程、验收结论、存在问题等信息
- 质量问题的反馈与处理建立标准化的问题处理流程
- 履约预警提醒功能帮助及时发现履约风险

### 3.10 付款报销管理

付款报销管理功能特别强化了分期付款管理和付款时限管控：

**付款合规管理**
按照《保障中小企业款项支付条例》要求：
- 机关、事业单位从中小企业采购货物、工程、服务，应当自货物、工程、服务交付之日起30日内支付款项
- 合同另有约定的，从其约定，但付款期限最长不得超过60日
- 以货物、工程、服务交付后经检验或者验收合格作为支付条件的，付款期限应当自检验或者验收合格之日起算

**分期付款精细化管理**
- 支持多期付款的详细记录和管理
- 每次付款日期与付款金额的精确对应
- 付款金额合计值与成交金额的一致性检查
- 分期付款计划的制定和执行跟踪

**付款时限预警管理**
- 对验收日期、付款日期等实行预警管理
- 发票登记后自动启动倒计时机制（通常20个工作日）
- 分级预警机制：剩余10日、5日、3日、1日时分别进行预警
- 超期未付款自动升级预警，推送至相关领导进行处理

**报销材料管理**
- 系统性地收集和整理所有必要的报销凭据
- 建立材料完整性检查机制，通过清单化管理确保报销材料的完整性
- 支持电子化存储和检索

### 3.11 监督预警功能

建立更加全面的多维度监督预警机制：

**付款时限预警**
- 发票登记后自动启动倒计时机制
- 分级预警机制更加细化
- 超期未付款自动升级预警
- 付款临期清单每日推送功能

**全流程监督预警**
- 超预算预警功能监控预算执行情况
- 流程超时预警监控各个业务环节的时限要求
- 合同到期提醒帮助及时处理合同续签或终止事宜
- 异常行为监测功能通过数据分析发现异常操作模式

**制度执行预警**
- 合规性检查提醒确保各项操作符合制度要求
- 涉密项目预警专门监控涉密项目的管理规范性
- 双人制执行预警监控双人制的执行情况

### 3.12 统计分析与报表

**采购分析报表**
- 采购执行情况统计全面反映采购计划的执行进度和完成质量
- 预算执行分析帮助了解预算使用情况和执行效率
- 供应商分析报表提供供应商绩效评价和市场分析
- 采购效率分析评估采购流程的效率和改进空间

**合规性统计报表**
- 反映各项制度的执行情况，包括双人制执行率、验收规范性、涉密项目管控等关键指标
- 付款及时性分析包括按时付款率统计、超期付款明细报表、付款周期分析等内容
- 部门付款及时性排名促进各部门提高付款工作效率

**自定义分析功能**
- 自定义报表功能支持用户根据特定需求生成个性化报表
- 数据导出功能支持多种格式的数据导出，便于进一步分析和使用

## 四、角色权限设计

### 4.1 角色定义

基于新的管理要求，角色设计需要更加细化：

**基础角色**
- **需求部门人员**：采购需求提出、采购申请发起、验收确认等职责
- **需求部门负责人**：负责本部门采购需求的审批工作
- **后勤服务中心采购员**：负责采购执行、报销准备等具体工作，需要严格执行双人制要求
- **后勤服务中心主任**：承担部门管理、报销审批等管理职责

**专门角色**
- **履约监督人员**：专门负责合同履行过程的监督管理
- **涉密项目管理员**：专门负责涉密项目的全程管控
- **会计科人员和主要负责人**：承担财务处理和审批职责
- **分管行领导和分管财务行领导**：承担相应的审批职责

**监督角色**
- **内审人员**：具有全流程查看权限，负责合规检查和审计监督
- **法务人员**：提供合同审核和法律支持
- **系统管理员**：负责系统维护和配置管理

### 4.2 权限矩阵

权限矩阵设计体现职责分离和相互制约的原则：
- 特别体现双人制、多人验收、履约监督等新的内控要求
- 涉密项目的权限管理需要建立更加严格的控制机制
- 确保只有具备相应资质的人员才能访问涉密项目信息

## 五、技术需求

### 5.1 系统架构

**架构设计**
- 系统采用B/S架构设计，支持多终端访问
- 前后端分离的设计模式提高系统的可维护性和扩展性
- 微服务架构便于系统功能的模块化管理和后续扩展
- 分布式部署支持提高系统的可用性和性能

### 5.2 技术要求

**性能要求**
- 系统需要支持主流浏览器访问，包括Chrome、Firefox、Edge等
- 页面加载时间不超过3秒，确保良好的用户体验
- 系统需要支持至少200个并发用户的同时访问

**安全要求**
- 数据安全方面需要采用加密传输和存储技术
- 特别是涉密项目数据需要更高级别的安全保护
- 系统可用性要求达到99.5%以上

### 5.3 集成要求

**数据交换机制**
考虑到当前技术环境限制，系统暂时无法与财务系统进行直接的数据对接，因此需要建立完善的数据交换替代机制：
- 系统应当设计标准化的数据导入导出接口，支持与财务部门进行规范化的数据交换
- 系统需要提供财务数据导出功能，能够按照财务部门要求的标准格式导出相关数据
- 建立财务反馈数据的导入功能，支持财务部门将实际付款情况导入到采购管理系统中

**设备集成**
- 系统要支持扫描设备的接入，用于合同、审批单、发票等重要文件的电子化管理
- 预留与其他业务系统的接口，支持未来可能的系统集成需求

### 5.4 安全要求

**基础安全**
- 用户身份认证、权限访问控制、数据加密存储、操作日志审计、数据备份恢复

**涉密项目安全**
- 基于新增的涉密项目管理需求，系统的安全要求需要进一步提高
- 涉密项目数据需要采用更高级别的加密技术进行存储和传输
- 访问控制需要更加严格，建立基于角色和项目属性的多层次权限控制机制

## 六、非功能性需求

### 6.1 性能要求
- 系统响应时间需要快速高效，查询功能需要进行优化以支持大数据量处理
- 特别是统计分析功能需要能够处理大量历史数据的快速查询和分析

### 6.2 易用性要求
- 界面设计需要友好直观，操作流程要简化便捷
- 提供详细的操作指引，支持批量操作以提高工作效率
- 特别是双人制操作界面需要设计得直观明了，便于用户理解和执行

### 6.3 可维护性要求
- 系统采用模块化设计，便于后续功能扩展和维护
- 建立完善的日志记录机制，便于问题定位和系统优化
- 提供完善的管理工具，支持系统配置和维护工作

## 七、实施计划建议

### 7.1 实施阶段

**第一阶段（4个月）**
- 进行深入的需求调研和确认，特别是对新增的涉密管理、双人制管理、履约监督等功能进行详细的需求分析
- 系统设计与开发需要充分考虑新的管理要求，核心功能的实现要确保满足内控制度的要求

**第二阶段（2个月）**
- 进行系统集成测试，重点测试双人制管理、涉密项目管理等新功能的稳定性和安全性
- 用户培训需要特别针对新功能进行专门培训
- 试运行阶段要重点验证新管理制度的系统化落实效果

**第三阶段（1个月）**
- 进行系统优化，根据试运行情况完善功能细节
- 正式上线后提供全面的运维支持，确保系统稳定运行

### 7.2 风险控制
- 建立完善的项目管理机制，定期评审项目进度和质量
- 制定详细的数据迁移方案，确保历史数据的完整迁移
- 建立应急预案，应对可能出现的技术风险和业务风险
- 特别要重视涉密项目数据的安全迁移和管理

## 八、预期效益

### 8.1 管理效益
- 进一步规范采购流程，提高合规性水平
- 加强内部控制，特别是通过双人制管理、多人验收、履约监督等机制降低采购风险
- 提升监督效率，形成更加有效的监管合力
- 优化资源配置，降低采购成本
- 涉密项目管理功能将大大提高敏感项目的安全管控水平

### 8.2 经济效益
- 减少人工成本，提高工作效率
- 通过规范化管理降低采购成本，避免重复采购
- 分期付款精细化管理有助于优化资金使用效率

### 8.3 社会效益
- 提升单位整体形象，促进廉洁采购文化建设
- 推动数字化转型，提高管理现代化水平
- 通过严格的内控制度建设，为行业内控管理提供示范和参考

## 九、附录

### 9.1 采购方式说明
- **小额零星采购项目自行组织实施**：主要采用协议定点、网上订购、比价、议价等采购方式
- **集中采购**：采用公开招标、邀请招标、竞争性磋商、竞争性谈判、单一来源采购、询价以及国家认定的其他采购方式

### 9.2 台账字段说明
基于采购台账结构，系统需要管理的核心字段包括：
- 采购项目/品类名称、项目类型、预算金额、采购方式、采购组织形式
- 是否政府采购、是否涉密、成交金额、采购需求部门
- 验收人员（2人及以上）、履约监督人员、采购实施部门、采购实施人员（双人）
- 法律意见书编号、签报审批日期、法律审核日期、采购实施时间
- 合同签订日期、验收日期、付款金额、付款日期
- 合同名称、合同履行期限、最终供应商名称、采购代理机构
- 供应商1-3、报价1-3

### 9.3 法规依据
- 《保障中小企业款项支付条例》第九条、第十条
- 相关采购管理法规和内控制度要求