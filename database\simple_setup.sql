-- =============================================
-- 采购数字化综合管理平台数据库初始化脚本
-- 兼容MySQL 9.3版本
-- =============================================

-- 创建数据库
DROP DATABASE IF EXISTS procurement_platform;
CREATE DATABASE procurement_platform 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE procurement_platform;

-- =============================================
-- 用户表
-- =============================================
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    real_name VARCHAR(100) NOT NULL,
    employee_id VARCHAR(50) UNIQUE,
    email VARCHAR(100),
    phone VARCHAR(20),
    department VARCHAR(100),
    position VARCHAR(100),
    status TINYINT DEFAULT 1,
    last_login_time DATETIME,
    last_login_ip VARCHAR(50),
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    create_by BIGINT,
    update_by BIGINT,
    deleted TINYINT DEFAULT 0,
    version INT DEFAULT 1,
    remark TEXT,
    INDEX idx_username (username),
    INDEX idx_employee_id (employee_id),
    INDEX idx_department (department),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 角色表
-- =============================================
CREATE TABLE roles (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    role_code VARCHAR(50) NOT NULL UNIQUE,
    role_name VARCHAR(100) NOT NULL,
    description TEXT,
    status TINYINT DEFAULT 1,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    create_by BIGINT,
    update_by BIGINT,
    deleted TINYINT DEFAULT 0,
    version INT DEFAULT 1,
    remark TEXT,
    INDEX idx_role_code (role_code),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 权限表
-- =============================================
CREATE TABLE permissions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    permission_code VARCHAR(100) NOT NULL UNIQUE,
    permission_name VARCHAR(100) NOT NULL,
    permission_type VARCHAR(20) NOT NULL,
    parent_id BIGINT DEFAULT 0,
    path VARCHAR(200),
    component VARCHAR(200),
    icon VARCHAR(100),
    sort_order INT DEFAULT 0,
    status TINYINT DEFAULT 1,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    create_by BIGINT,
    update_by BIGINT,
    deleted TINYINT DEFAULT 0,
    version INT DEFAULT 1,
    remark TEXT,
    INDEX idx_permission_code (permission_code),
    INDEX idx_parent_id (parent_id),
    INDEX idx_permission_type (permission_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 用户角色关联表
-- =============================================
CREATE TABLE user_roles (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    create_by BIGINT,
    UNIQUE KEY uk_user_role (user_id, role_id),
    INDEX idx_user_id (user_id),
    INDEX idx_role_id (role_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 角色权限关联表
-- =============================================
CREATE TABLE role_permissions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    role_id BIGINT NOT NULL,
    permission_id BIGINT NOT NULL,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    create_by BIGINT,
    UNIQUE KEY uk_role_permission (role_id, permission_id),
    INDEX idx_role_id (role_id),
    INDEX idx_permission_id (permission_id),
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 采购需求表
-- =============================================
CREATE TABLE procurement_requirements (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    requirement_no VARCHAR(50) NOT NULL UNIQUE,
    requirement_name VARCHAR(200) NOT NULL,
    requirement_type VARCHAR(50) NOT NULL,
    department VARCHAR(100) NOT NULL,
    applicant_id BIGINT NOT NULL,
    applicant_name VARCHAR(100) NOT NULL,
    urgency_level VARCHAR(20) DEFAULT 'NORMAL',
    expected_amount DECIMAL(15,2),
    expected_date DATE,
    usage_purpose TEXT NOT NULL,
    technical_requirements TEXT,
    is_classified TINYINT DEFAULT 0,
    classified_level VARCHAR(20),
    approval_status VARCHAR(20) DEFAULT 'PENDING',
    approver_id BIGINT,
    approver_name VARCHAR(100),
    approval_time DATETIME,
    approval_opinion TEXT,
    status TINYINT DEFAULT 1,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    create_by BIGINT,
    update_by BIGINT,
    deleted TINYINT DEFAULT 0,
    version INT DEFAULT 1,
    remark TEXT,
    INDEX idx_requirement_no (requirement_no),
    INDEX idx_requirement_type (requirement_type),
    INDEX idx_department (department),
    INDEX idx_applicant_id (applicant_id),
    INDEX idx_approval_status (approval_status),
    FOREIGN KEY (applicant_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 采购项目表
-- =============================================
CREATE TABLE procurement_projects (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    project_no VARCHAR(50) NOT NULL UNIQUE,
    project_name VARCHAR(200) NOT NULL,
    requirement_id BIGINT NOT NULL,
    project_type VARCHAR(50) NOT NULL,
    procurement_method VARCHAR(50) NOT NULL,
    budget_amount DECIMAL(15,2) NOT NULL,
    is_classified TINYINT DEFAULT 0,
    classified_level VARCHAR(20),
    primary_purchaser_id BIGINT NOT NULL,
    primary_purchaser_name VARCHAR(100) NOT NULL,
    secondary_purchaser_id BIGINT NOT NULL,
    secondary_purchaser_name VARCHAR(100) NOT NULL,
    supervisor_id BIGINT,
    supervisor_name VARCHAR(100),
    project_status VARCHAR(20) DEFAULT 'CREATED',
    start_date DATE,
    end_date DATE,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    create_by BIGINT,
    update_by BIGINT,
    deleted TINYINT DEFAULT 0,
    version INT DEFAULT 1,
    remark TEXT,
    INDEX idx_project_no (project_no),
    INDEX idx_requirement_id (requirement_id),
    INDEX idx_project_status (project_status),
    FOREIGN KEY (requirement_id) REFERENCES procurement_requirements(id),
    FOREIGN KEY (primary_purchaser_id) REFERENCES users(id),
    FOREIGN KEY (secondary_purchaser_id) REFERENCES users(id),
    FOREIGN KEY (supervisor_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 插入初始数据
-- =============================================

-- 插入默认用户
INSERT INTO users (username, password, real_name, employee_id, email, phone, department, position, status, remark) VALUES
('admin', '$2a$10$7JB720yubVSOfvVWdBYoOeWpyAcXMsEiUHUOWpylsHVSuqiKVoFla', '系统管理员', 'ADMIN001', '<EMAIL>', '13800000000', '信息中心', '系统管理员', 1, '默认系统管理员'),
('procurement01', '$2a$10$7JB720yubVSOfvVWdBYoOeWpyAcXMsEiUHUOWpylsHVSuqiKVoFla', '张采购', 'PROC001', '<EMAIL>', '13800000001', '后勤服务中心', '采购员', 1, '主采购员'),
('procurement02', '$2a$10$7JB720yubVSOfvVWdBYoOeWpyAcXMsEiUHUOWpylsHVSuqiKVoFla', '李采购', 'PROC002', '<EMAIL>', '13800000002', '后勤服务中心', '采购员', 1, '副采购员');

-- 插入基础角色
INSERT INTO roles (role_code, role_name, description, status, remark) VALUES
('ADMIN', '系统管理员', '系统管理员，拥有所有权限', 1, '系统管理员角色'),
('PROCUREMENT_STAFF', '采购员', '采购实施人员，执行具体采购工作', 1, '采购员角色'),
('FINANCE_STAFF', '财务人员', '财务处理人员，负责付款处理', 1, '财务人员角色');

-- 插入基础权限
INSERT INTO permissions (permission_code, permission_name, permission_type, parent_id, status, remark) VALUES
('SYSTEM', '系统管理', 'MENU', 0, 1, '系统管理菜单'),
('PROCUREMENT', '采购管理', 'MENU', 0, 1, '采购管理菜单'),
('FINANCE', '财务管理', 'MENU', 0, 1, '财务管理菜单');

-- 分配用户角色
INSERT INTO user_roles (user_id, role_id) VALUES
(1, 1),
(2, 2),
(3, 2);

-- 分配角色权限
INSERT INTO role_permissions (role_id, permission_id) VALUES
(1, 1),
(1, 2),
(1, 3),
(2, 2),
(3, 3);

-- =============================================
-- 完成信息
-- =============================================
SELECT '数据库初始化完成！' as status;
SELECT '默认管理员账户：admin/admin123' as login_info;
SELECT COUNT(*) as user_count FROM users;
SELECT COUNT(*) as role_count FROM roles;
SELECT COUNT(*) as permission_count FROM permissions;
