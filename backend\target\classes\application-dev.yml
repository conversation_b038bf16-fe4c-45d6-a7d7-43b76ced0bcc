# 开发环境配置
spring:
  # 数据源配置
  datasource:
    url: *******************************************************************************************************************************************************************************************
    username: root
    password: root
    
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        format_sql: true

  # Redis配置
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0

# 日志配置
logging:
  level:
    root: info
    com.procurement: debug
    org.springframework.security: debug
    org.springframework.web: debug
    com.baomidou.mybatisplus: debug
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n'

# MyBatis Plus配置
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# 开发环境特殊配置
procurement:
  # 开发模式
  dev-mode: true
  
  # 跨域配置
  cors:
    enabled: true
    allowed-origins: http://localhost:5173,http://localhost:3000
    allowed-methods: GET,POST,PUT,DELETE,OPTIONS
    allowed-headers: "*"
    allow-credentials: true
  
  # 文件存储配置
  file:
    upload-path: D:/caigoupingtai/uploads/
  
  # 安全配置（开发环境放宽）
  security:
    ignore-urls: /swagger-ui/**,/v3/api-docs/**,/druid/**,/actuator/**
