package com.procurement;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 采购数字化综合管理平台主启动类
 * 
 * <AUTHOR> Platform Team
 * @version 1.0.0
 * @since 2025-06-29
 */
@SpringBootApplication
@EnableTransactionManagement
@EnableAsync
@EnableScheduling
@MapperScan("com.procurement.mapper")
public class ProcurementApplication {

    public static void main(String[] args) {
        SpringApplication.run(ProcurementApplication.class, args);
        System.out.println("=================================");
        System.out.println("采购数字化综合管理平台启动成功！");
        System.out.println("访问地址：http://localhost:8080");
        System.out.println("API文档：http://localhost:8080/swagger-ui.html");
        System.out.println("=================================");
    }
}
