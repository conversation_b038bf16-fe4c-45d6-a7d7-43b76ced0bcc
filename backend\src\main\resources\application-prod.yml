# 生产环境配置
spring:
  # 数据源配置
  datasource:
    url: *************************************************************************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:root}
    
    # 生产环境连接池配置
    druid:
      initial-size: 10
      min-idle: 10
      max-active: 50
      max-wait: 60000
      
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false

  # Redis配置
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: ${REDIS_DATABASE:0}

# 日志配置
logging:
  level:
    root: warn
    com.procurement: info
    org.springframework.security: warn
  file:
    name: /var/log/procurement-platform/application.log
    max-size: 100MB
    max-history: 30

# MyBatis Plus配置
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl

# 生产环境配置
procurement:
  # 生产模式
  dev-mode: false
  
  # JWT配置（生产环境使用环境变量）
  jwt:
    secret: ${JWT_SECRET:procurement-platform-jwt-secret-key-2025-production}
    expiration: ${JWT_EXPIRATION:7200000}
    refresh-expiration: ${JWT_REFRESH_EXPIRATION:*********}
  
  # 文件存储配置
  file:
    upload-path: ${FILE_UPLOAD_PATH:/opt/procurement/uploads/}
  
  # 安全配置（生产环境严格）
  security:
    ignore-urls: /actuator/health
  
  # 跨域配置（生产环境限制）
  cors:
    enabled: true
    allowed-origins: ${ALLOWED_ORIGINS:https://procurement.yourcompany.com}
    allowed-methods: GET,POST,PUT,DELETE
    allowed-headers: Authorization,Content-Type
    allow-credentials: true

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized
