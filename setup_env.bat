@echo off
echo ================================
echo Setting up Development Environment
echo Procurement Platform
echo ================================
echo.

echo Setting environment variables for current session...

REM Set JAVA_HOME
set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-********-hotspot
echo JAVA_HOME = %JAVA_HOME%

REM Set MAVEN_HOME
set MAVEN_HOME=C:\Program Files\Apache\maven
echo MAVEN_HOME = %MAVEN_HOME%

REM Update PATH
set PATH=%JAVA_HOME%\bin;%MAVEN_HOME%\bin;%PATH%
echo PATH updated

echo.
echo Testing Java...
java -version

echo.
echo Testing Maven...
mvn -version

echo.
echo ================================
echo Environment setup complete
echo ================================
echo.
echo You can now run:
echo   mvn clean compile
echo   mvn spring-boot:run
echo.
pause
