<template>
  <div class="requirement-management">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>需求管理</span>
          <el-button type="primary">
            <el-icon><Plus /></el-icon>
            新建需求
          </el-button>
        </div>
      </template>
      
      <el-table :data="requirementList" style="width: 100%">
        <el-table-column prop="requirementNo" label="需求编号" />
        <el-table-column prop="requirementName" label="需求名称" />
        <el-table-column prop="department" label="申请部门" />
        <el-table-column prop="applicantName" label="申请人" />
        <el-table-column prop="urgencyLevel" label="紧急程度" width="100">
          <template #default="{ row }">
            <el-tag :type="getUrgencyColor(row.urgencyLevel)">
              {{ row.urgencyLevel }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="approvalStatus" label="审批状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.approvalStatus)">
              {{ getStatusText(row.approvalStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button type="primary" size="small">查看</el-button>
            <el-button type="success" size="small" v-if="row.approvalStatus === 'PENDING'">
              审批
            </el-button>
            <el-button type="warning" size="small">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const requirementList = ref([
  {
    id: 1,
    requirementNo: 'REQ202506290001',
    requirementName: '办公用品采购需求',
    department: '行政办公室',
    applicantName: '张三',
    urgencyLevel: 'NORMAL',
    approvalStatus: 'PENDING',
    createTime: '2025-06-29 09:00:00'
  },
  {
    id: 2,
    requirementNo: 'REQ202506290002',
    requirementName: '计算机设备采购需求',
    department: '信息中心',
    applicantName: '李四',
    urgencyLevel: 'HIGH',
    approvalStatus: 'APPROVED',
    createTime: '2025-06-29 10:00:00'
  }
])

const getUrgencyColor = (level: string) => {
  const colorMap: Record<string, string> = {
    'LOW': 'info',
    'NORMAL': 'success',
    'HIGH': 'warning',
    'URGENT': 'danger'
  }
  return colorMap[level] || 'info'
}

const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    'PENDING': 'warning',
    'APPROVED': 'success',
    'REJECTED': 'danger'
  }
  return colorMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'PENDING': '待审批',
    'APPROVED': '已通过',
    'REJECTED': '已拒绝'
  }
  return textMap[status] || status
}
</script>

<style lang="scss" scoped>
.requirement-management {
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}
</style>
