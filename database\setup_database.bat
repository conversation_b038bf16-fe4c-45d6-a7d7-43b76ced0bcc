@echo off
echo ================================
echo 采购数字化综合管理平台
echo 数据库初始化脚本
echo ================================
echo.

echo 请确保MySQL服务已启动...
echo 数据库连接信息：
echo 主机：localhost
echo 端口：3306
echo 用户名：root
echo 密码：root
echo.

set /p confirm=确认开始初始化数据库吗？(Y/N): 
if /i "%confirm%" neq "Y" (
    echo 操作已取消
    pause
    exit /b
)

echo.
echo [1/6] 创建数据库...
mysql -h localhost -P 3306 -u root -proot < init/01_create_database.sql
if errorlevel 1 (
    echo 错误：数据库创建失败
    pause
    exit /b 1
)
echo ✅ 数据库创建成功

echo.
echo [2/6] 创建基础表结构...
mysql -h localhost -P 3306 -u root -proot < init/02_create_tables.sql
if errorlevel 1 (
    echo 错误：基础表创建失败
    pause
    exit /b 1
)
echo ✅ 基础表创建成功

echo.
echo [3/6] 创建业务表结构...
mysql -h localhost -P 3306 -u root -proot < init/03_create_business_tables.sql
if errorlevel 1 (
    echo 错误：业务表创建失败
    pause
    exit /b 1
)
echo ✅ 业务表创建成功

echo.
echo [4/6] 创建财务和特殊功能表...
mysql -h localhost -P 3306 -u root -proot < init/04_create_finance_tables.sql
if errorlevel 1 (
    echo 错误：财务表创建失败
    pause
    exit /b 1
)
echo ✅ 财务表创建成功

echo.
echo [5/6] 初始化基础数据...
mysql -h localhost -P 3306 -u root -proot < init/05_init_data.sql
if errorlevel 1 (
    echo 错误：初始化数据失败
    pause
    exit /b 1
)
echo ✅ 初始化数据成功

echo.
echo [6/6] 创建索引优化...
mysql -h localhost -P 3306 -u root -proot < init/06_create_indexes.sql
if errorlevel 1 (
    echo 错误：索引创建失败
    pause
    exit /b 1
)
echo ✅ 索引创建成功

echo.
echo ================================
echo 数据库初始化完成！
echo ================================
echo.
echo 数据库信息：
echo - 数据库名：procurement_platform
echo - 字符集：utf8mb4
echo - 排序规则：utf8mb4_unicode_ci
echo.
echo 默认管理员账户：
echo - 用户名：admin
echo - 密码：admin123
echo - 请登录后立即修改密码！
echo.
echo 数据库表统计：
mysql -h localhost -P 3306 -u root -proot -e "USE procurement_platform; SELECT COUNT(*) as '总表数' FROM information_schema.TABLES WHERE TABLE_SCHEMA = 'procurement_platform';"
echo.
echo 可以开始启动后端服务了：
echo cd ../backend
echo mvn spring-boot:run
echo.
pause
