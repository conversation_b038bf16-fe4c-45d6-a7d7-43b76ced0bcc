# 采购数字化综合管理平台配置文件
server:
  port: 8080
  servlet:
    context-path: /api
    encoding:
      charset: UTF-8
      enabled: true
      force: true

spring:
  profiles:
    active: dev
  application:
    name: procurement-platform
  
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *************************************************************************************************************************************************************
    username: root
    password: root
    
    # Druid连接池配置
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      # 监控配置
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        login-username: admin
        login-password: admin123
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"

  # Redis配置
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0
    timeout: 10000ms
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 50MB

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null

# MyBatis Plus配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.procurement.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: ASSIGN_ID
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 日志配置
logging:
  level:
    com.procurement: debug
    org.springframework.security: debug
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n'
    file: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n'
  file:
    name: logs/procurement-platform.log

# 采购平台自定义配置
procurement:
  # JWT配置
  jwt:
    secret: procurement-platform-jwt-secret-key-2025
    expiration: 7200000  # 2小时
    refresh-expiration: 604800000  # 7天
    header: Authorization
    prefix: Bearer 
  
  # 文件存储配置
  file:
    upload-path: /uploads/
    max-size: 10485760  # 10MB
    allowed-types: jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx,zip,rar
  
  # 业务配置
  business:
    # 付款时限配置（工作日）
    payment:
      default-days: 30
      warning-days: 10,5,3,1
      max-days: 60
    
    # 双人制操作配置
    dual-person:
      enabled: true
      timeout-minutes: 30
      required-operations: PROJECT_CREATE,CONTRACT_SIGN,PAYMENT_APPROVE
    
    # 涉密项目配置
    classified:
      enabled: true
      encryption-algorithm: AES
      key-length: 256
      access-log-enabled: true
  
  # 系统配置
  system:
    name: 采购数字化综合管理平台
    version: 1.0.0
    company: 政府采购管理中心
    contact: <EMAIL>
