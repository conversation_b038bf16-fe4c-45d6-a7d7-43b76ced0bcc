# Augment User Guidelines - 采购数字化综合管理平台开发指南

## 项目概述

**项目名称**：采购数字化综合管理平台  
**开发环境**：Windows 10+  
**技术栈**：Spring Boot 2.7.x + Vue.js 3.x + MySQL 9.3 + Redis 6.x  
**数据库配置**：localhost/root/root  

## 开发目标

构建一个符合政府采购法规要求的企业级采购管理系统，支持：
- 采购全流程数字化管理
- 双人制操作和多人验收制度
- 涉密项目安全管控
- 分期付款精细化管理
- 智能预警和合规性监控

## 与Augment Agent协作指南

### 1. 项目初始化阶段

**明确告知Augment的信息：**
- 开发环境：Windows 10+
- 数据库信息：MySQL localhost/root/root
- 技术偏好：Spring Boot 2.7.x + Vue.js 3.x
- 特殊要求：双人制操作、涉密项目管理、付款时限控制

**请求Augment协助：**
```
"请帮我搭建采购数字化综合管理平台的开发环境，
使用Spring Boot 2.7.x + Vue.js 3.x技术栈，
数据库使用MySQL localhost/root/root配置"
```

### 2. 架构设计阶段

**向Augment描述业务需求：**
- 详细说明采购业务流程
- 强调合规性要求（双人制、多人验收）
- 说明涉密项目的特殊安全要求
- 明确付款时限管控的重要性

**请求设计建议：**
```
"请设计一个支持双人制操作的采购管理系统架构，
需要考虑涉密项目的安全隔离和付款时限的精确控制"
```

### 3. 数据库设计阶段

**提供业务实体信息：**
- 用户角色体系（采购员、财务人员、监督人员等）
- 采购项目生命周期
- 供应商管理要求
- 合同和付款管理需求

**请求数据库设计：**
```
"请设计采购管理系统的数据库表结构，
需要支持多期付款、双人制操作记录、涉密项目标识"
```

### 4. 功能开发阶段

**按模块请求开发：**

#### 用户认证模块
```
"请实现基于JWT的用户认证系统，
支持角色权限管理和涉密项目的特殊权限控制"
```

#### 采购管理模块
```
"请开发采购需求申报功能，
需要支持涉密项目标识和双人制审批流程"
```

#### 财务管理模块
```
"请实现分期付款管理功能，
包括付款时限计算和预警机制，
严格按照《保障中小企业款项支付条例》要求"
```

### 5. 测试和优化阶段

**请求测试支持：**
```
"请帮我编写采购流程的单元测试，
重点测试双人制操作和付款时限计算的准确性"
```

**请求性能优化：**
```
"请优化系统性能，确保支持200个并发用户，
页面响应时间不超过3秒"
```

## 沟通最佳实践

### 1. 明确具体需求
❌ 错误示例："帮我做一个采购系统"
✅ 正确示例："请实现采购项目的双人制创建功能，需要两名采购员分别确认才能创建项目"

### 2. 提供充分上下文
- 说明业务背景和合规要求
- 提供相关的法规条文
- 明确技术约束和环境限制

### 3. 分步骤请求
- 不要一次性要求完成整个系统
- 按功能模块逐步开发
- 每个模块完成后进行测试验证

### 4. 及时反馈和调整
- 测试Augment提供的代码
- 及时反馈问题和改进建议
- 根据实际需要调整需求

## 关键功能开发要点

### 1. 双人制操作实现
- 数据库设计：操作记录表，记录两人的操作信息
- 业务逻辑：强制要求两人确认的工作流
- 前端界面：双人操作的用户界面设计

### 2. 涉密项目管控
- 权限设计：基于项目属性的权限控制
- 数据加密：敏感数据的加密存储
- 访问审计：完整的操作日志记录

### 3. 付款时限管控
- 时间计算：工作日计算算法
- 预警机制：分级预警系统
- 自动化：定时任务和消息推送

### 4. 合规性保证
- 流程控制：严格按照法规要求设计流程
- 数据完整性：确保关键数据的完整性
- 审计追溯：所有操作可追溯

## 常见问题处理

### Q: 如何向Augment描述复杂的业务流程？
A: 使用流程图、表格或分步骤的文字描述，提供具体的业务场景和示例

### Q: Augment生成的代码不符合要求怎么办？
A: 详细说明问题所在，提供期望的结果，请求修改和优化

### Q: 如何确保代码质量？
A: 要求Augment添加注释、编写测试用例、遵循编码规范

### Q: 如何处理技术难点？
A: 将复杂问题分解为小问题，逐步解决，必要时请求多种解决方案

## 项目管理建议

### 1. 版本控制
- 使用Git管理代码版本
- 及时提交Augment生成的代码
- 做好分支管理和代码备份

### 2. 文档管理
- 保存与Augment的对话记录
- 维护详细的开发文档
- 记录重要的设计决策

### 3. 进度跟踪
- 制定详细的开发计划
- 定期评估开发进度
- 及时调整开发策略

### 4. 质量保证
- 每个功能完成后进行测试
- 定期进行代码审查
- 确保符合业务需求

## 成功标准

### 功能完整性
- ✅ 支持完整的采购业务流程
- ✅ 实现双人制和多人验收制度
- ✅ 涉密项目安全管控到位
- ✅ 付款时限精确控制

### 技术指标
- ✅ 支持200个并发用户
- ✅ 页面响应时间<3秒
- ✅ 系统可用性>99.5%
- ✅ 数据安全性符合要求

### 合规性
- ✅ 符合政府采购法规要求
- ✅ 满足内控制度要求
- ✅ 通过安全审计
- ✅ 用户满意度>8.5分

## 联系支持

如果在使用Augment开发过程中遇到问题：
1. 查阅本指南的相关章节
2. 检查项目文档和需求说明
3. 尝试重新描述需求给Augment
4. 记录问题和解决方案，完善开发经验

## 实用对话模板

### 环境配置类请求
```
"我需要在Windows 10环境下搭建采购管理系统的开发环境，
技术栈：Spring Boot 2.7.x + Vue.js 3.x + MySQL 9.3 + Redis 6.x
数据库配置：localhost/root/root
请提供详细的安装和配置步骤"
```

### 架构设计类请求
```
"请设计一个采购管理系统的微服务架构，
需要支持以下特殊要求：
1. 双人制操作（关键操作需要两人确认）
2. 涉密项目安全隔离
3. 付款时限精确控制（按工作日计算）
4. 多角色权限管理
请提供详细的架构图和技术选型建议"
```

### 数据库设计类请求
```
"请为采购管理系统设计数据库表结构，包括：
1. 用户和角色管理表
2. 采购项目管理表（支持涉密标识）
3. 供应商管理表
4. 合同和付款管理表（支持分期付款）
5. 操作日志表（记录双人制操作）
请提供完整的DDL语句和表关系图"
```

### 功能开发类请求
```
"请实现采购项目的双人制创建功能：
业务规则：
- 必须有两名采购员参与
- 主操作员创建项目草稿
- 副操作员确认后项目才能正式创建
- 记录完整的操作日志
技术要求：
- 使用Spring Boot + MyBatis Plus
- 前端使用Vue.js + Element Plus
- 包含完整的前后端代码和测试用例"
```

### 测试相关请求
```
"请为采购管理系统编写测试用例，重点测试：
1. 双人制操作的业务逻辑
2. 付款时限计算的准确性
3. 涉密项目的权限控制
4. 分期付款的数据一致性
请提供单元测试和集成测试代码"
```

## 代码质量要求

### 1. 编码规范
向Augment提出的代码质量要求：
```
"请确保生成的代码符合以下规范：
- Java代码遵循阿里巴巴Java开发手册
- 使用有意义的变量和方法命名
- 添加详细的注释说明
- 包含完整的异常处理
- 遵循RESTful API设计原则"
```

### 2. 安全要求
```
"请在代码中实现以下安全措施：
- SQL注入防护
- XSS攻击防护
- CSRF攻击防护
- 敏感数据加密存储
- 操作权限验证
- 详细的安全审计日志"
```

### 3. 性能要求
```
"请优化代码性能，确保：
- 数据库查询使用索引优化
- 实现Redis缓存机制
- 分页查询避免全表扫描
- 异步处理耗时操作
- 前端组件懒加载"
```

## 调试和问题解决

### 1. 问题描述模板
当遇到问题时，使用以下模板向Augment描述：
```
"我在开发[具体功能]时遇到问题：
问题现象：[详细描述问题表现]
错误信息：[完整的错误日志]
环境信息：[操作系统、浏览器、数据库版本等]
重现步骤：[详细的操作步骤]
期望结果：[期望的正确行为]
请帮助分析问题原因并提供解决方案"
```

### 2. 代码审查请求
```
"请审查以下代码的质量和安全性：
[粘贴代码]
重点关注：
1. 业务逻辑是否正确
2. 是否存在安全漏洞
3. 性能是否可以优化
4. 代码结构是否合理
请提供具体的改进建议"
```

### 3. 性能优化请求
```
"系统在[具体场景]下性能较慢，
当前响应时间：[具体时间]
期望响应时间：[目标时间]
请分析性能瓶颈并提供优化方案，
包括数据库优化、缓存策略、代码优化等"
```

## 部署和运维指导

### 1. 部署配置请求
```
"请提供采购管理系统的生产环境部署方案：
- Docker容器化部署配置
- Nginx反向代理配置
- MySQL数据库优化配置
- Redis缓存配置
- 系统监控和日志配置
- 备份和恢复策略"
```

### 2. 运维脚本请求
```
"请编写系统运维脚本：
1. 自动化部署脚本
2. 数据库备份脚本
3. 日志清理脚本
4. 系统健康检查脚本
5. 性能监控脚本
适用于Windows Server环境"
```

## 文档生成请求

### 1. API文档
```
"请为采购管理系统生成完整的API文档：
- 使用Swagger/OpenAPI格式
- 包含所有接口的详细说明
- 提供请求和响应示例
- 包含错误码说明
- 支持在线测试功能"
```

### 2. 用户手册
```
"请生成系统用户操作手册：
- 按角色分类的操作指南
- 详细的业务流程说明
- 常见问题解答
- 截图和操作步骤
- 故障排除指南"
```

### 3. 技术文档
```
"请生成技术文档：
- 系统架构设计文档
- 数据库设计文档
- 接口设计文档
- 部署运维文档
- 开发规范文档"
```

## 持续改进

### 1. 功能迭代
定期向Augment请求功能改进：
```
"基于用户反馈，需要优化以下功能：
[具体的改进需求]
请提供改进方案和实现代码"
```

### 2. 技术升级
```
"请帮助升级系统技术栈：
- Spring Boot升级到最新稳定版
- Vue.js升级到最新版本
- 数据库性能优化
- 安全性增强
请提供详细的升级方案和风险评估"
```

### 3. 新功能开发
```
"需要新增[具体功能]：
业务需求：[详细的业务描述]
技术要求：[技术实现要求]
集成要求：[与现有系统的集成方式]
请提供完整的设计和实现方案"
```

---

**使用建议**：
1. 保存常用的对话模板，提高沟通效率
2. 详细记录每次与Augment的对话，建立知识库
3. 定期总结开发经验，优化协作方式
4. 及时更新本指南，分享最佳实践

**注意事项**：
- 始终提供充分的上下文信息
- 明确表达具体需求和期望结果
- 及时测试和验证Augment提供的解决方案
- 保持耐心，复杂问题可能需要多轮对话解决

---

**版本信息**：
- 文档版本：1.0
- 创建日期：2025年6月29日
- 适用项目：采购数字化综合管理平台
- 更新计划：根据开发进展持续更新
