import request from '@/utils/request'
import type { LoginForm, LoginResponse, UserInfo, UserListItem, UserForm } from '@/types/user'

// 用户登录
export const login = (data: LoginForm) => {
  return request<LoginResponse>({
    url: '/auth/login',
    method: 'post',
    data
  })
}

// 获取用户信息
export const getUserInfo = () => {
  return request<{ userInfo: UserInfo; permissions: string[] }>({
    url: '/auth/userinfo',
    method: 'get'
  })
}

// 用户登出
export const logout = () => {
  return request({
    url: '/auth/logout',
    method: 'post'
  })
}

// 获取用户列表
export const getUserList = (params: any) => {
  return request<{ list: UserListItem[]; total: number }>({
    url: '/system/user/list',
    method: 'get',
    params
  })
}

// 创建用户
export const createUser = (data: UserForm) => {
  return request({
    url: '/system/user',
    method: 'post',
    data
  })
}

// 更新用户
export const updateUser = (id: number, data: UserForm) => {
  return request({
    url: `/system/user/${id}`,
    method: 'put',
    data
  })
}

// 删除用户
export const deleteUser = (id: number) => {
  return request({
    url: `/system/user/${id}`,
    method: 'delete'
  })
}

// 重置密码
export const resetPassword = (id: number, password: string) => {
  return request({
    url: `/system/user/${id}/password`,
    method: 'put',
    data: { password }
  })
}

// 修改用户状态
export const updateUserStatus = (id: number, status: number) => {
  return request({
    url: `/system/user/${id}/status`,
    method: 'put',
    data: { status }
  })
}
