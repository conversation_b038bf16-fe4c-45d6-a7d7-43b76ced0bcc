# Windows 10+ 开发环境安装指南
## 采购数字化综合管理平台开发环境配置

**适用系统：** Windows 10+ (64位)  
**技术栈：** Spring Boot 2.7.x + Vue.js 3.x + MySQL 9.3 + Redis 6.x  
**编写日期：** 2025年6月

---

## 📋 安装清单

### 必需软件列表
- ✅ **JDK 11+** - Java开发环境
- ✅ **Node.js 16.x+** - 前端开发环境
- ✅ **MySQL 8.0+** - 主数据库
- ✅ **Redis 6.x+** - 缓存数据库
- ✅ **Git** - 版本控制工具
- ✅ **IDE** - 开发工具 (IntelliJ IDEA / VS Code)

---

## 🚀 详细安装步骤

### 1. 安装 Java JDK 11

#### 1.1 下载 OpenJDK 21 (推荐最新LTS版本)
```
下载地址：https://adoptium.net/
选择版本：OpenJDK 21 (LTS) - 最新长期支持版本
操作系统：Windows x64
包类型：JDK (.msi 安装包)

备选版本：
- OpenJDK 17 (LTS) - 如果需要更保守的选择
- OpenJDK 11 (LTS) - 最低支持版本
```

#### 1.2 安装步骤
1. 双击下载的 `.msi` 文件
2. 按照安装向导完成安装
3. 默认安装路径：`C:\Program Files\Eclipse Adoptium\jdk-11.x.x.x-hotspot\`

#### 1.3 配置环境变量
```
1. 右键"此电脑" → "属性" → "高级系统设置" → "环境变量"
2. 在"系统变量"中新建：
   变量名：JAVA_HOME
   变量值：C:\Program Files\Eclipse Adoptium\jdk-21.x.x.x-hotspot
3. 编辑"Path"变量，添加：
   %JAVA_HOME%\bin
4. 点击"确定"保存

注意：路径中的版本号请根据实际安装的版本调整
```

#### 1.4 验证安装
```bash
# 打开命令提示符(cmd)或PowerShell
java -version
javac -version

# 预期输出类似：
# openjdk version "21.0.x" 2024-xx-xx
# OpenJDK Runtime Environment Temurin-21.0.x+x (build 21.0.x+x)
# OpenJDK 64-Bit Server VM Temurin-21.0.x+x (build 21.0.x+x, mixed mode, sharing)
```

### 2. 安装 Node.js

#### 2.1 下载 Node.js
```
下载地址：https://nodejs.org/
选择版本：LTS版本 (推荐 18.x 或 20.x)
操作系统：Windows Installer (.msi) 64-bit
```

#### 2.2 安装步骤
1. 双击下载的 `.msi` 文件
2. 按照安装向导完成安装
3. 确保勾选"Add to PATH"选项
4. 默认安装路径：`C:\Program Files\nodejs\`

#### 2.3 验证安装
```bash
# 打开命令提示符
node --version
npm --version

# 预期输出类似：
# v18.x.x
# 9.x.x
```

#### 2.4 配置npm镜像源(可选)
```bash
# 设置淘宝镜像源，提高下载速度
npm config set registry https://registry.npmmirror.com
```

### 3. 安装 MySQL 8.0

#### 3.1 下载 MySQL
```
下载地址：https://dev.mysql.com/downloads/mysql/
选择版本：MySQL Community Server 8.0.x
操作系统：Microsoft Windows
架构：Windows (x86, 64-bit), MSI Installer
```

#### 3.2 安装步骤
1. 双击下载的 `.msi` 文件
2. 选择"Custom"自定义安装
3. 选择安装组件：
   - MySQL Server 8.0.x
   - MySQL Workbench (可选，图形化管理工具)
   - MySQL Shell (可选)
4. 配置MySQL服务器：
   - 端口：3306 (默认)
   - 根密码：root (按您的要求设置)
   - 创建用户账户：root/root

#### 3.3 配置MySQL
```sql
-- 连接MySQL (使用MySQL Workbench或命令行)
-- 连接信息：
-- 主机：localhost
-- 端口：3306
-- 用户名：root
-- 密码：root

-- 创建项目数据库
CREATE DATABASE procurement_platform 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 验证数据库创建
SHOW DATABASES;
```

#### 3.4 验证安装
```bash
# 打开命令提示符
mysql --version

# 或者连接数据库测试
mysql -u root -p
# 输入密码：root
```

### 4. 安装 Redis

#### 4.1 方式一：使用 Windows 版本 Redis
```
下载地址：https://github.com/tporadowski/redis/releases
选择版本：Redis-x64-x.x.x.msi
```

安装步骤：
1. 双击下载的 `.msi` 文件
2. 按照安装向导完成安装
3. 默认端口：6379
4. 安装完成后Redis会自动启动为Windows服务

#### 4.2 方式二：使用 Docker (推荐)
```bash
# 1. 安装 Docker Desktop for Windows
# 下载地址：https://www.docker.com/products/docker-desktop

# 2. 安装完成后，运行Redis容器
docker run -d --name redis -p 6379:6379 redis:6-alpine

# 3. 验证Redis运行状态
docker ps
```

#### 4.3 验证安装
```bash
# 方式一：如果安装了Redis Windows版本
redis-cli ping
# 预期输出：PONG

# 方式二：如果使用Docker
docker exec -it redis redis-cli ping
# 预期输出：PONG
```

### 5. 安装 Git

#### 5.1 下载 Git
```
下载地址：https://git-scm.com/download/win
选择版本：64-bit Git for Windows Setup
```

#### 5.2 安装步骤
1. 双击下载的 `.exe` 文件
2. 安装选项推荐配置：
   - 选择组件：保持默认选择
   - 默认编辑器：选择您喜欢的编辑器
   - PATH环境：选择"Git from the command line and also from 3rd-party software"
   - HTTPS传输：选择"Use the OpenSSL library"
   - 行尾转换：选择"Checkout Windows-style, commit Unix-style line endings"
   - 终端模拟器：选择"Use MinTTY"

#### 5.3 验证安装
```bash
git --version
# 预期输出：git version 2.x.x.windows.x
```

### 6. 安装开发IDE

#### 6.1 IntelliJ IDEA (推荐用于后端开发)
```
下载地址：https://www.jetbrains.com/idea/download/#section=windows
版本选择：Community Edition (免费) 或 Ultimate Edition
```

安装插件推荐：
- Spring Boot
- MyBatis
- Lombok
- GitToolBox

#### 6.2 Visual Studio Code (推荐用于前端开发)
```
下载地址：https://code.visualstudio.com/download
选择版本：Windows x64 User Installer
```

安装插件推荐：
- Vue Language Features (Volar)
- TypeScript Vue Plugin (Volar)
- ESLint
- Prettier
- Auto Rename Tag
- Bracket Pair Colorizer
- GitLens

---

## 🔧 环境验证脚本

创建一个批处理文件来验证所有环境是否正确安装：

### 创建 `check_environment.bat`
```batch
@echo off
echo ================================
echo 检查开发环境安装状态
echo ================================

echo.
echo [1] 检查 Java 环境...
java -version
if %errorlevel% neq 0 (
    echo ❌ Java 未正确安装
) else (
    echo ✅ Java 安装正常
)

echo.
echo [2] 检查 Node.js 环境...
node --version
if %errorlevel% neq 0 (
    echo ❌ Node.js 未正确安装
) else (
    echo ✅ Node.js 安装正常
)

echo.
echo [3] 检查 npm 环境...
npm --version
if %errorlevel% neq 0 (
    echo ❌ npm 未正确安装
) else (
    echo ✅ npm 安装正常
)

echo.
echo [4] 检查 MySQL 环境...
mysql --version
if %errorlevel% neq 0 (
    echo ❌ MySQL 未正确安装或未添加到PATH
) else (
    echo ✅ MySQL 安装正常
)

echo.
echo [5] 检查 Git 环境...
git --version
if %errorlevel% neq 0 (
    echo ❌ Git 未正确安装
) else (
    echo ✅ Git 安装正常
)

echo.
echo [6] 检查 Redis 环境...
redis-cli ping
if %errorlevel% neq 0 (
    echo ❌ Redis 未正确安装或未启动
) else (
    echo ✅ Redis 安装正常
)

echo.
echo ================================
echo 环境检查完成
echo ================================
pause
```

---

## 📝 下一步操作

环境安装完成后，您可以：

1. **运行环境验证脚本**：确保所有软件正确安装
2. **创建项目目录结构**：准备开始项目开发
3. **配置IDE**：设置开发工具的项目配置
4. **初始化Git仓库**：开始版本控制

---

## 🆘 常见问题解决

### Java相关问题
**Q: java命令不被识别**
A: 检查JAVA_HOME环境变量和PATH配置是否正确

**Q: 版本不匹配**
A: 确保安装的是JDK 11或更高版本，不是JRE

### Node.js相关问题
**Q: npm安装包速度慢**
A: 配置国内镜像源：`npm config set registry https://registry.npmmirror.com`

**Q: 权限问题**
A: 以管理员身份运行命令提示符

### MySQL相关问题
**Q: 连接被拒绝**
A: 检查MySQL服务是否启动：`services.msc` → 查找MySQL80服务

**Q: 密码问题**
A: 重置root密码或检查密码是否为"root"

### Redis相关问题
**Q: 连接失败**
A: 检查Redis服务是否启动，或重启Docker容器

---

**安装指南结束**

> 如果在安装过程中遇到问题，请参考各软件的官方文档或联系技术支持。
