-- =============================================
-- 采购数字化综合管理平台完整数据库初始化脚本
-- 请在MySQL Workbench或命令提示符中执行
-- =============================================

-- 创建数据库
DROP DATABASE IF EXISTS procurement_platform;
CREATE DATABASE procurement_platform
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

USE procurement_platform;

-- =============================================
-- 用户表
-- =============================================
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码（加密）',
    real_name VARCHAR(100) NOT NULL COMMENT '真实姓名',
    employee_id VARCHAR(50) UNIQUE COMMENT '工号',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    department VARCHAR(100) COMMENT '部门',
    position VARCHAR(100) COMMENT '职位',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    last_login_time DATETIME COMMENT '最后登录时间',
    last_login_ip VARCHAR(50) COMMENT '最后登录IP',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人ID',
    update_by BIGINT COMMENT '更新人ID',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    version INT DEFAULT 1 COMMENT '版本号（乐观锁）',
    remark TEXT COMMENT '备注',
    INDEX idx_username (username),
    INDEX idx_employee_id (employee_id),
    INDEX idx_department (department),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- =============================================
-- 角色表
-- =============================================
CREATE TABLE roles (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '角色ID',
    role_code VARCHAR(50) NOT NULL UNIQUE COMMENT '角色编码',
    role_name VARCHAR(100) NOT NULL COMMENT '角色名称',
    description TEXT COMMENT '角色描述',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人ID',
    update_by BIGINT COMMENT '更新人ID',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    version INT DEFAULT 1 COMMENT '版本号（乐观锁）',
    remark TEXT COMMENT '备注',
    INDEX idx_role_code (role_code),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色表';

-- =============================================
-- 权限表
-- =============================================
CREATE TABLE permissions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '权限ID',
    permission_code VARCHAR(100) NOT NULL UNIQUE COMMENT '权限编码',
    permission_name VARCHAR(100) NOT NULL COMMENT '权限名称',
    permission_type VARCHAR(20) NOT NULL COMMENT '权限类型：MENU-菜单，BUTTON-按钮，API-接口',
    parent_id BIGINT DEFAULT 0 COMMENT '父权限ID',
    path VARCHAR(200) COMMENT '权限路径',
    component VARCHAR(200) COMMENT '组件路径',
    icon VARCHAR(100) COMMENT '图标',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人ID',
    update_by BIGINT COMMENT '更新人ID',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    version INT DEFAULT 1 COMMENT '版本号（乐观锁）',
    remark TEXT COMMENT '备注',
    INDEX idx_permission_code (permission_code),
    INDEX idx_parent_id (parent_id),
    INDEX idx_permission_type (permission_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限表';

-- =============================================
-- 用户角色关联表
-- =============================================
CREATE TABLE user_roles (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '关联ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by BIGINT COMMENT '创建人ID',
    UNIQUE KEY uk_user_role (user_id, role_id),
    INDEX idx_user_id (user_id),
    INDEX idx_role_id (role_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户角色关联表';

-- =============================================
-- 角色权限关联表
-- =============================================
CREATE TABLE role_permissions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '关联ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    permission_id BIGINT NOT NULL COMMENT '权限ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by BIGINT COMMENT '创建人ID',
    UNIQUE KEY uk_role_permission (role_id, permission_id),
    INDEX idx_role_id (role_id),
    INDEX idx_permission_id (permission_id),
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色权限关联表';

-- =============================================
-- 插入初始数据
-- =============================================

-- 插入默认管理员用户
INSERT INTO users (username, password, real_name, employee_id, email, phone, department, position, status, remark) VALUES
('admin', '$2a$10$7JB720yubVSOfvVWdBYoOeWpyAcXMsEiUHUOWpylsHVSuqiKVoFla', '系统管理员', 'ADMIN001', '<EMAIL>', '13800000000', '信息中心', '系统管理员', 1, '默认系统管理员'),
('procurement01', '$2a$10$7JB720yubVSOfvVWdBYoOeWpyAcXMsEiUHUOWpylsHVSuqiKVoFla', '张采购', 'PROC001', '<EMAIL>', '13800000001', '后勤服务中心', '采购员', 1, '主采购员'),
('procurement02', '$2a$10$7JB720yubVSOfvVWdBYoOeWpyAcXMsEiUHUOWpylsHVSuqiKVoFla', '李采购', 'PROC002', '<EMAIL>', '13800000002', '后勤服务中心', '采购员', 1, '副采购员');

-- 插入基础角色
INSERT INTO roles (role_code, role_name, description, status, remark) VALUES
('ADMIN', '系统管理员', '系统管理员，拥有所有权限', 1, '系统管理员角色'),
('PROCUREMENT_STAFF', '采购员', '采购实施人员，执行具体采购工作', 1, '采购员角色'),
('FINANCE_STAFF', '财务人员', '财务处理人员，负责付款处理', 1, '财务人员角色');

-- 插入基础权限
INSERT INTO permissions (permission_code, permission_name, permission_type, parent_id, status, remark) VALUES
('SYSTEM', '系统管理', 'MENU', 0, 1, '系统管理菜单'),
('PROCUREMENT', '采购管理', 'MENU', 0, 1, '采购管理菜单'),
('FINANCE', '财务管理', 'MENU', 0, 1, '财务管理菜单');

-- 分配用户角色
INSERT INTO user_roles (user_id, role_id) VALUES
(1, 1),  -- admin -> 系统管理员
(2, 2),  -- procurement01 -> 采购员
(3, 2);  -- procurement02 -> 采购员

-- 分配角色权限
INSERT INTO role_permissions (role_id, permission_id) VALUES
(1, 1),  -- 系统管理员 -> 系统管理
(1, 2),  -- 系统管理员 -> 采购管理
(1, 3),  -- 系统管理员 -> 财务管理
(2, 2),  -- 采购员 -> 采购管理
(3, 3);  -- 财务人员 -> 财务管理

-- =============================================
-- 完成信息
-- =============================================
SELECT '数据库初始化完成！' as '状态';
SELECT '默认管理员账户：admin/admin123' as '登录信息';
SELECT COUNT(*) as '用户数量' FROM users;
SELECT COUNT(*) as '角色数量' FROM roles;
SELECT COUNT(*) as '权限数量' FROM permissions;
