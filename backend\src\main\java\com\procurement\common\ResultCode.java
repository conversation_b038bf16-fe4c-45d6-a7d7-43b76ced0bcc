package com.procurement.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 响应码枚举
 * 
 * <AUTHOR> Platform Team
 * @version 1.0.0
 * @since 2025-06-29
 */
@Getter
@AllArgsConstructor
public enum ResultCode {

    // 通用响应码 (1000-1999)
    SUCCESS(200, "操作成功"),
    ERROR(500, "操作失败"),
    PARAM_ERROR(400, "参数错误"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    INTERNAL_ERROR(500, "系统内部错误"),

    // 认证授权相关 (2000-2999)
    UNAUTHORIZED(401, "未授权访问"),
    FORBIDDEN(403, "权限不足"),
    TOKEN_INVALID(2001, "Token无效"),
    TOKEN_EXPIRED(2002, "Token已过期"),
    LOGIN_FAILED(2003, "登录失败"),
    ACCOUNT_LOCKED(2004, "账户已锁定"),
    ACCOUNT_DISABLED(2005, "账户已禁用"),
    PASSWORD_ERROR(2006, "密码错误"),
    USER_NOT_FOUND(2007, "用户不存在"),
    PERMISSION_DENIED(2008, "权限不足"),

    // 业务相关 (3000-3999)
    BUSINESS_ERROR(3000, "业务处理失败"),
    DATA_NOT_FOUND(3001, "数据不存在"),
    DATA_ALREADY_EXISTS(3002, "数据已存在"),
    DATA_INTEGRITY_ERROR(3003, "数据完整性错误"),
    OPERATION_NOT_ALLOWED(3004, "操作不被允许"),

    // 采购业务相关 (4000-4999)
    PROCUREMENT_ERROR(4000, "采购业务错误"),
    PROJECT_NOT_FOUND(4001, "采购项目不存在"),
    PROJECT_STATUS_ERROR(4002, "项目状态错误"),
    DUAL_PERSON_REQUIRED(4003, "需要双人操作"),
    DUAL_PERSON_SAME_USER(4004, "双人操作不能是同一用户"),
    DUAL_PERSON_TIMEOUT(4005, "双人操作超时"),
    SUPPLIER_NOT_FOUND(4006, "供应商不存在"),
    CONTRACT_NOT_FOUND(4007, "合同不存在"),
    PAYMENT_AMOUNT_ERROR(4008, "付款金额错误"),
    PAYMENT_OVERDUE(4009, "付款已超期"),

    // 涉密项目相关 (5000-5999)
    CLASSIFIED_ERROR(5000, "涉密项目错误"),
    CLASSIFIED_ACCESS_DENIED(5001, "无涉密项目访问权限"),
    CLASSIFIED_OPERATION_DENIED(5002, "涉密项目操作被拒绝"),
    CLASSIFIED_DATA_ENCRYPTED(5003, "涉密数据已加密"),

    // 文件相关 (6000-6999)
    FILE_ERROR(6000, "文件处理错误"),
    FILE_NOT_FOUND(6001, "文件不存在"),
    FILE_SIZE_EXCEEDED(6002, "文件大小超出限制"),
    FILE_TYPE_NOT_ALLOWED(6003, "文件类型不允许"),
    FILE_UPLOAD_FAILED(6004, "文件上传失败"),
    FILE_DOWNLOAD_FAILED(6005, "文件下载失败"),

    // 验证相关 (7000-7999)
    VALIDATION_ERROR(7000, "数据验证失败"),
    REQUIRED_FIELD_MISSING(7001, "必填字段缺失"),
    FIELD_FORMAT_ERROR(7002, "字段格式错误"),
    FIELD_LENGTH_ERROR(7003, "字段长度错误"),
    FIELD_VALUE_ERROR(7004, "字段值错误"),

    // 系统相关 (8000-8999)
    SYSTEM_ERROR(8000, "系统错误"),
    DATABASE_ERROR(8001, "数据库错误"),
    REDIS_ERROR(8002, "Redis错误"),
    NETWORK_ERROR(8003, "网络错误"),
    TIMEOUT_ERROR(8004, "请求超时"),
    RATE_LIMIT_ERROR(8005, "请求频率超限"),

    // 第三方服务相关 (9000-9999)
    THIRD_PARTY_ERROR(9000, "第三方服务错误"),
    SMS_SEND_FAILED(9001, "短信发送失败"),
    EMAIL_SEND_FAILED(9002, "邮件发送失败"),
    PAYMENT_SERVICE_ERROR(9003, "支付服务错误");

    /**
     * 响应码
     */
    private final Integer code;

    /**
     * 响应消息
     */
    private final String message;

    /**
     * 根据code获取枚举
     */
    public static ResultCode getByCode(Integer code) {
        for (ResultCode resultCode : values()) {
            if (resultCode.getCode().equals(code)) {
                return resultCode;
            }
        }
        return ERROR;
    }
}
