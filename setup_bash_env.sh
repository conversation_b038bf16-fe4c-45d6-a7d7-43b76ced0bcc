#!/bin/bash
echo "================================"
echo "Setting up Git Bash Environment"
echo "Procurement Platform"
echo "================================"

# Set JAVA_HOME for Git Bash
export JAVA_HOME="/c/Program Files/Eclipse Adoptium/jdk-********-hotspot"
echo "JAVA_HOME = $JAVA_HOME"

# Set MAVEN_HOME for Git Bash  
export MAVEN_HOME="/c/Program Files/Apache/maven"
echo "MAVEN_HOME = $MAVEN_HOME"

# Add to PATH
export PATH="$JAVA_HOME/bin:$MAVEN_HOME/bin:$PATH"
echo "PATH updated"

echo ""
echo "Testing Java..."
"$JAVA_HOME/bin/java" -version

echo ""
echo "Testing Maven..."
"$MAVEN_HOME/bin/mvn" -version

echo ""
echo "================================"
echo "Environment setup complete"
echo "================================"
echo ""
echo "You can now run:"
echo "  mvn clean compile"
echo "  mvn spring-boot:run"
echo ""
