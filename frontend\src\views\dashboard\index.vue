<template>
  <div class="dashboard">
    <el-row :gutter="20">
      <!-- 统计卡片 -->
      <el-col :span="6" v-for="item in statsCards" :key="item.title">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-info">
              <h3>{{ item.value }}</h3>
              <p>{{ item.title }}</p>
            </div>
            <div class="stats-icon" :style="{ backgroundColor: item.color }">
              <el-icon :size="24">
                <component :is="item.icon" />
              </el-icon>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 快捷操作 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>快捷操作</span>
            </div>
          </template>
          <div class="quick-actions">
            <el-button
              v-for="action in quickActions"
              :key="action.name"
              :type="action.type"
              :icon="action.icon"
              @click="handleQuickAction(action.action)"
            >
              {{ action.name }}
            </el-button>
          </div>
        </el-card>
      </el-col>
      
      <!-- 系统信息 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>系统信息</span>
            </div>
          </template>
          <div class="system-info">
            <div class="info-item">
              <span class="label">当前用户：</span>
              <span class="value">{{ userStore.userInfo?.realName }}</span>
            </div>
            <div class="info-item">
              <span class="label">所属部门：</span>
              <span class="value">{{ userStore.userInfo?.department }}</span>
            </div>
            <div class="info-item">
              <span class="label">用户角色：</span>
              <span class="value">{{ userStore.userInfo?.position }}</span>
            </div>
            <div class="info-item">
              <span class="label">最后登录：</span>
              <span class="value">{{ formatTime(userStore.userInfo?.lastLoginTime) }}</span>
            </div>
            <div class="info-item">
              <span class="label">系统版本：</span>
              <span class="value">v1.0.0</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 待办事项 -->
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>待办事项</span>
              <el-button type="primary" size="small">查看全部</el-button>
            </div>
          </template>
          <el-table :data="todoList" style="width: 100%">
            <el-table-column prop="title" label="事项" />
            <el-table-column prop="type" label="类型" width="120">
              <template #default="{ row }">
                <el-tag :type="getTypeColor(row.type)">{{ row.type }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="priority" label="优先级" width="120">
              <template #default="{ row }">
                <el-tag :type="getPriorityColor(row.priority)">{{ row.priority }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="deadline" label="截止时间" width="180" />
            <el-table-column label="操作" width="120">
              <template #default="{ row }">
                <el-button type="primary" size="small" @click="handleTodo(row)">
                  处理
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'
import dayjs from 'dayjs'

const router = useRouter()
const userStore = useUserStore()

// 统计卡片数据
const statsCards = ref([
  {
    title: '待处理需求',
    value: '12',
    icon: 'Document',
    color: '#409eff'
  },
  {
    title: '进行中项目',
    value: '8',
    icon: 'Folder',
    color: '#67c23a'
  },
  {
    title: '待签合同',
    value: '5',
    icon: 'DocumentCopy',
    color: '#e6a23c'
  },
  {
    title: '待付款项',
    value: '3',
    icon: 'Money',
    color: '#f56c6c'
  }
])

// 快捷操作
const quickActions = ref([
  {
    name: '新建需求',
    type: 'primary',
    icon: 'Plus',
    action: 'createRequirement'
  },
  {
    name: '创建项目',
    type: 'success',
    icon: 'FolderAdd',
    action: 'createProject'
  },
  {
    name: '供应商管理',
    type: 'warning',
    icon: 'User',
    action: 'manageSupplier'
  },
  {
    name: '合同管理',
    type: 'info',
    icon: 'Document',
    action: 'manageContract'
  }
])

// 待办事项
const todoList = ref([
  {
    id: 1,
    title: '办公用品采购需求审批',
    type: '需求审批',
    priority: '高',
    deadline: '2025-06-30 18:00'
  },
  {
    id: 2,
    title: '计算机设备采购项目验收',
    type: '项目验收',
    priority: '中',
    deadline: '2025-07-01 12:00'
  },
  {
    id: 3,
    title: '办公家具供应商报价评审',
    type: '报价评审',
    priority: '中',
    deadline: '2025-07-02 15:00'
  },
  {
    id: 4,
    title: '软件服务合同付款审批',
    type: '付款审批',
    priority: '高',
    deadline: '2025-06-29 17:00'
  }
])

// 格式化时间
const formatTime = (time?: string) => {
  if (!time) return '-'
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

// 获取类型颜色
const getTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    '需求审批': 'primary',
    '项目验收': 'success',
    '报价评审': 'warning',
    '付款审批': 'danger'
  }
  return colorMap[type] || 'info'
}

// 获取优先级颜色
const getPriorityColor = (priority: string) => {
  const colorMap: Record<string, string> = {
    '高': 'danger',
    '中': 'warning',
    '低': 'info'
  }
  return colorMap[priority] || 'info'
}

// 处理快捷操作
const handleQuickAction = (action: string) => {
  switch (action) {
    case 'createRequirement':
      router.push('/procurement/requirement')
      break
    case 'createProject':
      router.push('/procurement/project')
      break
    case 'manageSupplier':
      ElMessage.info('供应商管理功能开发中...')
      break
    case 'manageContract':
      ElMessage.info('合同管理功能开发中...')
      break
    default:
      ElMessage.info('功能开发中...')
  }
}

// 处理待办事项
const handleTodo = (todo: any) => {
  ElMessage.info(`处理待办事项：${todo.title}`)
}

onMounted(() => {
  // 可以在这里加载实际的统计数据
})
</script>

<style lang="scss" scoped>
.dashboard {
  .stats-card {
    .stats-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      .stats-info {
        h3 {
          font-size: 28px;
          font-weight: bold;
          color: #303133;
          margin: 0 0 8px 0;
        }
        
        p {
          font-size: 14px;
          color: #909399;
          margin: 0;
        }
      }
      
      .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
      }
    }
  }
  
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  
  .quick-actions {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    
    .el-button {
      height: 60px;
      font-size: 16px;
    }
  }
  
  .system-info {
    .info-item {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .label {
        width: 100px;
        color: #909399;
        font-size: 14px;
      }
      
      .value {
        color: #303133;
        font-size: 14px;
        font-weight: 500;
      }
    }
  }
}
</style>
