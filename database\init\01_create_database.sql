-- =============================================
-- 采购数字化综合管理平台数据库初始化脚本
-- 创建数据库和基础配置
-- =============================================

-- 创建数据库
DROP DATABASE IF EXISTS procurement_platform;
CREATE DATABASE procurement_platform 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci
COMMENT '采购数字化综合管理平台数据库';

-- 使用数据库
USE procurement_platform;

-- 显示数据库信息
SELECT 
    SCHEMA_NAME as '数据库名',
    DEFAULT_CHARACTER_SET_NAME as '字符集',
    DEFAULT_COLLATION_NAME as '排序规则'
FROM information_schema.SCHEMATA 
WHERE SCHEMA_NAME = 'procurement_platform';

-- 创建数据库用户（如果需要）
-- CREATE USER 'procurement_user'@'localhost' IDENTIFIED BY 'procurement_password';
-- GRANT ALL PRIVILEGES ON procurement_platform.* TO 'procurement_user'@'localhost';
-- FLUSH PRIVILEGES;

SELECT '数据库创建成功！' as '状态';
