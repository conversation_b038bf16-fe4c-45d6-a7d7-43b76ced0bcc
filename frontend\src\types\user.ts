// 用户相关类型定义

export interface LoginForm {
  username: string
  password: string
  captcha?: string
}

export interface UserInfo {
  id: number
  username: string
  realName: string
  employeeId: string
  email: string
  phone: string
  department: string
  position: string
  avatar?: string
  roles: string[]
  lastLoginTime?: string
  lastLoginIp?: string
}

export interface LoginResponse {
  token: string
  userInfo: UserInfo
  permissions: string[]
}

export interface UserListItem {
  id: number
  username: string
  realName: string
  employeeId: string
  email: string
  phone: string
  department: string
  position: string
  status: number
  createTime: string
  lastLoginTime?: string
}

export interface UserForm {
  id?: number
  username: string
  password?: string
  realName: string
  employeeId: string
  email: string
  phone: string
  department: string
  position: string
  status: number
  roleIds: number[]
}

export interface Role {
  id: number
  roleCode: string
  roleName: string
  description: string
  status: number
  createTime: string
}

export interface Permission {
  id: number
  permissionCode: string
  permissionName: string
  permissionType: string
  parentId: number
  path?: string
  component?: string
  icon?: string
  sortOrder: number
  status: number
  children?: Permission[]
}
