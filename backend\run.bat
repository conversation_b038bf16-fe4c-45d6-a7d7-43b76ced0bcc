@echo off
echo ================================
echo 采购数字化综合管理平台后端服务
echo ================================
echo.

echo 检查Java环境...
java -version
if errorlevel 1 (
    echo 错误：Java环境未配置
    pause
    exit /b 1
)
echo.

echo 检查Maven环境...
mvn -version
if errorlevel 1 (
    echo 错误：Maven环境未配置
    pause
    exit /b 1
)
echo.

echo 开始编译项目...
mvn clean compile
if errorlevel 1 (
    echo 错误：项目编译失败
    pause
    exit /b 1
)
echo ✅ 项目编译成功
echo.

echo 启动Spring Boot应用...
echo 访问地址：http://localhost:8080
echo 健康检查：http://localhost:8080/health
echo 系统信息：http://localhost:8080/health/info
echo 按 Ctrl+C 停止服务
echo.
mvn spring-boot:run
