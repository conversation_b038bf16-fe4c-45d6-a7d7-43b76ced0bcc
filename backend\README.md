# 采购数字化综合管理平台 - 后端服务

## 项目简介

采购数字化综合管理平台后端服务，基于Spring Boot 2.7.x构建，提供完整的采购管理业务API接口。

## 技术栈

- **框架**: Spring Boot 2.7.18
- **安全**: Spring Security
- **数据库**: MySQL 8.0+
- **ORM**: MyBatis Plus 3.5.3.1
- **缓存**: Redis 6.x
- **连接池**: Druid
- **认证**: JWT
- **工具**: Hutool, FastJSON
- **构建**: Maven 3.6+
- **JDK**: 21+ (推荐最新LTS版本)

## 项目结构

```
backend/
├── src/main/java/com/procurement/
│   ├── ProcurementApplication.java     # 主启动类
│   ├── common/                         # 通用类
│   │   ├── BaseEntity.java            # 基础实体类
│   │   ├── Result.java                # 统一响应结果
│   │   ├── ResultCode.java            # 响应码枚举
│   │   ├── PageResult.java            # 分页响应结果
│   │   ├── BusinessException.java     # 业务异常类
│   │   └── GlobalExceptionHandler.java # 全局异常处理器
│   ├── config/                        # 配置类
│   │   ├── MybatisPlusConfig.java     # MyBatis Plus配置
│   │   ├── RedisConfig.java           # Redis配置
│   │   ├── CorsConfig.java            # 跨域配置
│   │   └── WebConfig.java             # Web配置
│   ├── controller/                    # 控制器层
│   ├── service/                       # 业务逻辑层
│   ├── mapper/                        # 数据访问层
│   ├── entity/                        # 实体类
│   ├── dto/                           # 数据传输对象
│   ├── vo/                            # 视图对象
│   ├── enums/                         # 枚举类
│   ├── utils/                         # 工具类
│   └── security/                      # 安全相关
├── src/main/resources/
│   ├── application.yml                # 主配置文件
│   ├── application-dev.yml            # 开发环境配置
│   ├── application-prod.yml           # 生产环境配置
│   └── mapper/                        # MyBatis XML文件
└── pom.xml                            # Maven配置文件
```

## 环境要求

### 开发环境
- JDK 21+ (推荐OpenJDK 21 LTS)
- Maven 3.6+
- MySQL 8.0+
- Redis 6.0+
- IDE: IntelliJ IDEA (推荐)

### 数据库配置
```yaml
# 开发环境数据库配置
spring:
  datasource:
    url: ************************************************
    username: root
    password: root
```

## 快速开始

### 1. 环境准备
确保已安装并启动：
- MySQL 8.0+
- Redis 6.0+

### 2. 数据库初始化
```sql
-- 创建数据库
CREATE DATABASE procurement_platform 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;
```

### 3. 克隆项目
```bash
git clone <repository-url>
cd caigoupingtai/backend
```

### 4. 配置修改
根据实际环境修改 `application-dev.yml` 中的数据库连接信息。

### 5. 启动项目
```bash
# 使用Maven启动
mvn spring-boot:run

# 或者使用IDE直接运行ProcurementApplication.java
```

### 6. 验证启动
访问健康检查接口：
```
GET http://localhost:8080/api/health
```

预期响应：
```json
{
  "code": 200,
  "message": "健康检查通过",
  "data": {
    "status": "UP",
    "systemName": "采购数字化综合管理平台",
    "version": "1.0.0",
    "timestamp": "2025-06-29T10:00:00",
    "message": "系统运行正常"
  },
  "timestamp": 1719633600000
}
```

## 配置说明

### 核心配置
- **服务端口**: 8080
- **上下文路径**: /api
- **数据库**: MySQL (localhost:3306)
- **Redis**: localhost:6379
- **JWT密钥**: 可在配置文件中自定义

### 环境配置
- **开发环境**: application-dev.yml
- **生产环境**: application-prod.yml
- **激活环境**: spring.profiles.active

### 业务配置
```yaml
procurement:
  jwt:
    expiration: 7200000  # JWT过期时间(2小时)
  business:
    payment:
      default-days: 30   # 默认付款期限(工作日)
    dual-person:
      enabled: true      # 启用双人制操作
    classified:
      enabled: true      # 启用涉密项目管理
```

## 开发规范

### 代码规范
- 遵循阿里巴巴Java开发手册
- 使用Lombok简化代码
- 统一异常处理
- 完善的注释说明

### API规范
- RESTful API设计
- 统一响应格式
- 完整的参数验证
- 详细的错误码定义

### 数据库规范
- 统一字段命名(下划线)
- 必要的索引设计
- 逻辑删除
- 乐观锁控制

## 监控与运维

### 健康检查
- `/api/health` - 系统健康状态
- `/api/health/info` - 系统详细信息
- `/api/health/version` - 版本信息

### 数据库监控
- Druid监控: http://localhost:8080/api/druid/
- 用户名: admin
- 密码: admin123

### 日志配置
- 开发环境: 控制台输出 + 文件输出
- 生产环境: 文件输出
- 日志级别: 可配置

## 部署说明

### 开发环境部署
```bash
# 启动开发环境
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

### 生产环境部署
```bash
# 打包
mvn clean package -Dmaven.test.skip=true

# 启动
java -jar target/procurement-platform-1.0.0.jar --spring.profiles.active=prod
```

### Docker部署
```dockerfile
# 后续提供Docker配置
```

## 常见问题

### Q: 启动时数据库连接失败？
A: 检查MySQL服务是否启动，数据库是否存在，用户名密码是否正确。

### Q: Redis连接失败？
A: 检查Redis服务是否启动，端口是否正确。

### Q: 端口被占用？
A: 修改application.yml中的server.port配置。

## 联系方式

- 项目团队: Procurement Platform Team
- 邮箱: <EMAIL>
- 文档: 详见项目docs目录

## 更新日志

### v1.0.0 (2025-06-29)
- 初始版本发布
- 基础框架搭建完成
- 核心配置和通用组件实现
