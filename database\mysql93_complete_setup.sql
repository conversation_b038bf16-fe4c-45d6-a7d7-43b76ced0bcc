-- =============================================
-- 采购数字化综合管理平台完整数据库初始化
-- 专门针对MySQL 9.3优化版本
-- =============================================

-- 创建数据库
DROP DATABASE IF EXISTS procurement_platform;
CREATE DATABASE procurement_platform 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE procurement_platform;

-- =============================================
-- 1. 用户管理表
-- =============================================
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    real_name VARCHAR(100) NOT NULL,
    employee_id VARCHAR(50) UNIQUE,
    email VARCHAR(100),
    phone VARCHAR(20),
    department VARCHAR(100),
    position VARCHAR(100),
    status TINYINT DEFAULT 1,
    last_login_time DATETIME,
    last_login_ip VARCHAR(50),
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    create_by BIGINT,
    update_by BIGINT,
    deleted TINYINT DEFAULT 0,
    version INT DEFAULT 1,
    remark TEXT,
    INDEX idx_username (username),
    INDEX idx_employee_id (employee_id),
    INDEX idx_department (department),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 2. 角色管理表
-- =============================================
CREATE TABLE roles (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    role_code VARCHAR(50) NOT NULL UNIQUE,
    role_name VARCHAR(100) NOT NULL,
    description TEXT,
    status TINYINT DEFAULT 1,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    create_by BIGINT,
    update_by BIGINT,
    deleted TINYINT DEFAULT 0,
    version INT DEFAULT 1,
    remark TEXT,
    INDEX idx_role_code (role_code),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 3. 权限管理表
-- =============================================
CREATE TABLE permissions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    permission_code VARCHAR(100) NOT NULL UNIQUE,
    permission_name VARCHAR(100) NOT NULL,
    permission_type VARCHAR(20) NOT NULL,
    parent_id BIGINT DEFAULT 0,
    path VARCHAR(200),
    component VARCHAR(200),
    icon VARCHAR(100),
    sort_order INT DEFAULT 0,
    status TINYINT DEFAULT 1,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    create_by BIGINT,
    update_by BIGINT,
    deleted TINYINT DEFAULT 0,
    version INT DEFAULT 1,
    remark TEXT,
    INDEX idx_permission_code (permission_code),
    INDEX idx_parent_id (parent_id),
    INDEX idx_permission_type (permission_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 4. 用户角色关联表
-- =============================================
CREATE TABLE user_roles (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    create_by BIGINT,
    UNIQUE KEY uk_user_role (user_id, role_id),
    INDEX idx_user_id (user_id),
    INDEX idx_role_id (role_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 5. 角色权限关联表
-- =============================================
CREATE TABLE role_permissions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    role_id BIGINT NOT NULL,
    permission_id BIGINT NOT NULL,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    create_by BIGINT,
    UNIQUE KEY uk_role_permission (role_id, permission_id),
    INDEX idx_role_id (role_id),
    INDEX idx_permission_id (permission_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 6. 供应商管理表
-- =============================================
CREATE TABLE suppliers (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    supplier_code VARCHAR(50) NOT NULL UNIQUE,
    supplier_name VARCHAR(200) NOT NULL,
    supplier_type VARCHAR(50),
    legal_person VARCHAR(100),
    contact_person VARCHAR(100),
    contact_phone VARCHAR(20),
    contact_email VARCHAR(100),
    address TEXT,
    business_license VARCHAR(100),
    tax_number VARCHAR(100),
    bank_name VARCHAR(200),
    bank_account VARCHAR(100),
    business_scope TEXT,
    qualification_level VARCHAR(50),
    credit_rating VARCHAR(20),
    cooperation_status VARCHAR(20) DEFAULT 'NORMAL',
    status TINYINT DEFAULT 1,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    create_by BIGINT,
    update_by BIGINT,
    deleted TINYINT DEFAULT 0,
    version INT DEFAULT 1,
    remark TEXT,
    INDEX idx_supplier_code (supplier_code),
    INDEX idx_supplier_name (supplier_name),
    INDEX idx_supplier_type (supplier_type),
    INDEX idx_cooperation_status (cooperation_status),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 7. 采购需求表
-- =============================================
CREATE TABLE procurement_requirements (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    requirement_no VARCHAR(50) NOT NULL UNIQUE,
    requirement_name VARCHAR(200) NOT NULL,
    requirement_type VARCHAR(50) NOT NULL,
    department VARCHAR(100) NOT NULL,
    applicant_id BIGINT NOT NULL,
    applicant_name VARCHAR(100) NOT NULL,
    urgency_level VARCHAR(20) DEFAULT 'NORMAL',
    expected_amount DECIMAL(15,2),
    expected_date DATE,
    usage_purpose TEXT NOT NULL,
    technical_requirements TEXT,
    quality_standards TEXT,
    delivery_requirements TEXT,
    is_classified TINYINT DEFAULT 0,
    classified_level VARCHAR(20),
    approval_status VARCHAR(20) DEFAULT 'PENDING',
    approver_id BIGINT,
    approver_name VARCHAR(100),
    approval_time DATETIME,
    approval_opinion TEXT,
    status TINYINT DEFAULT 1,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    create_by BIGINT,
    update_by BIGINT,
    deleted TINYINT DEFAULT 0,
    version INT DEFAULT 1,
    remark TEXT,
    INDEX idx_requirement_no (requirement_no),
    INDEX idx_requirement_type (requirement_type),
    INDEX idx_department (department),
    INDEX idx_applicant_id (applicant_id),
    INDEX idx_urgency_level (urgency_level),
    INDEX idx_is_classified (is_classified),
    INDEX idx_approval_status (approval_status),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 8. 需求明细表
-- =============================================
CREATE TABLE requirement_items (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    requirement_id BIGINT NOT NULL,
    item_name VARCHAR(200) NOT NULL,
    item_specification TEXT,
    item_brand VARCHAR(100),
    quantity DECIMAL(10,2) NOT NULL,
    unit VARCHAR(20) NOT NULL,
    estimated_price DECIMAL(10,2),
    estimated_total DECIMAL(15,2),
    technical_params TEXT,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    create_by BIGINT,
    update_by BIGINT,
    deleted TINYINT DEFAULT 0,
    version INT DEFAULT 1,
    INDEX idx_requirement_id (requirement_id),
    INDEX idx_item_name (item_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 9. 采购项目表
-- =============================================
CREATE TABLE procurement_projects (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    project_no VARCHAR(50) NOT NULL UNIQUE,
    project_name VARCHAR(200) NOT NULL,
    requirement_id BIGINT NOT NULL,
    project_type VARCHAR(50) NOT NULL,
    procurement_method VARCHAR(50) NOT NULL,
    procurement_organization VARCHAR(50),
    is_government_procurement TINYINT DEFAULT 0,
    budget_amount DECIMAL(15,2) NOT NULL,
    is_classified TINYINT DEFAULT 0,
    classified_level VARCHAR(20),
    primary_purchaser_id BIGINT NOT NULL,
    primary_purchaser_name VARCHAR(100) NOT NULL,
    secondary_purchaser_id BIGINT NOT NULL,
    secondary_purchaser_name VARCHAR(100) NOT NULL,
    supervisor_id BIGINT,
    supervisor_name VARCHAR(100),
    project_status VARCHAR(20) DEFAULT 'CREATED',
    start_date DATE,
    end_date DATE,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    create_by BIGINT,
    update_by BIGINT,
    deleted TINYINT DEFAULT 0,
    version INT DEFAULT 1,
    remark TEXT,
    INDEX idx_project_no (project_no),
    INDEX idx_requirement_id (requirement_id),
    INDEX idx_project_type (project_type),
    INDEX idx_procurement_method (procurement_method),
    INDEX idx_is_classified (is_classified),
    INDEX idx_primary_purchaser_id (primary_purchaser_id),
    INDEX idx_secondary_purchaser_id (secondary_purchaser_id),
    INDEX idx_project_status (project_status),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 10. 供应商报价表
-- =============================================
CREATE TABLE supplier_quotes (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    project_id BIGINT NOT NULL,
    supplier_id BIGINT NOT NULL,
    quote_no VARCHAR(50) NOT NULL,
    quote_amount DECIMAL(15,2) NOT NULL,
    quote_date DATE NOT NULL,
    delivery_period INT,
    payment_terms TEXT,
    warranty_period INT,
    technical_proposal TEXT,
    service_commitment TEXT,
    quote_validity_days INT DEFAULT 30,
    is_selected TINYINT DEFAULT 0,
    selection_reason TEXT,
    quote_status VARCHAR(20) DEFAULT 'SUBMITTED',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    create_by BIGINT,
    update_by BIGINT,
    deleted TINYINT DEFAULT 0,
    version INT DEFAULT 1,
    remark TEXT,
    INDEX idx_project_id (project_id),
    INDEX idx_supplier_id (supplier_id),
    INDEX idx_quote_no (quote_no),
    INDEX idx_is_selected (is_selected),
    INDEX idx_quote_status (quote_status),
    INDEX idx_quote_date (quote_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 11. 合同表
-- =============================================
CREATE TABLE contracts (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    contract_no VARCHAR(50) NOT NULL UNIQUE,
    contract_name VARCHAR(200) NOT NULL,
    project_id BIGINT NOT NULL,
    supplier_id BIGINT NOT NULL,
    quote_id BIGINT,
    contract_type VARCHAR(50) NOT NULL,
    contract_amount DECIMAL(15,2) NOT NULL,
    currency VARCHAR(10) DEFAULT 'CNY',
    signing_date DATE NOT NULL,
    effective_date DATE NOT NULL,
    delivery_date DATE,
    completion_date DATE,
    warranty_period INT,
    payment_method VARCHAR(50),
    contract_terms TEXT,
    delivery_terms TEXT,
    quality_standards TEXT,
    breach_liability TEXT,
    dispute_resolution TEXT,
    contract_status VARCHAR(20) DEFAULT 'DRAFT',
    performance_status VARCHAR(20) DEFAULT 'NOT_STARTED',
    supervisor_id BIGINT,
    supervisor_name VARCHAR(100),
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    create_by BIGINT,
    update_by BIGINT,
    deleted TINYINT DEFAULT 0,
    version INT DEFAULT 1,
    remark TEXT,
    INDEX idx_contract_no (contract_no),
    INDEX idx_project_id (project_id),
    INDEX idx_supplier_id (supplier_id),
    INDEX idx_contract_type (contract_type),
    INDEX idx_signing_date (signing_date),
    INDEX idx_contract_status (contract_status),
    INDEX idx_performance_status (performance_status),
    INDEX idx_supervisor_id (supervisor_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 12. 付款计划表
-- =============================================
CREATE TABLE payment_plans (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    plan_no VARCHAR(50) NOT NULL UNIQUE,
    contract_id BIGINT NOT NULL,
    total_amount DECIMAL(15,2) NOT NULL,
    payment_method VARCHAR(50) NOT NULL,
    installment_count INT DEFAULT 1,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    create_by BIGINT,
    update_by BIGINT,
    deleted TINYINT DEFAULT 0,
    version INT DEFAULT 1,
    remark TEXT,
    INDEX idx_plan_no (plan_no),
    INDEX idx_contract_id (contract_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 13. 付款明细表
-- =============================================
CREATE TABLE payment_details (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    plan_id BIGINT NOT NULL,
    installment_no INT NOT NULL,
    payment_amount DECIMAL(15,2) NOT NULL,
    payment_ratio DECIMAL(5,2) NOT NULL,
    planned_date DATE NOT NULL,
    payment_condition TEXT,
    payment_status VARCHAR(20) DEFAULT 'PENDING',
    due_date DATE NOT NULL,
    working_days_limit INT DEFAULT 30,
    warning_days VARCHAR(50) DEFAULT '10,5,3,1',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    create_by BIGINT,
    update_by BIGINT,
    deleted TINYINT DEFAULT 0,
    version INT DEFAULT 1,
    INDEX idx_plan_id (plan_id),
    INDEX idx_installment_no (installment_no),
    INDEX idx_payment_status (payment_status),
    INDEX idx_due_date (due_date),
    INDEX idx_planned_date (planned_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 初始化数据
-- =============================================

-- 插入默认用户（密码：admin123，已加密）
INSERT INTO users (username, password, real_name, employee_id, email, phone, department, position, status, remark) VALUES
('admin', '$2a$10$7JB720yubVSOfvVWdBYoOeWpyAcXMsEiUHUOWpylsHVSuqiKVoFla', '系统管理员', 'ADMIN001', '<EMAIL>', '13800000000', '信息中心', '系统管理员', 1, '默认系统管理员'),
('procurement01', '$2a$10$7JB720yubVSOfvVWdBYoOeWpyAcXMsEiUHUOWpylsHVSuqiKVoFla', '张采购', 'PROC001', '<EMAIL>', '13800000001', '后勤服务中心', '采购员', 1, '主采购员'),
('procurement02', '$2a$10$7JB720yubVSOfvVWdBYoOeWpyAcXMsEiUHUOWpylsHVSuqiKVoFla', '李采购', 'PROC002', '<EMAIL>', '13800000002', '后勤服务中心', '采购员', 1, '副采购员'),
('finance01', '$2a$10$7JB720yubVSOfvVWdBYoOeWpyAcXMsEiUHUOWpylsHVSuqiKVoFla', '王财务', 'FIN001', '<EMAIL>', '13800000003', '财务科', '财务主管', 1, '财务主管'),
('supervisor01', '$2a$10$7JB720yubVSOfvVWdBYoOeWpyAcXMsEiUHUOWpylsHVSuqiKVoFla', '刘监督', 'SUP001', '<EMAIL>', '13800000004', '监察室', '履约监督员', 1, '履约监督员');

-- 插入基础角色
INSERT INTO roles (role_code, role_name, description, status, remark) VALUES
('ADMIN', '系统管理员', '系统管理员，拥有所有权限', 1, '系统管理员角色'),
('PROCUREMENT_STAFF', '采购员', '采购实施人员，执行具体采购工作', 1, '采购员角色'),
('FINANCE_STAFF', '财务人员', '财务处理人员，负责付款处理', 1, '财务人员角色'),
('SUPERVISOR', '履约监督员', '专门负责合同履约监督', 1, '履约监督员角色'),
('DEPARTMENT_HEAD', '部门负责人', '各部门负责人，负责需求审批', 1, '部门负责人角色');

-- 插入基础权限
INSERT INTO permissions (permission_code, permission_name, permission_type, parent_id, status, remark) VALUES
('SYSTEM', '系统管理', 'MENU', 0, 1, '系统管理菜单'),
('SYSTEM:USER', '用户管理', 'MENU', 1, 1, '用户管理'),
('SYSTEM:ROLE', '角色管理', 'MENU', 1, 1, '角色管理'),
('PROCUREMENT', '采购管理', 'MENU', 0, 1, '采购管理菜单'),
('PROCUREMENT:REQUIREMENT', '需求管理', 'MENU', 4, 1, '需求管理'),
('PROCUREMENT:PROJECT', '项目管理', 'MENU', 4, 1, '项目管理'),
('SUPPLIER', '供应商管理', 'MENU', 0, 1, '供应商管理菜单'),
('CONTRACT', '合同管理', 'MENU', 0, 1, '合同管理菜单'),
('FINANCE', '财务管理', 'MENU', 0, 1, '财务管理菜单');

-- 分配用户角色
INSERT INTO user_roles (user_id, role_id) VALUES
(1, 1),  -- admin -> 系统管理员
(2, 2),  -- procurement01 -> 采购员
(3, 2),  -- procurement02 -> 采购员
(4, 3),  -- finance01 -> 财务人员
(5, 4);  -- supervisor01 -> 履约监督员

-- 分配角色权限（系统管理员拥有所有权限）
INSERT INTO role_permissions (role_id, permission_id) VALUES
(1, 1), (1, 2), (1, 3), (1, 4), (1, 5), (1, 6), (1, 7), (1, 8), (1, 9),  -- 系统管理员
(2, 4), (2, 5), (2, 6), (2, 7),  -- 采购员
(3, 9),  -- 财务人员
(4, 8);  -- 履约监督员

-- 插入示例供应商
INSERT INTO suppliers (supplier_code, supplier_name, supplier_type, legal_person, contact_person, contact_phone, contact_email, address, business_license, bank_name, bank_account, business_scope, cooperation_status, status, remark) VALUES
('SUP001', '北京科技有限公司', 'ENTERPRISE', '张总', '李经理', '010-********', '<EMAIL>', '北京市海淀区中关村大街1号', '91110000********9X', '中国银行北京分行', '********90********9', '计算机软硬件销售', 'NORMAL', 1, '优质供应商'),
('SUP002', '上海办公用品公司', 'ENTERPRISE', '王总', '赵经理', '021-********', '<EMAIL>', '上海市浦东新区陆家嘴路100号', '913100009********Y', '工商银行上海分行', '9********09********', '办公用品销售', 'NORMAL', 1, '长期合作伙伴'),
('SUP003', '广州家具制造厂', 'ENTERPRISE', '刘总', '陈经理', '020-********', '<EMAIL>', '广州市天河区珠江路200号', '91440000********1Z', '建设银行广州分行', '****************111', '办公家具制造销售', 'NORMAL', 1, '家具专业供应商');

-- =============================================
-- 验证数据
-- =============================================
SELECT '数据库初始化完成！' as status;
SELECT '默认管理员账户：admin/admin123' as login_info;
SELECT COUNT(*) as user_count FROM users;
SELECT COUNT(*) as role_count FROM roles;
SELECT COUNT(*) as permission_count FROM permissions;
SELECT COUNT(*) as supplier_count FROM suppliers;

-- 显示表统计信息
SELECT
    TABLE_NAME as table_name,
    TABLE_ROWS as row_count,
    ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) as size_mb
FROM information_schema.TABLES
WHERE TABLE_SCHEMA = 'procurement_platform'
ORDER BY TABLE_NAME;
