@echo off
echo ================================
echo 创建Vue.js前端项目
echo ================================
echo.

echo 检查Node.js环境...
node --version
if errorlevel 1 (
    echo 错误：Node.js环境未配置
    pause
    exit /b 1
)

echo 检查npm环境...
npm --version
if errorlevel 1 (
    echo 错误：npm环境未配置
    pause
    exit /b 1
)
echo.

echo 切换到项目目录...
cd /d D:\caigoupingtai
echo 当前目录：%CD%
echo.

echo 创建Vue.js项目...
echo 项目名称：frontend
echo 技术栈：Vue 3 + TypeScript + Element Plus
echo.

npm create vue@latest frontend

echo.
echo 项目创建完成！
echo 下一步请执行：
echo cd frontend
echo npm install
echo npm run dev
echo.
pause
