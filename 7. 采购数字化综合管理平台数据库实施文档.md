# 采购数字化综合管理平台数据库实施文档

**文档版本：** 1.0  
**编写日期：** 2025年6月  
**文档类型：** 数据库实施文档（DID）

## 1. 引言与实施理念

### 1.1 数据库实施的本质理解

数据库实施就像是为一个复杂的图书馆设计和建造完整的管理体系。我们不仅要设计书架的结构（表结构），还要制定图书的分类规则（数据规范），建立借阅管理制度（权限控制），设置安全防护措施（备份恢复），并确保整个体系能够高效运转（性能优化）。

对于采购数字化综合管理平台而言，数据库不仅仅是数据的存储容器，更是业务逻辑的重要载体。双人制操作的控制、涉密项目的安全隔离、付款金额的一致性保障，这些关键业务规则都需要在数据库层面得到严格的实现和保护。因此，我们的数据库实施必须像银行建造金库一样严谨和安全。

### 1.2 实施方法论

我们采用"渐进式构建、分层验证"的实施方法论，这就像建造一座复杂的建筑一样，需要从地基开始，一层一层向上构建，每一层都要经过严格的质量检验后才能继续下一层的建设。

首先是基础环境的准备，就像建筑施工前的场地平整和基础设施准备。我们需要安装和配置数据库软件，设置基本的安全参数，建立初始的用户和权限体系。

然后是核心结构的创建，就像建筑的主体框架搭建。我们按照业务模块的依赖关系，有序地创建数据库表、索引、约束条件和触发器，确保每个组件都能正确工作。

接下来是业务数据的初始化，就像为新房子配备家具和生活用品。我们需要导入基础的字典数据、用户信息、组织架构等静态数据，为系统的正常运行提供基础支撑。

最后是系统优化和调试，就像新房子的精装修和设备调试。我们需要根据实际的使用情况调整数据库参数，优化查询性能，完善监控和维护机制。

### 1.3 质量保证原则

数据库实施的质量保证必须像医院的手术标准一样严格。我们建立了多重质量检查机制，确保每个实施步骤都经过充分的验证。

数据完整性验证确保所有的约束条件都能正确工作，防止不合法的数据进入系统。我们会逐一测试每个外键约束、检查约束和唯一性约束，确保它们能够有效地保护数据的完整性。

性能基准测试确保数据库能够满足系统的性能要求。我们会使用真实的数据规模和访问模式来测试数据库的查询性能、并发处理能力和系统稳定性。

安全机制验证确保数据库的安全设置能够有效防范各种安全威胁。我们会测试用户权限控制、数据加密、访问审计等安全功能，确保敏感数据得到充分保护。

## 2. 环境准备与配置

### 2.1 硬件环境规划

数据库硬件环境的规划就像是为一个高性能的计算中心设计基础设施，需要综合考虑计算能力、存储性能、网络带宽和可靠性要求。

**生产环境硬件配置**

生产环境的硬件配置必须能够支撑系统的全负荷运行，就像高速公路必须能够承受最大的交通流量一样。我们为生产环境设计了高可用的硬件架构。

主数据库服务器采用16核心CPU、64GB内存的高性能配置。CPU选择具有高主频和大缓存的型号，确保复杂查询的执行效率。内存容量足够大，能够将热点数据和索引完全缓存在内存中，大幅提升数据访问速度。

存储系统采用企业级SSD硬盘组成的RAID 10阵列，总容量2TB。RAID 10既提供了数据冗余保护，又保证了读写性能。我们选择具有高IOPS和低延迟特性的SSD硬盘，确保数据库事务的快速提交和查询的及时响应。

网络配置采用千兆以太网，并配置网络冗余。主备网络连接确保在单一网络故障时系统仍能正常运行。网络交换机也采用冗余配置，消除单点故障风险。

**备份环境硬件配置**

备份环境的硬件配置相对简化，但仍需保证备份和恢复操作的可靠性。备份服务器采用8核心CPU、32GB内存的配置，主要用于备份数据的存储和管理。

备份存储采用大容量的机械硬盘组成的RAID 6阵列，总容量10TB。RAID 6提供了双重奇偶校验保护，即使同时损坏两块硬盘也不会导致数据丢失。虽然机械硬盘的性能不如SSD，但对于备份存储而言，容量和可靠性比性能更重要。

### 2.2 软件环境配置

软件环境的配置就像是为一台精密仪器调校各种参数，每个设置都会影响系统的性能和稳定性。

**操作系统配置**

我们选择Ubuntu 20.04 LTS作为数据库服务器的操作系统。LTS版本提供了长期的技术支持和安全更新，确保系统的稳定性和安全性。

操作系统的内核参数需要针对数据库工作负载进行优化。我们调整了内存管理参数、文件系统参数和网络参数，确保操作系统能够高效地支撑数据库的运行。

```bash
# 优化内存管理参数
echo 'vm.swappiness = 1' >> /etc/sysctl.conf
echo 'vm.dirty_ratio = 15' >> /etc/sysctl.conf
echo 'vm.dirty_background_ratio = 5' >> /etc/sysctl.conf

# 优化文件系统参数
echo 'fs.file-max = 65536' >> /etc/sysctl.conf

# 优化网络参数
echo 'net.core.rmem_max = 134217728' >> /etc/sysctl.conf
echo 'net.core.wmem_max = 134217728' >> /etc/sysctl.conf

# 应用配置更改
sysctl -p
```

文件系统选择ext4，并启用了日志功能，确保在系统异常关闭时数据的完整性。文件系统的挂载参数经过优化，提升了数据库文件的访问性能。

**MySQL 8.0 配置优化**

MySQL 8.0的配置优化就像是调校一台赛车的引擎，需要根据具体的使用场景精心调整各种参数。

```sql
-- MySQL配置文件 /etc/mysql/mysql.conf.d/mysqld.cnf

[mysqld]
# 基础配置
port = 3306
socket = /var/run/mysqld/mysqld.sock
datadir = /var/lib/mysql
log-error = /var/log/mysql/error.log
pid-file = /var/run/mysqld/mysqld.pid

# 字符集配置 - 支持中文和特殊字符
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
default-time-zone = '+08:00'

# 内存配置 - 根据64GB内存优化
innodb_buffer_pool_size = 48G          # 设置为物理内存的75%
innodb_buffer_pool_instances = 16      # 提高并发性能
key_buffer_size = 256M                 # MyISAM表的索引缓存
tmp_table_size = 128M                  # 临时表的内存大小
max_heap_table_size = 128M             # Memory引擎表的最大大小

# 连接配置 - 支持高并发访问
max_connections = 500                  # 最大连接数
max_connect_errors = 100000            # 连接错误阈值
wait_timeout = 3600                    # 连接超时时间
interactive_timeout = 3600             # 交互式连接超时

# 查询优化配置
query_cache_type = 0                   # 关闭查询缓存，MySQL 8.0已不推荐
sort_buffer_size = 8M                  # 排序缓冲区大小
read_buffer_size = 2M                  # 顺序读缓冲区
read_rnd_buffer_size = 8M              # 随机读缓冲区
join_buffer_size = 8M                  # 连接操作缓冲区

# InnoDB存储引擎优化
innodb_file_per_table = 1              # 每个表使用独立的表空间文件
innodb_flush_log_at_trx_commit = 1     # 事务提交时立即刷新日志，保证ACID
innodb_log_file_size = 2G              # 重做日志文件大小
innodb_log_files_in_group = 2          # 重做日志文件数量
innodb_flush_method = O_DIRECT         # 绕过操作系统缓存，提高性能
innodb_lock_wait_timeout = 120         # 锁等待超时时间

# 慢查询日志配置 - 性能调优的重要工具
slow_query_log = 1                     # 启用慢查询日志
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2                    # 超过2秒的查询记录为慢查询
log_queries_not_using_indexes = 1      # 记录未使用索引的查询

# 二进制日志配置 - 主从复制和数据恢复
log-bin = mysql-bin                    # 启用二进制日志
binlog_format = ROW                    # 行级别的二进制日志格式
expire_logs_days = 7                   # 二进制日志保留天数
max_binlog_size = 1G                   # 二进制日志文件最大大小

# 安全配置
skip-name-resolve = 1                  # 跳过DNS解析，提高连接速度
sql_mode = STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO
```

### 2.3 安全配置

数据库的安全配置就像是为银行金库设计多重防护系统，需要从多个层面确保数据的安全性。

**用户权限管理**

我们建立了分层分级的用户权限体系，就像军队的等级制度一样，不同级别的用户只能访问相应权限范围内的资源。

```sql
-- 创建应用数据库用户
CREATE USER 'procurement_app'@'%' IDENTIFIED BY 'App_SecurePassword_2025!';

-- 创建只读用户（用于报表查询）
CREATE USER 'procurement_readonly'@'%' IDENTIFIED BY 'ReadOnly_SecurePassword_2025!';

-- 创建备份用户
CREATE USER 'procurement_backup'@'localhost' IDENTIFIED BY 'Backup_SecurePassword_2025!';

-- 创建监控用户
CREATE USER 'procurement_monitor'@'%' IDENTIFIED BY 'Monitor_SecurePassword_2025!';

-- 为应用用户授予完整的数据库权限
GRANT SELECT, INSERT, UPDATE, DELETE ON procurement_db.* TO 'procurement_app'@'%';
GRANT CREATE, DROP, ALTER, INDEX ON procurement_db.* TO 'procurement_app'@'%';
GRANT EXECUTE ON procurement_db.* TO 'procurement_app'@'%';

-- 为只读用户授予查询权限
GRANT SELECT ON procurement_db.* TO 'procurement_readonly'@'%';

-- 为备份用户授予备份权限
GRANT SELECT, LOCK TABLES, SHOW VIEW, EVENT, TRIGGER ON procurement_db.* TO 'procurement_backup'@'localhost';
GRANT RELOAD, SUPER, REPLICATION CLIENT ON *.* TO 'procurement_backup'@'localhost';

-- 为监控用户授予必要的监控权限
GRANT PROCESS, REPLICATION CLIENT ON *.* TO 'procurement_monitor'@'%';
GRANT SELECT ON performance_schema.* TO 'procurement_monitor'@'%';

-- 刷新权限
FLUSH PRIVILEGES;
```

**数据加密配置**

敏感数据的加密就像是给重要文件加上密码锁，确保即使数据被非法获取也无法直接阅读。

```sql
-- 启用表空间加密（需要MySQL 8.0+）
-- 创建加密的表空间用于存储敏感数据
CREATE TABLESPACE sensitive_data_ts ADD DATAFILE 'sensitive_data.ibd' ENCRYPTION = 'Y';

-- 为涉密项目表启用加密
ALTER TABLE pm_classified_project ENCRYPTION = 'Y';
ALTER TABLE sys_classified_permission ENCRYPTION = 'Y';
ALTER TABLE sys_classified_log ENCRYPTION = 'Y';

-- 为用户敏感信息启用加密
ALTER TABLE sys_user ENCRYPTION = 'Y';

-- 为双人制操作记录启用加密
ALTER TABLE sys_dual_operation ENCRYPTION = 'Y';
```

## 3. 数据库结构创建

### 3.1 数据库和表空间创建

数据库结构的创建就像是搭建一座建筑的框架，需要按照合理的顺序，确保每个组件都能正确地支撑后续的建设。

```sql
-- 创建主数据库
CREATE DATABASE procurement_db
  DEFAULT CHARACTER SET utf8mb4
  DEFAULT COLLATE utf8mb4_unicode_ci;

-- 使用新创建的数据库
USE procurement_db;

-- 创建专门的表空间用于不同类型的数据
-- 这样可以更好地管理存储空间和进行性能优化

-- 业务数据表空间
CREATE TABLESPACE business_data_ts ADD DATAFILE 'business_data.ibd';

-- 日志数据表空间
CREATE TABLESPACE log_data_ts ADD DATAFILE 'log_data.ibd';

-- 敏感数据表空间（启用加密）
CREATE TABLESPACE sensitive_data_ts ADD DATAFILE 'sensitive_data.ibd' ENCRYPTION = 'Y';
```

### 3.2 核心业务表创建

核心业务表的创建需要严格按照业务依赖关系的顺序进行，就像建造房屋需要先打地基再盖房子一样。

**基础数据表创建**

基础数据表就像是系统的基础设施，需要首先创建，为后续的业务表提供引用基础。

```sql
-- 数据字典表 - 系统的基础配置信息
CREATE TABLE sys_dictionary (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '字典ID',
    dict_type VARCHAR(50) NOT NULL COMMENT '字典类型',
    dict_code VARCHAR(50) NOT NULL COMMENT '字典编码',
    dict_value VARCHAR(200) NOT NULL COMMENT '字典值',
    dict_label VARCHAR(200) NOT NULL COMMENT '字典标签',
    sort_order INT NOT NULL DEFAULT 0 COMMENT '排序',
    is_active TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
    parent_code VARCHAR(50) COMMENT '父级编码',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 组合唯一约束确保字典编码在同一类型下唯一
    UNIQUE KEY uk_dict_type_code (dict_type, dict_code),
    
    -- 索引优化查询性能
    INDEX idx_dict_type (dict_type),
    INDEX idx_parent_code (parent_code),
    INDEX idx_is_active (is_active)
) COMMENT='数据字典表' 
  ENGINE=InnoDB 
  DEFAULT CHARSET=utf8mb4 
  COLLATE=utf8mb4_unicode_ci;

-- 用户信息表 - 系统用户的核心信息
CREATE TABLE sys_user (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码(BCrypt加密)',
    real_name VARCHAR(100) NOT NULL COMMENT '真实姓名',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    department VARCHAR(100) COMMENT '所属部门',
    position VARCHAR(100) COMMENT '职位',
    is_active TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
    has_classified_permission TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否有涉密权限',
    last_login_time DATETIME COMMENT '最后登录时间',
    password_update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '密码更新时间',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引优化查询性能
    INDEX idx_username (username),
    INDEX idx_real_name (real_name),
    INDEX idx_department (department),
    INDEX idx_is_active (is_active),
    INDEX idx_has_classified_permission (has_classified_permission)
) COMMENT='用户信息表' 
  ENGINE=InnoDB 
  DEFAULT CHARSET=utf8mb4 
  COLLATE=utf8mb4_unicode_ci
  ENCRYPTION = 'Y';  -- 用户信息属于敏感数据，启用加密

-- 工作日历表 - 支持精确的工作日计算
CREATE TABLE sys_work_calendar (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日历ID',
    calendar_date DATE NOT NULL UNIQUE COMMENT '日期',
    is_workday TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否工作日',
    holiday_type VARCHAR(50) COMMENT '节假日类型',
    holiday_name VARCHAR(100) COMMENT '节假日名称',
    year INT NOT NULL COMMENT '年份',
    month INT NOT NULL COMMENT '月份',
    day_of_week INT NOT NULL COMMENT '星期几(1-7)',
    
    -- 索引优化日期查询性能
    INDEX idx_calendar_date (calendar_date),
    INDEX idx_year_month (year, month),
    INDEX idx_is_workday (is_workday)
) COMMENT='工作日历表' 
  ENGINE=InnoDB 
  DEFAULT CHARSET=utf8mb4 
  COLLATE=utf8mb4_unicode_ci;
```

**项目管理核心表创建**

项目管理表是整个系统的核心，需要特别注意数据完整性和性能优化。

```sql
-- 采购项目主表 - 系统的核心业务实体
CREATE TABLE pm_project (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '项目ID',
    project_name VARCHAR(255) NOT NULL COMMENT '采购项目/品类名称',
    project_type VARCHAR(50) NOT NULL COMMENT '项目类型(货物/服务/工程)',
    budget_amount DECIMAL(15,2) NOT NULL COMMENT '预算金额（元）',
    procurement_method VARCHAR(50) NOT NULL COMMENT '采购方式',
    organization_form VARCHAR(50) NOT NULL COMMENT '采购组织形式',
    is_government_procurement TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否政府采购',
    is_classified TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否涉密',
    transaction_amount DECIMAL(15,2) COMMENT '成交金额（元）',
    project_status VARCHAR(50) NOT NULL DEFAULT 'CREATED' COMMENT '项目状态',
    demand_department VARCHAR(100) NOT NULL COMMENT '采购需求部门',
    procurement_department VARCHAR(100) NOT NULL COMMENT '采购实施部门',
    
    -- 审计字段
    created_by BIGINT NOT NULL COMMENT '创建人',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by BIGINT COMMENT '更新人',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    version INT NOT NULL DEFAULT 1 COMMENT '版本号',
    is_deleted TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
    
    -- 外键约束确保数据一致性
    FOREIGN KEY (created_by) REFERENCES sys_user(id),
    FOREIGN KEY (updated_by) REFERENCES sys_user(id),
    
    -- 业务约束
    CONSTRAINT chk_budget_amount CHECK (budget_amount > 0),
    CONSTRAINT chk_transaction_amount CHECK (transaction_amount IS NULL OR transaction_amount > 0),
    
    -- 索引优化查询性能
    INDEX idx_project_name (project_name),
    INDEX idx_project_type (project_type),
    INDEX idx_procurement_method (procurement_method),
    INDEX idx_project_status (project_status),
    INDEX idx_created_time (created_time),
    INDEX idx_demand_department (demand_department),
    INDEX idx_is_classified (is_classified),
    INDEX idx_is_deleted (is_deleted)
) COMMENT='采购项目主表' 
  ENGINE=InnoDB 
  DEFAULT CHARSET=utf8mb4 
  COLLATE=utf8mb4_unicode_ci
  TABLESPACE=business_data_ts;

-- 项目状态变更记录表 - 追踪项目状态的变化历史
CREATE TABLE pm_project_status_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '记录ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    old_status VARCHAR(50) COMMENT '原状态',
    new_status VARCHAR(50) NOT NULL COMMENT '新状态',
    change_reason VARCHAR(500) COMMENT '变更原因',
    operator_id BIGINT NOT NULL COMMENT '操作人ID',
    operator_name VARCHAR(100) NOT NULL COMMENT '操作人姓名',
    change_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '变更时间',
    
    -- 外键约束
    FOREIGN KEY (project_id) REFERENCES pm_project(id) ON DELETE CASCADE,
    FOREIGN KEY (operator_id) REFERENCES sys_user(id),
    
    -- 索引优化查询性能
    INDEX idx_project_id (project_id),
    INDEX idx_change_time (change_time),
    INDEX idx_operator_id (operator_id)
) COMMENT='项目状态变更记录表' 
  ENGINE=InnoDB 
  DEFAULT CHARSET=utf8mb4 
  COLLATE=utf8mb4_unicode_ci
  TABLESPACE=log_data_ts;

-- 双人制操作记录表 - 确保内控制度的严格执行
CREATE TABLE sys_dual_operation (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '操作ID',
    operation_type VARCHAR(50) NOT NULL COMMENT '操作类型',
    business_id BIGINT NOT NULL COMMENT '关联业务ID',
    business_type VARCHAR(50) NOT NULL COMMENT '业务类型',
    first_operator_id BIGINT NOT NULL COMMENT '第一操作人ID',
    first_operator_name VARCHAR(100) NOT NULL COMMENT '第一操作人姓名',
    first_operation_time DATETIME NOT NULL COMMENT '第一次操作时间',
    first_operation_content TEXT COMMENT '第一次操作内容',
    second_operator_id BIGINT COMMENT '第二操作人ID',
    second_operator_name VARCHAR(100) COMMENT '第二操作人姓名',
    second_operation_time DATETIME COMMENT '第二次操作时间',
    second_operation_content TEXT COMMENT '第二次操作内容',
    operation_status VARCHAR(20) NOT NULL DEFAULT 'PENDING' COMMENT '操作状态(PENDING/CONFIRMED/REJECTED)',
    complete_time DATETIME COMMENT '完成时间',
    
    -- 外键约束
    FOREIGN KEY (first_operator_id) REFERENCES sys_user(id),
    FOREIGN KEY (second_operator_id) REFERENCES sys_user(id),
    
    -- 业务约束 - 确保双人制操作的基本规则
    CONSTRAINT chk_different_operators CHECK (
        second_operator_id IS NULL OR first_operator_id != second_operator_id
    ),
    
    -- 索引优化查询性能
    INDEX idx_business_id (business_id),
    INDEX idx_operation_type (operation_type),
    INDEX idx_operation_status (operation_status),
    INDEX idx_first_operation_time (first_operation_time),
    INDEX idx_complete_time (complete_time)
) COMMENT='双人制操作记录表' 
  ENGINE=InnoDB 
  DEFAULT CHARSET=utf8mb4 
  COLLATE=utf8mb4_unicode_ci
  TABLESPACE=sensitive_data_ts
  ENCRYPTION = 'Y';  -- 双人制操作记录属于敏感数据
```

**财务管理表创建**

财务管理表涉及资金安全，需要特别严格的数据完整性控制。

```sql
-- 付款计划表 - 管理分期付款的计划安排
CREATE TABLE fm_payment_plan (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '计划ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    contract_id BIGINT COMMENT '合同ID',
    plan_sequence INT NOT NULL COMMENT '计划序号',
    plan_amount DECIMAL(15,2) NOT NULL COMMENT '计划付款金额',
    plan_date DATE NOT NULL COMMENT '计划付款日期',
    payment_condition TEXT COMMENT '付款条件',
    plan_status VARCHAR(20) NOT NULL DEFAULT 'PLANNED' COMMENT '计划状态',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 外键约束
    FOREIGN KEY (project_id) REFERENCES pm_project(id) ON DELETE CASCADE,
    
    -- 业务约束 - 确保付款金额为正数
    CONSTRAINT chk_plan_amount CHECK (plan_amount > 0),
    CONSTRAINT chk_plan_sequence CHECK (plan_sequence > 0),
    
    -- 组合唯一约束 - 同一项目下的计划序号唯一
    UNIQUE KEY uk_project_sequence (project_id, plan_sequence),
    
    -- 索引优化查询性能
    INDEX idx_project_id (project_id),
    INDEX idx_plan_date (plan_date),
    INDEX idx_plan_status (plan_status),
    INDEX idx_contract_id (contract_id)
) COMMENT='付款计划表' 
  ENGINE=InnoDB 
  DEFAULT CHARSET=utf8mb4 
  COLLATE=utf8mb4_unicode_ci
  TABLESPACE=business_data_ts;

-- 付款记录表 - 记录实际的付款执行情况
CREATE TABLE fm_payment_record (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '记录ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    plan_id BIGINT COMMENT '计划ID',
    payment_amount DECIMAL(15,2) NOT NULL COMMENT '实际付款金额',
    payment_date DATE NOT NULL COMMENT '实际付款日期',
    payment_method VARCHAR(50) COMMENT '付款方式',
    payment_note VARCHAR(500) COMMENT '付款说明',
    approver_id BIGINT COMMENT '审批人ID',
    approver_name VARCHAR(100) COMMENT '审批人姓名',
    approval_time DATETIME COMMENT '审批时间',
    payment_status VARCHAR(20) NOT NULL DEFAULT 'PENDING' COMMENT '付款状态',
    voucher_number VARCHAR(100) COMMENT '凭证号',
    created_by BIGINT NOT NULL COMMENT '创建人',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    -- 外键约束
    FOREIGN KEY (project_id) REFERENCES pm_project(id) ON DELETE CASCADE,
    FOREIGN KEY (plan_id) REFERENCES fm_payment_plan(id),
    FOREIGN KEY (approver_id) REFERENCES sys_user(id),
    FOREIGN KEY (created_by) REFERENCES sys_user(id),
    
    -- 业务约束
    CONSTRAINT chk_payment_amount CHECK (payment_amount > 0),
    
    -- 索引优化查询性能
    INDEX idx_project_id (project_id),
    INDEX idx_payment_date (payment_date),
    INDEX idx_payment_status (payment_status),
    INDEX idx_plan_id (plan_id),
    INDEX idx_created_by (created_by)
) COMMENT='付款记录表' 
  ENGINE=InnoDB 
  DEFAULT CHARSET=utf8mb4 
  COLLATE=utf8mb4_unicode_ci
  TABLESPACE=business_data_ts;
```

### 3.3 数据完整性约束

数据完整性约束就像是法律条文，确保数据始终符合业务规则的要求。

**触发器创建 - 确保付款金额一致性**

这个触发器是整个系统最重要的业务规则保障之一，就像银行的账务平衡检查一样关键。

```sql
-- 创建付款金额一致性检查触发器
DELIMITER $$

CREATE TRIGGER tr_payment_amount_check 
    BEFORE INSERT ON fm_payment_record
    FOR EACH ROW
BEGIN
    DECLARE total_paid DECIMAL(15,2) DEFAULT 0;
    DECLARE contract_amount DECIMAL(15,2) DEFAULT 0;
    DECLARE error_message VARCHAR(255);
    
    -- 获取项目的成交金额（合同金额）
    SELECT transaction_amount INTO contract_amount
    FROM pm_project 
    WHERE id = NEW.project_id;
    
    -- 如果项目还没有成交金额，则不进行检查
    IF contract_amount IS NULL THEN
        LEAVE;
    END IF;
    
    -- 计算该项目已有的付款总额
    SELECT COALESCE(SUM(payment_amount), 0) INTO total_paid
    FROM fm_payment_record
    WHERE project_id = NEW.project_id 
      AND payment_status IN ('COMPLETED', 'PROCESSING');
    
    -- 检查新增付款后是否超过合同总额
    IF (total_paid + NEW.payment_amount) > contract_amount THEN
        SET error_message = CONCAT(
            '付款总额不能超过合同金额。',
            '合同金额: ', contract_amount, ', ',
            '已付金额: ', total_paid, ', ',
            '本次付款: ', NEW.payment_amount, ', ',
            '超出金额: ', (total_paid + NEW.payment_amount - contract_amount)
        );
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = error_message;
    END IF;
END$$

DELIMITER ;
```

**项目状态变更记录触发器**

这个触发器确保项目状态的每次变更都有完整的审计记录。

```sql
DELIMITER $$

CREATE TRIGGER tr_project_status_log
    AFTER UPDATE ON pm_project
    FOR EACH ROW
BEGIN
    -- 只有当状态发生变化时才记录
    IF OLD.project_status != NEW.project_status THEN
        INSERT INTO pm_project_status_log (
            project_id, 
            old_status, 
            new_status, 
            change_reason,
            operator_id, 
            operator_name, 
            change_time
        ) VALUES (
            NEW.id, 
            OLD.project_status, 
            NEW.project_status,
            CONCAT('状态从 ', OLD.project_status, ' 变更为 ', NEW.project_status),
            NEW.updated_by, 
            (SELECT real_name FROM sys_user WHERE id = NEW.updated_by),
            NOW()
        );
    END IF;
END$$

DELIMITER ;
```

## 4. 索引优化策略

### 4.1 索引设计原理

索引设计就像是为图书馆设计目录系统，需要根据读者的查询习惯来设计最有效的索引结构。

**复合索引设计**

复合索引需要遵循最左前缀原则，就像字典的排序规则一样，需要考虑查询条件的组合方式。

```sql
-- 项目查询的复合索引 - 支持多条件查询
CREATE INDEX idx_project_query ON pm_project (
    project_status, 
    project_type, 
    procurement_method, 
    created_time
);

-- 财务查询的复合索引 - 支持付款记录查询
CREATE INDEX idx_payment_query ON fm_payment_record (
    project_id, 
    payment_status, 
    payment_date
);

-- 双人制操作查询的复合索引
CREATE INDEX idx_dual_operation_query ON sys_dual_operation (
    operation_status, 
    business_type, 
    first_operation_time
);
```

**覆盖索引设计**

覆盖索引包含查询所需的所有列，避免回表查询，大幅提升查询性能。

```sql
-- 项目列表查询的覆盖索引
CREATE INDEX idx_project_list_covering ON pm_project (
    project_status, 
    is_deleted, 
    id, 
    project_name, 
    project_type, 
    budget_amount, 
    created_time
);

-- 用户基本信息查询的覆盖索引
CREATE INDEX idx_user_basic_covering ON sys_user (
    username, 
    is_active, 
    id, 
    real_name, 
    department, 
    has_classified_permission
);
```

### 4.2 性能优化配置

数据库性能优化就像是调校一台精密仪器，需要根据实际的使用情况不断调整参数。

**查询优化器配置**

```sql
-- 优化查询缓存配置（虽然MySQL 8.0不再使用查询缓存，但可以配置其他缓存）
SET GLOBAL optimizer_switch = 'index_merge=on,index_merge_union=on,index_merge_sort_union=on';

-- 配置优化器成本模型
UPDATE mysql.server_cost SET cost_value = 0.2 WHERE cost_name = 'row_evaluate_cost';
UPDATE mysql.server_cost SET cost_value = 1.0 WHERE cost_name = 'key_compare_cost';

-- 刷新优化器成本
FLUSH OPTIMIZER_COSTS;
```

## 5. 初始数据导入

### 5.1 基础数据初始化

基础数据的初始化就像是为新房子添置基本的家具和设备，为系统的正常运行提供必要的支撑。

**数据字典初始化**

```sql
-- 项目类型字典数据
INSERT INTO sys_dictionary (dict_type, dict_code, dict_value, dict_label, sort_order) VALUES
('PROJECT_TYPE', 'GOODS', '货物', '货物', 1),
('PROJECT_TYPE', 'SERVICE', '服务', '服务', 2),
('PROJECT_TYPE', 'ENGINEERING', '工程', '工程', 3);

-- 采购方式字典数据
INSERT INTO sys_dictionary (dict_type, dict_code, dict_value, dict_label, sort_order) VALUES
('PROCUREMENT_METHOD', 'PUBLIC_TENDER', '公开招标', '公开招标', 1),
('PROCUREMENT_METHOD', 'INVITE_TENDER', '邀请招标', '邀请招标', 2),
('PROCUREMENT_METHOD', 'COMPETITIVE_NEGOTIATION', '竞争性磋商', '竞争性磋商', 3),
('PROCUREMENT_METHOD', 'INQUIRY', '询价', '询价', 4),
('PROCUREMENT_METHOD', 'SINGLE_SOURCE', '单一来源', '单一来源', 5),
('PROCUREMENT_METHOD', 'COMPETITIVE_CONSULTATION', '竞争性磋商', '竞争性磋商', 6);

-- 采购组织形式字典数据
INSERT INTO sys_dictionary (dict_type, dict_code, dict_value, dict_label, sort_order) VALUES
('ORGANIZATION_FORM', 'SELF_ORGANIZED', '自行组织', '自行组织', 1),
('ORGANIZATION_FORM', 'CENTRALIZED', '集中采购', '集中采购', 2),
('ORGANIZATION_FORM', 'AGENCY', '委托代理', '委托代理', 3);

-- 项目状态字典数据
INSERT INTO sys_dictionary (dict_type, dict_code, dict_value, dict_label, sort_order) VALUES
('PROJECT_STATUS', 'CREATED', '已创建', '已创建', 1),
('PROJECT_STATUS', 'APPROVED', '审核通过', '审核通过', 2),
('PROJECT_STATUS', 'IMPLEMENTING', '采购实施', '采购实施', 3),
('PROJECT_STATUS', 'CONTRACTED', '合同签订', '合同签订', 4),
('PROJECT_STATUS', 'PERFORMING', '履约监督', '履约监督', 5),
('PROJECT_STATUS', 'ACCEPTED', '验收完成', '验收完成', 6),
('PROJECT_STATUS', 'COMPLETED', '项目完成', '项目完成', 7),
('PROJECT_STATUS', 'CANCELLED', '已取消', '已取消', 8);
```

**工作日历数据初始化**

工作日历的初始化需要考虑国家法定节假日和调休安排，这是付款期限计算的重要基础。

```sql
-- 工作日历初始化存储过程
DELIMITER $$

CREATE PROCEDURE InitializeWorkCalendar(IN start_year INT, IN end_year INT)
BEGIN
    DECLARE current_date DATE;
    DECLARE end_date DATE;
    DECLARE day_of_week INT;
    
    SET current_date = CONCAT(start_year, '-01-01');
    SET end_date = CONCAT(end_year, '-12-31');
    
    -- 清空现有数据
    DELETE FROM sys_work_calendar WHERE year BETWEEN start_year AND end_year;
    
    -- 循环生成日历数据
    WHILE current_date <= end_date DO
        SET day_of_week = DAYOFWEEK(current_date);
        
        INSERT INTO sys_work_calendar (
            calendar_date, 
            is_workday, 
            year, 
            month, 
            day_of_week
        ) VALUES (
            current_date,
            CASE WHEN day_of_week IN (1, 7) THEN 0 ELSE 1 END, -- 周末为非工作日
            YEAR(current_date),
            MONTH(current_date),
            day_of_week
        );
        
        SET current_date = DATE_ADD(current_date, INTERVAL 1 DAY);
    END WHILE;
    
    -- 设置2025年的法定节假日
    UPDATE sys_work_calendar SET is_workday = 0, holiday_type = 'NATIONAL', holiday_name = '元旦' 
    WHERE calendar_date IN ('2025-01-01');
    
    UPDATE sys_work_calendar SET is_workday = 0, holiday_type = 'NATIONAL', holiday_name = '春节' 
    WHERE calendar_date BETWEEN '2025-01-28' AND '2025-02-03';
    
    UPDATE sys_work_calendar SET is_workday = 0, holiday_type = 'NATIONAL', holiday_name = '清明节' 
    WHERE calendar_date BETWEEN '2025-04-05' AND '2025-04-07';
    
    UPDATE sys_work_calendar SET is_workday = 0, holiday_type = 'NATIONAL', holiday_name = '劳动节' 
    WHERE calendar_date BETWEEN '2025-05-01' AND '2025-05-05';
    
    UPDATE sys_work_calendar SET is_workday = 0, holiday_type = 'NATIONAL', holiday_name = '端午节' 
    WHERE calendar_date BETWEEN '2025-05-31' AND '2025-06-02';
    
    UPDATE sys_work_calendar SET is_workday = 0, holiday_type = 'NATIONAL', holiday_name = '中秋节' 
    WHERE calendar_date = '2025-10-06';
    
    UPDATE sys_work_calendar SET is_workday = 0, holiday_type = 'NATIONAL', holiday_name = '国庆节' 
    WHERE calendar_date BETWEEN '2025-10-01' AND '2025-10-07';
    
    -- 设置调休工作日
    UPDATE sys_work_calendar SET is_workday = 1, holiday_type = 'ADJUSTED', holiday_name = '春节调休' 
    WHERE calendar_date IN ('2025-01-26', '2025-02-08');
    
    UPDATE sys_work_calendar SET is_workday = 1, holiday_type = 'ADJUSTED', holiday_name = '国庆调休' 
    WHERE calendar_date IN ('2025-09-28', '2025-10-11');
    
END$$

DELIMITER ;

-- 执行初始化程序，生成2025-2027年的工作日历
CALL InitializeWorkCalendar(2025, 2027);
```

## 6. 备份与恢复策略

### 6.1 备份策略设计

数据库备份策略就像是为重要文件建立多重保险机制，需要考虑不同类型的故障场景和恢复需求。

**全量备份策略**

```bash
#!/bin/bash
# 全量备份脚本 - daily_full_backup.sh

# 配置变量
DB_NAME="procurement_db"
DB_USER="procurement_backup"
DB_PASSWORD="Backup_SecurePassword_2025!"
BACKUP_DIR="/opt/mysql_backup"
DATE=$(date +%Y%m%d_%H%M%S)
RETENTION_DAYS=30

# 创建备份目录
mkdir -p $BACKUP_DIR/full/$DATE

echo "开始全量备份: $(date)"

# 执行全量备份
mysqldump --user=$DB_USER --password=$DB_PASSWORD \
    --single-transaction \
    --routines \
    --triggers \
    --events \
    --hex-blob \
    --lock-tables=false \
    --master-data=2 \
    $DB_NAME > $BACKUP_DIR/full/$DATE/${DB_NAME}_full_$DATE.sql

# 检查备份是否成功
if [ $? -eq 0 ]; then
    echo "全量备份成功: $BACKUP_DIR/full/$DATE/${DB_NAME}_full_$DATE.sql"
    
    # 压缩备份文件
    gzip $BACKUP_DIR/full/$DATE/${DB_NAME}_full_$DATE.sql
    
    # 备份配置文件
    cp /etc/mysql/mysql.conf.d/mysqld.cnf $BACKUP_DIR/full/$DATE/
    
    echo "备份文件已压缩完成"
else
    echo "全量备份失败"
    exit 1
fi

# 清理过期备份文件
find $BACKUP_DIR/full -type d -mtime +$RETENTION_DAYS -exec rm -rf {} \; 2>/dev/null

echo "全量备份完成: $(date)"
```

**增量备份策略**

```bash
#!/bin/bash
# 增量备份脚本 - hourly_incremental_backup.sh

# 配置变量
BACKUP_DIR="/opt/mysql_backup"
BINLOG_DIR="/var/lib/mysql"
DATE=$(date +%Y%m%d_%H%M%S)
RETENTION_HOURS=72

# 创建增量备份目录
mkdir -p $BACKUP_DIR/incremental/$DATE

echo "开始增量备份: $(date)"

# 刷新二进制日志
mysql -u procurement_backup -pBackup_SecurePassword_2025! -e "FLUSH LOGS;"

# 复制二进制日志文件
cp $BINLOG_DIR/mysql-bin.* $BACKUP_DIR/incremental/$DATE/ 2>/dev/null

# 记录备份信息
mysql -u procurement_backup -pBackup_SecurePassword_2025! -e "SHOW MASTER STATUS;" > $BACKUP_DIR/incremental/$DATE/master_status_$DATE.txt

if [ $? -eq 0 ]; then
    echo "增量备份成功: $BACKUP_DIR/incremental/$DATE"
else
    echo "增量备份失败"
    exit 1
fi

# 清理过期的增量备份
find $BACKUP_DIR/incremental -type d -mtime +$RETENTION_HOURS -exec rm -rf {} \; 2>/dev/null

echo "增量备份完成: $(date)"
```

### 6.2 恢复策略设计

数据库恢复策略需要考虑不同的故障场景和恢复时间要求，就像急救医疗程序一样需要分级处理。

**完整恢复程序**

```bash
#!/bin/bash
# 数据库完整恢复脚本 - database_recovery.sh

# 使用方法: ./database_recovery.sh [backup_date] [point_in_time]
# 示例: ./database_recovery.sh 20250615_140000 "2025-06-15 15:30:00"

BACKUP_DATE=$1
POINT_IN_TIME=$2
BACKUP_DIR="/opt/mysql_backup"
DB_NAME="procurement_db"
RECOVERY_DB_NAME="${DB_NAME}_recovery"

if [ -z "$BACKUP_DATE" ]; then
    echo "错误：请指定备份日期"
    echo "使用方法: $0 [backup_date] [point_in_time]"
    exit 1
fi

echo "开始数据库恢复程序"
echo "备份日期: $BACKUP_DATE"
echo "恢复时间点: ${POINT_IN_TIME:-最新}"

# 步骤1：创建恢复数据库
echo "步骤1：创建恢复数据库"
mysql -u root -p << EOF
DROP DATABASE IF EXISTS $RECOVERY_DB_NAME;
CREATE DATABASE $RECOVERY_DB_NAME DEFAULT CHARACTER SET utf8mb4 DEFAULT COLLATE utf8mb4_unicode_ci;
EOF

# 步骤2：恢复全量备份
echo "步骤2：恢复全量备份"
FULL_BACKUP_FILE="$BACKUP_DIR/full/$BACKUP_DATE/${DB_NAME}_full_${BACKUP_DATE}.sql.gz"

if [ -f "$FULL_BACKUP_FILE" ]; then
    echo "找到全量备份文件: $FULL_BACKUP_FILE"
    gunzip -c $FULL_BACKUP_FILE | mysql -u root -p $RECOVERY_DB_NAME
    
    if [ $? -eq 0 ]; then
        echo "全量备份恢复成功"
    else
        echo "全量备份恢复失败"
        exit 1
    fi
else
    echo "错误：找不到全量备份文件 $FULL_BACKUP_FILE"
    exit 1
fi

# 步骤3：应用增量备份（如果指定了时间点）
if [ -n "$POINT_IN_TIME" ]; then
    echo "步骤3：应用增量备份到指定时间点"
    
    # 查找需要应用的二进制日志文件
    for INCREMENTAL_DIR in $(find $BACKUP_DIR/incremental -name "*" -type d | sort); do
        if [ -f "$INCREMENTAL_DIR/mysql-bin.000001" ]; then
            echo "应用增量备份: $INCREMENTAL_DIR"
            
            # 应用二进制日志到指定时间点
            mysqlbinlog --stop-datetime="$POINT_IN_TIME" \
                $INCREMENTAL_DIR/mysql-bin.* | \
                mysql -u root -p $RECOVERY_DB_NAME
        fi
    done
    
    echo "增量备份恢复完成"
fi

# 步骤4：验证恢复结果
echo "步骤4：验证恢复结果"
mysql -u root -p << EOF
USE $RECOVERY_DB_NAME;
SELECT '数据库表数量:', COUNT(*) FROM information_schema.tables WHERE table_schema = '$RECOVERY_DB_NAME';
SELECT '项目记录数量:', COUNT(*) FROM pm_project;
SELECT '用户记录数量:', COUNT(*) FROM sys_user;
EOF

echo "数据库恢复程序完成"
echo "恢复的数据库名称: $RECOVERY_DB_NAME"
echo "请验证数据完整性后，决定是否替换生产数据库"
```

## 7. 监控与维护

### 7.1 性能监控配置

数据库性能监控就像是为汽车安装仪表盘，需要实时了解各种关键指标的状态。

**Performance Schema配置**

```sql
-- 启用Performance Schema的关键监控
UPDATE performance_schema.setup_instruments 
SET ENABLED = 'YES', TIMED = 'YES' 
WHERE NAME LIKE 'statement/%';

UPDATE performance_schema.setup_instruments 
SET ENABLED = 'YES', TIMED = 'YES' 
WHERE NAME LIKE 'wait/io/%';

-- 启用关键的消费者
UPDATE performance_schema.setup_consumers 
SET ENABLED = 'YES' 
WHERE NAME IN (
    'events_statements_current',
    'events_statements_history',
    'events_statements_history_long',
    'events_waits_current',
    'events_waits_history',
    'events_waits_history_long'
);
```

**监控视图创建**

```sql
-- 创建慢查询监控视图
CREATE VIEW v_slow_queries AS
SELECT 
    DIGEST_TEXT as query_text,
    COUNT_STAR as exec_count,
    AVG_TIMER_WAIT/1000000000 as avg_time_seconds,
    MAX_TIMER_WAIT/1000000000 as max_time_seconds,
    SUM_ROWS_EXAMINED/COUNT_STAR as avg_rows_examined,
    FIRST_SEEN,
    LAST_SEEN
FROM performance_schema.events_statements_summary_by_digest 
WHERE AVG_TIMER_WAIT > 2000000000  -- 大于2秒的查询
ORDER BY AVG_TIMER_WAIT DESC;

-- 创建锁等待监控视图
CREATE VIEW v_lock_waits AS
SELECT 
    r.object_schema,
    r.object_name,
    r.lock_type,
    r.lock_duration,
    p1.user as blocked_user,
    p1.host as blocked_host,
    p1.info as blocked_query,
    p2.user as blocking_user,
    p2.host as blocking_host,
    p2.info as blocking_query
FROM performance_schema.metadata_locks r
JOIN information_schema.processlist p1 ON r.owner_thread_id = p1.id
JOIN information_schema.processlist p2 ON r.owner_thread_id != p2.id
WHERE r.lock_status = 'PENDING';
```

### 7.2 自动化维护脚本

数据库的自动化维护就像是汽车的定期保养，需要定期执行各种维护任务来保持最佳性能。

**表优化脚本**

```bash
#!/bin/bash
# 数据库维护脚本 - weekly_maintenance.sh

DB_NAME="procurement_db"
DB_USER="procurement_app"
DB_PASSWORD="App_SecurePassword_2025!"
LOG_FILE="/var/log/mysql/maintenance.log"

echo "$(date): 开始数据库维护" >> $LOG_FILE

# 分析所有表的统计信息
echo "$(date): 更新表统计信息" >> $LOG_FILE
mysql -u $DB_USER -p$DB_PASSWORD $DB_NAME << EOF
ANALYZE TABLE pm_project;
ANALYZE TABLE fm_payment_record;
ANALYZE TABLE sys_dual_operation;
ANALYZE TABLE pm_project_status_log;
EOF

# 优化重要表
echo "$(date): 优化表结构" >> $LOG_FILE
mysql -u $DB_USER -p$DB_PASSWORD $DB_NAME << EOF
OPTIMIZE TABLE pm_project;
OPTIMIZE TABLE fm_payment_record;
OPTIMIZE TABLE sys_user;
EOF

# 清理过期的日志数据
echo "$(date): 清理过期日志" >> $LOG_FILE
mysql -u $DB_USER -p$DB_PASSWORD $DB_NAME << EOF
-- 清理90天前的状态变更日志
DELETE FROM pm_project_status_log 
WHERE change_time < DATE_SUB(NOW(), INTERVAL 90 DAY);

-- 清理1年前的已完成双人制操作记录
DELETE FROM sys_dual_operation 
WHERE operation_status = 'CONFIRMED' 
  AND complete_time < DATE_SUB(NOW(), INTERVAL 1 YEAR);
EOF

echo "$(date): 数据库维护完成" >> $LOG_FILE
```

通过这样详细而全面的数据库实施文档，我们不仅提供了完整的数据库搭建指南，更建立了一套科学的数据库管理体系。这个体系就像一个精密的时钟机制，每个组件都有其特定的作用，共同确保采购数字化综合管理平台的数据基础稳固可靠。