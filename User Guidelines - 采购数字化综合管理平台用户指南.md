# 采购数字化综合管理平台用户指南
## User Guidelines for Procurement Digital Management Platform

**文档版本：** 1.0  
**编写日期：** 2025年6月  
**适用系统：** 采购数字化综合管理平台  
**技术栈：** Spring Boot 2.7.x + Vue.js 3.x + MySQL 9.3 + Redis 6.x

---

## 1. 系统概述

### 1.1 平台简介

采购数字化综合管理平台是一个基于现代化技术架构构建的企业级采购管理系统，旨在实现采购全流程的数字化管理，提高采购透明度，降低采购成本，加强供应商管理，确保采购合规性。

### 1.2 核心特性

- **全流程管理**：覆盖从采购计划到付款完成的完整业务链条
- **双人制操作**：严格执行采购实施双人制和验收多人制管理
- **涉密项目管控**：专门的涉密项目安全管理机制
- **智能预警**：付款时限、流程超时、合规性等多维度预警
- **分期付款管理**：精细化的多期付款管理和时限控制
- **供应商全生命周期管理**：从注册到评估的完整管理体系

### 1.3 技术架构

```
前端技术栈：
✅ Vue.js 3.x              // 前端框架
✅ Element Plus            // UI组件库  
✅ Axios                   // HTTP客户端
✅ Vite                    // 构建工具

后端技术栈：
✅ Spring Boot 2.7.x       // 核心应用框架
✅ Spring Security         // 安全认证框架
✅ Spring Data JPA         // 数据访问层
✅ MyBatis Plus           // ORM框架
✅ MySQL 9.3              // 主数据库
✅ Redis 6.x              // 缓存数据库

部署技术栈：
✅ Docker                 // 容器化
✅ Nginx                  // Web服务器/反向代理
✅ Jenkins                // CI/CD流水线
```

---

## 2. 用户角色与权限

### 2.1 角色体系

#### 基础角色
- **需求部门人员**：采购需求提出、验收确认
- **需求部门负责人**：本部门采购需求审批
- **后勤服务中心采购员**：采购执行、报销准备（双人制）
- **后勤服务中心主任**：部门管理、报销审批

#### 专门角色
- **履约监督人员**：合同履行过程监督管理
- **涉密项目管理员**：涉密项目全程安全管控
- **会计科人员**：财务处理和审批
- **分管领导**：相应审批职责

#### 监督角色
- **内审人员**：全流程查看权限、合规检查
- **法务人员**：合同审核和法律支持
- **系统管理员**：系统维护和配置管理

### 2.2 权限矩阵

| 功能模块 | 需求部门 | 采购员 | 履约监督 | 财务人员 | 内审人员 | 系统管理员 |
|---------|---------|--------|----------|----------|----------|------------|
| 需求申报 | ✅创建/修改 | ❌ | ❌ | ❌ | 👁️查看 | 🔧管理 |
| 采购实施 | 👁️查看 | ✅执行(双人) | 👁️监督 | ❌ | 👁️查看 | 🔧管理 |
| 合同管理 | 👁️查看 | ✅管理 | ✅监督 | 👁️查看 | 👁️查看 | 🔧管理 |
| 验收管理 | ✅验收(多人) | ✅组织 | 👁️监督 | ❌ | 👁️查看 | 🔧管理 |
| 付款管理 | 👁️查看 | ✅申请 | ❌ | ✅审批 | 👁️查看 | 🔧管理 |
| 涉密项目 | 🔒授权访问 | 🔒授权访问 | 🔒授权访问 | 🔒授权访问 | 🔒授权访问 | 🔧管理 |

---

## 3. 系统登录与基础操作

### 3.1 系统登录

1. **访问地址**：`http://localhost:8080` (开发环境)
2. **浏览器要求**：Chrome 90+、Firefox 88+、Edge 90+
3. **登录凭证**：使用分配的用户名和密码
4. **安全认证**：基于JWT令牌的双令牌机制

```
登录流程：
1. 输入用户名和密码
2. 系统验证身份信息
3. 获取访问令牌(2小时有效)和刷新令牌(7天有效)
4. 进入系统主界面
```

### 3.2 界面布局

```
系统界面布局：
┌─────────────────────────────────────────┐
│ 顶部导航栏 (用户信息、消息通知、退出)      │
├─────────────────────────────────────────┤
│ 侧边菜单栏 │        主工作区域          │
│           │                           │
│ - 采购管理 │    表单/表格/图表显示区     │
│ - 供应商   │                           │
│ - 合同管理 │                           │
│ - 财务管理 │                           │
│ - 统计报表 │                           │
│ - 系统设置 │                           │
└─────────────────────────────────────────┘
```

### 3.3 基础操作规范

#### 数据录入规范
- **必填字段**：标有红色星号(*)的字段必须填写
- **数据格式**：严格按照字段要求的格式输入
- **保存机制**：支持草稿保存和正式提交两种模式
- **数据校验**：前端实时校验+后端二次校验

#### 操作确认机制
- **重要操作**：删除、审批、付款等操作需要二次确认
- **双人制操作**：系统强制要求两人参与的操作环节
- **操作日志**：所有操作自动记录，支持审计追溯

---

## 4. 核心业务流程指南

### 4.1 采购需求管理流程

#### 4.1.1 需求申报（需求部门人员）

```
操作步骤：
1. 登录系统 → 采购管理 → 需求申报
2. 点击"新建需求申请"
3. 填写需求信息：
   - 物品名称*
   - 详细规格型号*
   - 采购数量*
   - 预期用途*
   - 需求时间*
   - 是否涉密项目*
4. 上传相关附件（如需求说明书）
5. 保存草稿或提交申请
```

**注意事项：**
- 涉密项目必须明确标注，一旦标注将启用特殊管控流程
- 需求信息必须详细准确，后续修改需要重新审批
- 预期用途要明确具体，便于后续验收确认

#### 4.1.2 需求审核（后勤服务中心）

```
审核流程：
1. 采购管理 → 需求审核 → 待审核列表
2. 选择需求申请，查看详细信息
3. 进行审核评估：
   - 采购必要性审核
   - 技术规格合理性审核  
   - 库存查验
   - 预算可行性分析
4. 填写审核意见
5. 选择"通过"或"退回修改"
```

### 4.2 采购实施管理流程

#### 4.2.1 采购项目创建（双人制操作）

```
创建流程：
1. 采购管理 → 项目管理 → 新建项目
2. 基本信息录入：
   - 项目名称*
   - 项目类型*（货物/服务/工程）
   - 预算金额*
   - 采购方式*
   - 采购组织形式*
   - 是否政府采购*
   - 是否涉密*
3. 人员配置（系统强制双人制）：
   - 采购实施人员1*
   - 采购实施人员2*
   - 履约监督人员*
4. 保存并提交审批
```

**双人制操作要求：**
- 系统强制要求录入两名采购实施人员
- 两名人员必须都具备相应操作权限
- 关键操作需要两人分别确认

#### 4.2.2 供应商报价管理

```
报价管理流程：
1. 项目管理 → 选择项目 → 报价管理
2. 添加供应商（至少3家）：
   - 供应商1 + 报价1*
   - 供应商2 + 报价2*  
   - 供应商3 + 报价3*
   - 可添加更多供应商
3. 报价比较分析
4. 确定成交供应商和成交金额
5. 生成比价记录
```

### 4.3 合同管理流程

#### 4.3.1 合同签订

```
合同签订流程：
1. 合同管理 → 新建合同
2. 关联采购项目
3. 填写合同信息：
   - 合同名称*
   - 合同编号*
   - 签订日期*
   - 履行期限*
   - 合同金额*
4. 上传合同扫描件
5. 法务审核（如需要）
6. 合同生效确认
```

#### 4.3.2 履约监督（履约监督人员）

```
监督流程：
1. 合同管理 → 履约监督 → 我的监督项目
2. 制定履约计划
3. 定期检查履约进度：
   - 供应商履约情况
   - 交付时间节点
   - 质量标准符合性
4. 记录监督日志
5. 发现问题及时预警
```

### 4.4 验收管理流程（多人制操作）

```
验收流程：
1. 验收管理 → 待验收项目
2. 创建验收单
3. 配置验收人员（至少2人）：
   - 验收人员1*
   - 验收人员2*
   - 可添加更多验收人员
4. 现场验收：
   - 数量核对
   - 质量检查
   - 规格确认
5. 填写验收结果
6. 多人签字确认
7. 生成验收报告
```

**多人验收要求：**
- 系统强制要求至少2人参与验收
- 所有验收人员必须分别确认
- 验收意见不一致时需要协调解决

### 4.5 付款管理流程

#### 4.5.1 分期付款设置

```
分期付款配置：
1. 财务管理 → 付款计划 → 新建计划
2. 关联合同信息
3. 设置付款期次：
   - 第1期：金额 + 计划日期
   - 第2期：金额 + 计划日期
   - 第N期：金额 + 计划日期
4. 系统自动校验：付款金额合计 = 成交金额
5. 保存付款计划
```

#### 4.5.2 付款时限管控

```
时限管控机制：
1. 验收完成后自动启动倒计时
2. 分级预警提醒：
   - 剩余10个工作日：黄色预警
   - 剩余5个工作日：橙色预警  
   - 剩余3个工作日：红色预警
   - 超期未付款：紧急预警
3. 预警推送：
   - 系统内消息通知
   - 邮件提醒
   - 升级推送至领导
```

---

## 5. 涉密项目管理指南

### 5.1 涉密项目标识

```
涉密标识流程：
1. 项目创建时明确标注"是否涉密"
2. 一旦标注为涉密项目：
   - 启用特殊安全管控流程
   - 限制访问权限范围
   - 启用数据加密存储
   - 记录详细访问日志
```

### 5.2 涉密项目权限管理

```
权限控制机制：
1. 只有获得涉密项目授权的人员才能访问
2. 涉密项目管理员负责权限分配
3. 访问权限定期审核和更新
4. 所有访问操作详细记录
```

### 5.3 涉密项目安全要求

- **数据加密**：涉密数据采用AES-256加密存储
- **访问控制**：基于角色和项目属性的多层次权限控制
- **审计日志**：完整记录所有访问和操作行为
- **安全隔离**：涉密项目数据与普通项目数据物理隔离

---

## 6. 系统预警与提醒

### 6.1 付款时限预警

```
预警级别：
🟡 黄色预警：剩余10个工作日
🟠 橙色预警：剩余5个工作日
🔴 红色预警：剩余3个工作日
🚨 紧急预警：超期未付款
```

### 6.2 流程超时预警

- **审批超时**：审批环节超过规定时限
- **验收超时**：验收环节超过规定时限
- **合同到期**：合同即将到期提醒

### 6.3 合规性预警

- **双人制执行**：监控双人制操作的执行情况
- **涉密项目**：监控涉密项目的管理规范性
- **预算超支**：监控预算执行情况

---

## 7. 报表与统计

### 7.1 采购分析报表

- **采购执行情况统计**：反映采购计划执行进度
- **预算执行分析**：预算使用情况和执行效率
- **供应商分析报表**：供应商绩效评价和市场分析
- **采购效率分析**：评估采购流程效率

### 7.2 合规性统计报表

- **制度执行情况**：双人制执行率、验收规范性等
- **付款及时性分析**：按时付款率、超期付款明细
- **部门付款及时性排名**：促进各部门提高效率

### 7.3 自定义报表

- **自定义查询**：根据特定条件生成个性化报表
- **数据导出**：支持Excel、PDF等多种格式导出

---

## 8. 常见问题与解决方案

### 8.1 登录问题

**Q: 忘记密码怎么办？**
A: 联系系统管理员重置密码，或使用"忘记密码"功能通过邮箱重置。

**Q: 令牌过期怎么处理？**
A: 系统会自动使用刷新令牌获取新的访问令牌，如果刷新令牌也过期，需要重新登录。

### 8.2 操作问题

**Q: 双人制操作如何执行？**
A: 系统会强制要求录入两名操作人员，关键操作需要两人分别登录确认。

**Q: 涉密项目如何申请访问权限？**
A: 向涉密项目管理员申请，经审核通过后获得访问权限。

### 8.3 数据问题

**Q: 数据录入错误如何修改？**
A: 根据业务流程状态，已提交的数据可能需要撤回或走变更流程。

**Q: 付款金额合计与成交金额不一致怎么办？**
A: 系统会自动校验，需要调整各期付款金额确保合计值正确。

---

## 9. 技术支持与联系方式

### 9.1 技术支持

- **系统管理员**：负责系统维护和用户权限管理
- **业务支持**：负责业务流程咨询和培训
- **技术支持**：负责系统技术问题解决

### 9.2 联系方式

- **技术支持热线**：[待配置]
- **业务咨询邮箱**：[待配置]
- **系统管理员**：[待配置]

---

## 10. 附录

### 10.1 Windows 10+ 开发环境要求

```
开发环境要求：
- 操作系统：Windows 10+ (64位)
- 浏览器：Chrome 90+、Firefox 88+、Edge 90+
- 网络：稳定的网络连接
- 分辨率：1366x768以上
- 内存：8GB以上推荐
- 存储：50GB以上可用空间

必需软件：
- Java：JDK 11+ (推荐OpenJDK 11或Oracle JDK 11)
- Node.js：16.x+ (包含npm)
- MySQL：8.0+ (Windows版本)
- Redis：6.0+ (Windows版本或Docker)
- Git：最新版本
- IDE：IntelliJ IDEA / VS Code (推荐)
```

### 10.2 数据库连接信息

```
开发环境配置：
连接名：localhost
主机：localhost
端口：3306
用户名：root
密码：root
数据库：procurement_platform
```

### 10.3 相关法规依据

- 《政府采购法》
- 《保障中小企业款项支付条例》第九条、第十条
- 相关采购管理法规和内控制度要求

---

**文档结束**

> 本用户指南将随着系统功能的完善和用户反馈持续更新。如有疑问或建议，请及时联系技术支持团队。
