# 采购数字化综合管理平台用户指南
## User Guidelines for Procurement Digital Management Platform

**文档版本：** 2.0
**编写日期：** 2025年6月
**适用系统：** 采购数字化综合管理平台
**开发环境：** Windows 10+
**技术栈：** Spring Boot 2.7.x + Vue.js 3.x + MySQL 9.3 + Redis 6.x

---

## 📋 目录

1. [系统概述](#1-系统概述)
2. [快速开始](#2-快速开始)
3. [用户角色与权限](#3-用户角色与权限)
4. [核心功能模块](#4-核心功能模块)
5. [业务流程指南](#5-业务流程指南)
6. [特殊功能说明](#6-特殊功能说明)
7. [系统管理](#7-系统管理)
8. [常见问题](#8-常见问题)
9. [技术支持](#9-技术支持)

---

## 1. 系统概述

### 1.1 平台简介

采购数字化综合管理平台是专为政府机关、事业单位设计的企业级采购管理系统，严格按照《政府采购法》、《保障中小企业款项支付条例》等法规要求构建，实现采购全流程的数字化、规范化管理。

### 1.2 核心价值

🎯 **提升效率**：采购流程效率提升30%以上，人工作业时间减少40%
🔒 **确保合规**：100%符合政府采购法规要求，支持双人制、多人验收等内控制度
💰 **降低成本**：通过规范化管理和数据分析，降低采购成本25%以上
📊 **数据驱动**：实时数据分析和智能预警，为决策提供科学依据
🛡️ **安全可靠**：涉密项目专门管控，数据安全等级达到国家标准

### 1.3 系统特色

#### 🔄 全流程闭环管理
```
需求申报 → 需求审核 → 采购实施 → 合同签订 → 履约监督 → 验收确认 → 付款结算
    ↑                                                                    ↓
    └─────────────────── 数据反馈与流程优化 ←─────────────────────────────┘
```

#### 👥 多角色协同作业
- **需求部门**：提出采购需求，参与验收确认
- **采购部门**：执行采购实施，组织验收工作
- **财务部门**：预算控制，付款审批
- **监督部门**：全程监督，合规检查
- **履约监督员**：专人负责合同履行监督

#### 🔐 严格的安全管控
- **分级权限管理**：基于角色的精细化权限控制
- **涉密项目隔离**：专门的安全管控机制
- **操作全程记录**：所有操作可追溯、可审计
- **数据加密存储**：敏感数据采用高级加密算法

### 1.4 技术架构

```
┌─────────────────────────────────────────────────────────────┐
│                    用户界面层 (Vue.js 3.x)                    │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │  采购管理   │ │  供应商管理  │ │  合同管理   │ │  财务管理   │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                   业务逻辑层 (Spring Boot)                    │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │  用户认证   │ │  权限管理   │ │  业务处理   │ │  消息通知   │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    数据访问层 (MyBatis Plus)                  │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │  MySQL 9.3  │ │  Redis 6.x  │ │  文件存储   │ │  日志系统   │ │
│  │  (主数据库)  │ │  (缓存)     │ │  (附件)     │ │  (审计)     │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

---

## 2. 快速开始

### 2.1 系统访问

#### 🌐 访问地址
- **开发环境**：`http://localhost:5173` (前端) + `http://localhost:8080` (后端API)
- **测试环境**：`http://test.procurement.local`
- **生产环境**：`https://procurement.yourcompany.com`

#### 💻 浏览器要求
| 浏览器 | 最低版本 | 推荐版本 | 状态 |
|--------|----------|----------|------|
| Chrome | 90+ | 最新版 | ✅ 完全支持 |
| Firefox | 88+ | 最新版 | ✅ 完全支持 |
| Edge | 90+ | 最新版 | ✅ 完全支持 |
| Safari | 14+ | 最新版 | ⚠️ 基本支持 |
| IE | - | - | ❌ 不支持 |

#### 🔑 首次登录
```
默认管理员账户：
用户名：admin
密码：admin123
首次登录后请立即修改密码
```

### 2.2 界面布局

```
系统主界面布局：
┌─────────────────────────────────────────────────────────────┐
│ 🏠 顶部导航栏                                                │
│ 采购平台 | 📧消息(3) | 👤张三(采购员) | 🔔提醒 | 🚪退出      │
├─────────────────────────────────────────────────────────────┤
│ 📁侧边菜单 │                主工作区域                        │
│           │  ┌─────────────────────────────────────────┐    │
│ 📋 采购管理 │  │                                         │    │
│ ├ 需求申报  │  │           内容显示区域                   │    │
│ ├ 项目管理  │  │        (表单/表格/图表)                  │    │
│ └ 实施跟踪  │  │                                         │    │
│           │  └─────────────────────────────────────────┘    │
│ 🏢 供应商   │  ┌─────────────────────────────────────────┐    │
│ 📄 合同管理 │  │              操作按钮区域                │    │
│ 💰 财务管理 │  │    [新增] [编辑] [删除] [导出] [打印]     │    │
│ 📊 统计报表 │  └─────────────────────────────────────────┘    │
│ ⚙️ 系统设置 │                                              │
└─────────────────────────────────────────────────────────────┘
```

### 2.3 基本操作

#### 🎯 导航操作
- **菜单展开/收起**：点击左上角菜单图标
- **面包屑导航**：显示当前页面位置，可点击返回上级
- **快捷搜索**：顶部搜索框支持全局搜索
- **消息通知**：右上角消息图标显示未读消息数量

#### 📝 数据操作
- **新增记录**：点击"新增"按钮，填写表单后保存
- **编辑记录**：点击记录行的"编辑"按钮或双击记录行
- **删除记录**：选择记录后点击"删除"按钮，需要确认
- **批量操作**：勾选多条记录，使用批量操作按钮

#### 🔍 查询筛选
- **快速筛选**：使用表格上方的筛选条件
- **高级搜索**：点击"高级搜索"展开更多筛选条件
- **排序功能**：点击表格列标题进行排序
- **分页浏览**：使用表格底部的分页控件

---

## 3. 用户角色与权限

### 3.1 角色体系架构

```
角色层次结构：
                    ┌─ 系统管理员 (最高权限)
                    │
        ┌─ 管理层 ───┼─ 分管领导 (审批权限)
        │           │
        │           └─ 部门主任 (部门管理)
        │
组织架构 ┼─ 业务层 ───┼─ 采购员 (双人制)
        │           │
        │           ├─ 履约监督员 (专人监督)
        │           │
        │           └─ 财务人员 (付款审批)
        │
        └─ 监督层 ───┼─ 内审人员 (全程监督)
                    │
                    ├─ 法务人员 (合同审核)
                    │
                    └─ 涉密管理员 (安全管控)
```

### 3.2 详细角色定义

#### 👥 业务执行角色

**🏢 需求部门人员**
- **主要职责**：提出采购需求，参与验收确认
- **操作权限**：
  - ✅ 创建采购需求申请
  - ✅ 修改未提交的需求申请
  - ✅ 查看本部门的采购项目
  - ✅ 参与验收确认（多人制）
  - ❌ 不能查看其他部门的涉密项目

**👨‍💼 需求部门负责人**
- **主要职责**：审批本部门采购需求
- **操作权限**：
  - ✅ 审批本部门需求申请
  - ✅ 查看本部门所有采购项目
  - ✅ 指定验收人员
  - ⚠️ 重大项目需要上级审批

**🛒 采购实施人员（双人制）**
- **主要职责**：执行采购实施，组织验收工作
- **操作权限**：
  - ✅ 创建采购项目（必须双人）
  - ✅ 管理供应商报价
  - ✅ 签订采购合同
  - ✅ 组织验收工作
  - ✅ 申请付款报销
  - ⚠️ 关键操作需要双人确认

#### 👨‍⚖️ 监督管理角色

**🔍 履约监督人员**
- **主要职责**：专门负责合同履行监督
- **操作权限**：
  - ✅ 查看分配的监督项目
  - ✅ 记录履约监督日志
  - ✅ 发起履约预警
  - ✅ 评估供应商履约情况
  - ❌ 不能修改合同内容

**💰 财务人员**
- **主要职责**：预算控制，付款审批
- **操作权限**：
  - ✅ 审批付款申请
  - ✅ 查看财务相关数据
  - ✅ 设置预算控制
  - ✅ 生成财务报表
  - ⚠️ 大额付款需要分管领导审批

**🛡️ 内审人员**
- **主要职责**：全流程合规检查和审计监督
- **操作权限**：
  - ✅ 查看所有采购项目（除涉密）
  - ✅ 检查流程合规性
  - ✅ 生成审计报告
  - ✅ 发起合规预警
  - 🔒 涉密项目需要特殊授权

#### 🔐 特殊管理角色

**🔒 涉密项目管理员**
- **主要职责**：涉密项目全程安全管控
- **操作权限**：
  - ✅ 管理涉密项目权限
  - ✅ 审核涉密项目参与人员
  - ✅ 监控涉密项目操作日志
  - ✅ 设置安全管控策略
  - ⚠️ 最高级别的安全权限

**⚙️ 系统管理员**
- **主要职责**：系统维护和配置管理
- **操作权限**：
  - ✅ 用户账户管理
  - ✅ 角色权限配置
  - ✅ 系统参数设置
  - ✅ 数据备份恢复
  - ✅ 系统监控维护

### 3.3 权限控制矩阵

| 功能模块 | 需求部门 | 采购员 | 履约监督 | 财务人员 | 内审人员 | 涉密管理员 | 系统管理员 |
|---------|---------|--------|----------|----------|----------|------------|------------|
| **需求管理** |
| 需求申报 | ✅创建/修改 | 👁️查看 | ❌ | ❌ | 👁️查看 | 🔒按权限 | 🔧管理 |
| 需求审核 | ✅本部门审批 | 👁️查看 | ❌ | ❌ | 👁️查看 | 🔒按权限 | 🔧管理 |
| **采购实施** |
| 项目创建 | ❌ | ✅双人制 | ❌ | ❌ | 👁️查看 | 🔒按权限 | 🔧管理 |
| 供应商管理 | 👁️查看 | ✅管理 | 👁️查看 | ❌ | 👁️查看 | 🔒按权限 | 🔧管理 |
| 报价管理 | 👁️查看 | ✅管理 | 👁️监督 | ❌ | 👁️查看 | 🔒按权限 | 🔧管理 |
| **合同管理** |
| 合同签订 | 👁️查看 | ✅管理 | 👁️监督 | 👁️查看 | 👁️查看 | 🔒按权限 | 🔧管理 |
| 履约监督 | 👁️查看 | 👁️查看 | ✅监督 | ❌ | 👁️查看 | 🔒按权限 | 🔧管理 |
| **验收管理** |
| 验收组织 | ✅参与多人制 | ✅组织 | 👁️监督 | ❌ | 👁️查看 | 🔒按权限 | 🔧管理 |
| 验收确认 | ✅确认 | ✅确认 | 👁️监督 | ❌ | 👁️查看 | 🔒按权限 | 🔧管理 |
| **财务管理** |
| 付款申请 | 👁️查看 | ✅申请 | ❌ | 👁️查看 | 👁️查看 | 🔒按权限 | 🔧管理 |
| 付款审批 | ❌ | ❌ | ❌ | ✅审批 | 👁️查看 | 🔒按权限 | 🔧管理 |
| 预算管理 | 👁️查看 | 👁️查看 | ❌ | ✅管理 | 👁️查看 | 🔒按权限 | 🔧管理 |
| **涉密项目** |
| 涉密标识 | 🔒授权 | 🔒授权 | 🔒授权 | 🔒授权 | 🔒特殊授权 | ✅管理 | 🔧管理 |
| 权限管理 | ❌ | ❌ | ❌ | ❌ | ❌ | ✅管理 | 🔧管理 |

**图例说明：**
- ✅ 完全权限（创建、修改、删除）
- 👁️ 查看权限（只读）
- 🔒 按权限访问（需要特殊授权）
- ⚠️ 有条件权限（需要满足特定条件）
- ❌ 无权限
- 🔧 系统管理权限

---

## 4. 核心功能模块

### 4.1 功能模块总览

```
系统功能架构图：
                    ┌─ 需求申报管理
        ┌─ 采购管理 ─┼─ 采购实施管理
        │           └─ 采购跟踪管理
        │
        ├─ 供应商管理 ─┼─ 供应商注册
        │             ├─ 资质管理
        │             └─ 绩效评估
        │
核心功能 ┼─ 合同管理 ─┼─ 合同签订
        │           ├─ 履约监督
        │           └─ 验收管理
        │
        ├─ 财务管理 ─┼─ 预算控制
        │           ├─ 分期付款
        │           └─ 付款审批
        │
        └─ 系统管理 ─┼─ 用户权限
                    ├─ 数据统计
                    └─ 系统配置
```

### 4.2 采购管理模块

#### 📋 需求申报管理
**功能描述**：需求部门在线提交采购需求，支持涉密项目标识

**主要功能**：
- ✅ **在线申报**：支持多种商品类型的需求申报
- ✅ **附件上传**：支持需求说明书、技术规格等文件上传
- ✅ **涉密标识**：明确标注涉密项目，启用特殊管控
- ✅ **审批流程**：自动流转到相应审批人员
- ✅ **状态跟踪**：实时查看申请处理状态

**操作流程**：
```
需求部门 → 填写申报表 → 上传附件 → 提交申请 → 等待审核 → 审核通过 → 进入采购流程
```

#### 🛒 采购实施管理
**功能描述**：采购人员执行具体采购工作，严格执行双人制操作

**主要功能**：
- ✅ **项目创建**：基于审核通过的需求创建采购项目
- ✅ **双人制操作**：系统强制要求两名采购员共同操作
- ✅ **供应商邀请**：邀请合格供应商参与报价
- ✅ **报价管理**：收集、比较、分析供应商报价
- ✅ **成交确认**：确定成交供应商和成交价格

**双人制要求**：
```
操作环节          主操作员        副操作员        系统验证
项目创建    →    张三(采购员)  +  李四(采购员)  →  双人身份验证
供应商邀请  →    张三确认      +  李四确认      →  双人操作记录
成交确认    →    张三签字      +  李四签字      →  电子签名验证
```

### 4.3 供应商管理模块

#### 🏢 供应商注册
**功能描述**：供应商信息的统一管理和维护

**主要功能**：
- ✅ **基础信息管理**：企业名称、地址、联系方式等
- ✅ **资质证照管理**：营业执照、税务登记、行业资质等
- ✅ **银行账户管理**：收款账户信息维护
- ✅ **分类管理**：按行业、规模、合作级别分类
- ✅ **黑名单管理**：不良供应商记录和限制

#### 📊 绩效评估
**功能描述**：对供应商的历史表现进行量化评估

**评估维度**：
```
评估指标体系：
┌─ 产品质量 (30%) ─┼─ 质量合格率
│                 ├─ 用户满意度
│                 └─ 售后服务
├─ 交付能力 (25%) ─┼─ 按时交付率
│                 ├─ 交付数量准确性
│                 └─ 包装完整性
├─ 价格竞争力(20%)─┼─ 报价合理性
│                 ├─ 价格稳定性
│                 └─ 成本控制能力
├─ 合作态度 (15%) ─┼─ 沟通配合度
│                 ├─ 问题响应速度
│                 └─ 合同履行诚信度
└─ 创新能力 (10%) ─┼─ 技术创新
                  ├─ 服务创新
                  └─ 管理创新
```

### 4.4 合同管理模块

#### 📄 合同全生命周期管理
**功能描述**：从合同起草到归档的完整管理流程

**管理阶段**：
```
合同生命周期：
起草 → 审核 → 签订 → 履行 → 变更 → 验收 → 付款 → 归档
 ↓      ↓      ↓      ↓      ↓      ↓      ↓      ↓
法务   内审   双方   监督   审批   多人   财务   档案
审核   检查   签字   跟踪   流程   验收   处理   管理
```

#### 👨‍💼 履约监督管理
**功能描述**：专人负责合同履行过程的全程监督

**监督内容**：
- ✅ **进度监督**：跟踪合同履行进度，及时发现延期风险
- ✅ **质量监督**：监督产品/服务质量是否符合合同要求
- ✅ **变更监督**：监督合同变更的合规性和必要性
- ✅ **风险预警**：识别履约风险，及时发出预警信息

### 4.5 财务管理模块

#### 💰 分期付款管理
**功能描述**：支持复杂的多期付款计划和精确的时限控制

**付款管理特色**：
```
分期付款示例：
合同总金额：100万元
├─ 第1期：30万元 (合同签订后7天内)
├─ 第2期：50万元 (货物验收合格后15天内)
└─ 第3期：20万元 (质保期满后10天内)

系统自动计算：
- 各期付款到期日
- 剩余付款时间
- 超期风险预警
- 合规性检查
```

#### ⏰ 付款时限预警
**功能描述**：严格按照《保障中小企业款项支付条例》要求进行时限管控

**预警机制**：
```
预警级别设置：
🟢 正常状态：距离付款期限 > 10个工作日
🟡 提醒预警：距离付款期限 5-10个工作日
🟠 紧急预警：距离付款期限 1-5个工作日
🔴 超期预警：已超过付款期限
🚨 升级预警：超期3天以上，推送至分管领导
```

---

## 5. 业务流程指南

### 5.1 完整业务流程图

```
采购全流程业务图：
                                    ┌─ 涉密项目特殊流程
                                    │
需求申报 → 需求审核 → 采购实施 → 供应商报价 → 成交确认
   ↓         ↓         ↓           ↓          ↓
需求部门   后勤中心   双人制操作   至少3家     比价分析
   │         │         │           │          │
   └─────────┼─────────┼───────────┼──────────┘
             ↓         ↓           ↓
        合同签订 → 履约监督 → 验收管理 → 付款管理 → 项目归档
           ↓         ↓         ↓         ↓         ↓
        法务审核   专人监督   多人制验收  分期付款   档案管理
```

### 5.2 需求申报详细流程

#### 📝 Step 1: 需求申报准备
**操作人员**：需求部门人员
**前置条件**：已获得部门领导同意

**准备材料**：
- ✅ 采购需求说明书
- ✅ 技术规格要求文档
- ✅ 预算估算依据
- ✅ 使用部门确认函
- ⚠️ 涉密项目需要保密审查意见

#### 📋 Step 2: 在线填报
**操作路径**：`登录系统 → 采购管理 → 需求申报 → 新建申请`

**必填信息**：
```
基础信息：
├─ 物品名称*：具体、准确的物品名称
├─ 规格型号*：详细的技术规格和型号要求
├─ 采购数量*：具体数量和计量单位
├─ 预算金额*：预估采购金额
├─ 需求时间*：期望完成采购的时间
├─ 用途说明*：详细说明采购用途和必要性
└─ 涉密标识*：是否涉及国家秘密或敏感信息

特殊要求：
├─ 技术要求：特殊的技术指标或性能要求
├─ 质量标准：执行的质量标准或认证要求
├─ 交付要求：交付时间、地点、方式等要求
└─ 售后服务：保修期限、服务内容等要求
```

#### 📎 Step 3: 附件上传
**支持格式**：PDF、DOC、DOCX、XLS、XLSX、JPG、PNG
**文件大小**：单个文件不超过10MB，总大小不超过50MB

**建议上传**：
- 📄 需求说明书（详细描述采购需求）
- 📊 技术规格书（技术参数和性能要求）
- 💰 预算分析表（预算编制依据和明细）
- 📋 相关标准文件（执行的国家或行业标准）

#### ✅ Step 4: 提交审核
**提交前检查**：
- ✅ 所有必填字段已完整填写
- ✅ 附件已正确上传
- ✅ 信息准确无误
- ✅ 涉密标识正确

**提交后状态**：
- 🟡 **待审核**：已提交，等待后勤服务中心审核
- 🔄 **审核中**：正在审核过程中
- ✅ **审核通过**：可以进入采购实施阶段
- ❌ **审核退回**：需要修改后重新提交

### 5.3 采购实施详细流程

#### 🛒 Step 1: 项目立项（双人制）
**操作人员**：采购实施人员（必须2人）
**操作路径**：`采购管理 → 项目管理 → 新建项目`

**双人制操作流程**：
```
主操作员（张三）操作：
1. 登录系统，选择"新建项目"
2. 填写项目基本信息
3. 选择副操作员（李四）
4. 提交待确认

副操作员（李四）操作：
1. 登录系统，查看待确认项目
2. 核对项目信息
3. 确认无误后签字确认
4. 项目正式创建

系统验证：
- 验证两人都具有采购员权限
- 记录双人操作日志
- 生成电子签名
```

#### 🏢 Step 2: 供应商邀请
**最低要求**：至少邀请3家合格供应商
**邀请方式**：系统内消息 + 邮件通知 + 电话确认

**供应商筛选标准**：
- ✅ 具有相应经营范围
- ✅ 具备必要的资质证书
- ✅ 近三年无重大违法记录
- ✅ 财务状况良好
- ✅ 技术能力满足要求

#### 💰 Step 3: 报价收集与分析
**报价期限**：一般不少于5个工作日
**报价要求**：
```
报价文件必须包含：
├─ 商务报价单（价格明细）
├─ 技术方案书（技术规格确认）
├─ 资质证明文件（营业执照、资质证书等）
├─ 类似项目业绩（近三年相关项目经验）
├─ 售后服务方案（保修、维护等承诺）
└─ 其他要求文件（根据项目特殊要求）
```

**比价分析**：
```
综合评分标准：
├─ 价格分 (40%) ─ 最低价得满分，其他按比例计算
├─ 技术分 (35%) ─ 技术方案的先进性和可行性
├─ 商务分 (15%) ─ 资质、业绩、财务状况
└─ 服务分 (10%) ─ 售后服务方案和承诺
```

### 5.4 合同管理详细流程

#### 📄 Step 1: 合同起草
**起草依据**：采购项目信息 + 中标供应商报价
**合同要素**：
```
合同必备条款：
├─ 合同主体：采购方和供应商信息
├─ 标的物：货物/服务的详细描述
├─ 数量和质量：具体数量和质量标准
├─ 价款和支付：总价、分期付款安排
├─ 履行期限：交付时间和验收期限
├─ 违约责任：违约情形和责任承担
├─ 争议解决：争议处理方式和管辖
└─ 其他条款：保密、知识产权等特殊约定
```

#### ⚖️ Step 2: 法务审核（重要合同）
**审核标准**：
- 合同金额 > 50万元
- 涉及重要技术或服务
- 合同条款复杂
- 涉密项目

**审核内容**：
- ✅ 合同条款的合法性
- ✅ 权利义务的对等性
- ✅ 风险条款的合理性
- ✅ 争议解决的可操作性

#### 👨‍💼 Step 3: 履约监督
**监督人员**：专门指定的履约监督员
**监督职责**：
```
监督计划制定：
├─ 关键节点识别（交付、验收、付款等）
├─ 监督频次安排（每周/每月检查）
├─ 风险点分析（可能的履约风险）
└─ 应急预案制定（风险应对措施）

日常监督工作：
├─ 进度跟踪：定期了解项目进展情况
├─ 质量监督：检查产品/服务质量
├─ 沟通协调：协调解决履约中的问题
└─ 记录报告：详细记录监督情况
```

### 5.5 验收管理详细流程

#### 👥 Step 1: 验收组织（多人制）
**验收人员配置**：
- 最少2人，建议3-5人
- 必须包含需求部门代表
- 建议包含技术专家
- 重要项目包含质量监督员

**验收准备**：
```
验收前准备工作：
├─ 制定验收方案（验收标准、方法、程序）
├─ 准备验收工具（检测设备、测量工具等）
├─ 通知相关人员（验收时间、地点、要求）
└─ 准备验收文档（验收单、检查表等）
```

#### 🔍 Step 2: 现场验收
**验收内容**：
```
验收检查项目：
├─ 数量验收 ─┼─ 清点实际数量
│            ├─ 核对包装清单
│            └─ 确认计量单位
├─ 质量验收 ─┼─ 外观质量检查
│            ├─ 功能性能测试
│            └─ 技术指标验证
├─ 规格验收 ─┼─ 型号规格确认
│            ├─ 技术参数核对
│            └─ 配置清单检查
└─ 文档验收 ─┼─ 产品说明书
             ├─ 质量证明文件
             └─ 保修卡等资料
```

#### 📋 Step 3: 验收结论
**验收结果**：
- ✅ **验收合格**：完全符合合同要求，可以付款
- ⚠️ **有条件合格**：基本符合要求，但有小问题需要整改
- ❌ **验收不合格**：不符合合同要求，需要退货或重新供货

**验收文档**：
```
验收文档清单：
├─ 验收报告（详细的验收过程和结论）
├─ 验收单（验收人员签字确认）
├─ 问题清单（发现的问题和整改要求）
├─ 照片资料（现场验收照片）
└─ 测试报告（技术测试数据和结果）
```

### 5.6 付款管理详细流程

#### 💰 Step 1: 付款申请
**申请条件**：验收合格 + 发票齐全
**申请材料**：
```
付款申请材料清单：
├─ 付款申请单（申请金额、付款理由等）
├─ 验收报告（验收合格证明）
├─ 发票原件（增值税专用发票或普通发票）
├─ 合同复印件（加盖公章）
├─ 银行账户信息（收款方账户信息）
└─ 其他材料（根据具体要求）
```

#### ⏰ Step 2: 时限计算
**法定时限**：按照《保障中小企业款项支付条例》
- 一般情况：验收合格后30日内支付
- 合同约定：可约定具体期限，但不超过60日
- 检验验收：以验收合格日期为起算点

**系统自动计算**：
```
时限计算示例：
验收日期：2025年6月1日
付款期限：30个工作日
到期日期：2025年7月15日（扣除周末和节假日）

预警时间点：
├─ 7月1日：提前10个工作日黄色预警
├─ 7月8日：提前5个工作日橙色预警
├─ 7月12日：提前3个工作日红色预警
└─ 7月16日：超期1日紧急预警
```

#### 💳 Step 3: 付款审批
**审批流程**：
```
付款审批层级：
小额付款（≤10万） → 财务科长审批
中额付款（10-50万）→ 财务科长 → 分管领导
大额付款（>50万） → 财务科长 → 分管领导 → 主要领导
```

**审批要点**：
- ✅ 付款依据是否充分
- ✅ 付款金额是否准确
- ✅ 收款账户是否正确
- ✅ 是否在付款期限内

---

## 6. 特殊功能说明

### 6.1 涉密项目管理

#### 🔒 涉密项目识别与标注
**涉密项目定义**：涉及国家秘密、商业秘密或敏感信息的采购项目

**标注时机**：
- ✅ 需求申报阶段：需求部门明确标注
- ✅ 项目创建阶段：采购人员确认标注
- ✅ 合同签订阶段：最终确认涉密属性

**涉密级别分类**：
```
涉密级别体系：
├─ 一般敏感 ─ 内部信息，限制范围传播
├─ 商业秘密 ─ 涉及商业机密，严格保密
├─ 工作秘密 ─ 涉及工作机密，专人管理
└─ 国家秘密 ─ 涉及国家机密，最高保密级别
```

#### 🛡️ 涉密项目安全管控

**访问权限管控**：
```
权限分级管理：
┌─ 涉密项目管理员 ─ 最高权限，可管理所有涉密项目
├─ 项目负责人 ─ 可访问负责的涉密项目
├─ 授权参与人员 ─ 可访问指定的涉密项目
└─ 监督审计人员 ─ 可查看涉密项目（限制操作）
```

**技术安全措施**：
- 🔐 **数据加密**：AES-256加密算法，密钥分级管理
- 🔍 **访问监控**：实时监控所有访问行为
- 📝 **操作审计**：详细记录每一次操作
- 🚫 **权限隔离**：涉密数据与普通数据物理隔离
- ⏰ **会话管理**：涉密项目会话时间限制

**操作安全要求**：
```
涉密项目操作规范：
├─ 登录验证 ─ 双因子认证（密码+短信验证码）
├─ 操作确认 ─ 重要操作需要二次身份验证
├─ 时间限制 ─ 涉密项目会话30分钟自动超时
├─ 地点限制 ─ 只能在指定地点访问涉密项目
├─ 设备限制 ─ 只能使用授权设备访问
└─ 人员限制 ─ 严格的人员准入和退出机制
```

### 6.2 双人制操作管理

#### 👥 双人制操作原理
**设计目的**：通过两人相互制约，防范单人操作风险，确保关键环节的安全性

**适用环节**：
- 🛒 采购项目创建
- 💰 大额资金操作
- 📄 重要合同签订
- 🔑 系统关键配置
- 🗑️ 重要数据删除

#### ⚙️ 双人制操作流程

**标准操作流程**：
```
双人制操作标准流程：
主操作员操作：
1. 登录系统，发起操作申请
2. 填写操作内容和理由
3. 选择副操作员
4. 提交待确认状态

副操作员操作：
1. 接收操作确认通知
2. 登录系统查看操作详情
3. 核实操作内容和必要性
4. 确认或拒绝操作申请

系统处理：
1. 验证两人身份和权限
2. 记录完整操作日志
3. 生成双人电子签名
4. 执行业务操作
```

**权限验证机制**：
```
双人制权限验证：
├─ 身份验证 ─ 两人都必须具有相应操作权限
├─ 角色验证 ─ 两人不能是同一角色（如都是主管）
├─ 关系验证 ─ 两人不能有直接利益关系
├─ 时间验证 ─ 操作确认必须在规定时间内完成
└─ 地点验证 ─ 重要操作需要在指定地点进行
```

### 6.3 多人验收管理

#### 👥 多人验收制度
**制度目的**：确保验收工作的客观性、公正性和准确性

**人员配置要求**：
```
验收人员配置标准：
基本配置（最少2人）：
├─ 需求部门代表 ─ 熟悉业务需求，负责功能验收
└─ 采购部门代表 ─ 熟悉采购要求，负责合同符合性验收

标准配置（3-4人）：
├─ 需求部门代表 ─ 业务需求验收
├─ 技术专家 ─ 技术指标验收
├─ 质量监督员 ─ 质量标准验收
└─ 采购部门代表 ─ 合同符合性验收

重要项目配置（5人以上）：
├─ 需求部门代表
├─ 技术专家（多名）
├─ 质量监督员
├─ 财务代表
├─ 法务代表
└─ 监督部门代表
```

#### 📋 验收协调机制

**意见分歧处理**：
```
验收意见分歧处理流程：
一致同意 → 验收通过
部分分歧 → 协商讨论 → 达成一致 → 验收通过
重大分歧 → 暂停验收 → 技术鉴定 → 重新验收
无法达成一致 → 上报领导 → 组织专家评审 → 最终决定
```

**验收质量保证**：
- ✅ **标准化验收**：统一的验收标准和程序
- ✅ **专业化验收**：专业人员负责专业领域验收
- ✅ **文档化验收**：完整的验收记录和文档
- ✅ **可追溯验收**：验收过程和结果可追溯

### 6.4 智能预警系统

#### ⏰ 付款时限预警

**预警触发机制**：
```
付款时限预警级别：
🟢 正常状态（>10个工作日）
├─ 状态：正常办理
├─ 提醒：无需特殊关注
└─ 操作：按正常流程处理

🟡 提醒预警（5-10个工作日）
├─ 状态：需要关注
├─ 提醒：系统消息提醒
└─ 操作：加快办理进度

🟠 紧急预警（1-5个工作日）
├─ 状态：紧急处理
├─ 提醒：邮件+短信提醒
└─ 操作：优先办理

🔴 超期预警（已超期）
├─ 状态：立即处理
├─ 提醒：电话+邮件+短信
└─ 操作：紧急处理并上报

🚨 升级预警（超期3天以上）
├─ 状态：重大问题
├─ 提醒：推送至分管领导
└─ 操作：启动应急处理机制
```

#### 📊 合规性预警

**合规检查项目**：
```
合规性监控指标：
├─ 双人制执行率 ─ 监控双人制操作的执行情况
├─ 多人验收率 ─ 监控验收环节的人员配置
├─ 时限合规率 ─ 监控各环节时限要求执行情况
├─ 文档完整率 ─ 监控必要文档的完整性
├─ 流程规范率 ─ 监控业务流程的规范性
└─ 权限合规率 ─ 监控权限使用的合规性
```

**预警处理机制**：
- 🔔 **实时监控**：系统7×24小时实时监控
- 📱 **多渠道通知**：系统消息、邮件、短信、电话
- 📊 **数据分析**：预警数据统计分析和趋势预测
- 📋 **处理跟踪**：预警处理过程跟踪和效果评估

---

## 7. 系统管理

### 7.1 用户管理

#### 👤 用户账户管理
**管理权限**：系统管理员
**管理功能**：
```
用户账户管理功能：
├─ 用户创建 ─ 新增用户账户，设置基本信息
├─ 用户编辑 ─ 修改用户信息，更新联系方式
├─ 用户禁用 ─ 临时禁用用户账户
├─ 用户删除 ─ 删除不再使用的账户（谨慎操作）
├─ 密码重置 ─ 重置用户密码
└─ 权限分配 ─ 分配用户角色和权限
```

**用户信息字段**：
- 👤 **基本信息**：姓名、工号、部门、职务
- 📞 **联系信息**：电话、邮箱、办公地址
- 🔐 **账户信息**：用户名、密码、状态
- 👥 **角色信息**：分配的角色和权限
- 📅 **时间信息**：创建时间、最后登录时间

#### 🔑 角色权限管理
**角色配置**：
```
角色权限配置界面：
┌─ 角色列表 ─┬─ 系统管理员 ─ [编辑] [删除]
│           ├─ 采购员 ─ [编辑] [删除]
│           ├─ 财务人员 ─ [编辑] [删除]
│           └─ [新增角色]
│
├─ 权限配置 ─┬─ 功能权限 ─ 菜单访问、操作权限
│           ├─ 数据权限 ─ 数据查看、编辑范围
│           └─ 特殊权限 ─ 涉密项目、系统配置
│
└─ 用户分配 ─ 为用户分配角色和权限
```

### 7.2 系统配置

#### ⚙️ 基础参数配置
**配置项目**：
```
系统参数配置：
├─ 业务参数 ─┬─ 付款时限设置（默认30个工作日）
│           ├─ 预警时间设置（10、5、3、1天）
│           ├─ 验收人员最少数量（默认2人）
│           └─ 双人制操作范围设置
│
├─ 安全参数 ─┬─ 密码复杂度要求
│           ├─ 会话超时时间
│           ├─ 登录失败锁定策略
│           └─ 涉密项目安全级别
│
└─ 系统参数 ─┬─ 文件上传大小限制
             ├─ 数据备份频率
             ├─ 日志保留时间
             └─ 邮件服务器配置
```

#### 📊 数据字典管理
**字典类型**：
- 📋 **采购方式**：公开招标、邀请招标、竞争性磋商等
- 🏢 **项目类型**：货物、服务、工程
- 💰 **付款方式**：一次性付款、分期付款
- 🔒 **涉密级别**：一般敏感、商业秘密、工作秘密、国家秘密

### 7.3 数据统计与报表

#### 📈 统计分析功能

**采购业务统计**：
```
采购统计分析：
├─ 采购执行统计 ─┬─ 按时间统计（月度、季度、年度）
│               ├─ 按部门统计（各部门采购情况）
│               ├─ 按类型统计（货物、服务、工程）
│               └─ 按金额统计（不同金额区间分布）
│
├─ 供应商统计 ─┬─ 供应商数量统计
│             ├─ 合作频次统计
│             ├─ 成交金额统计
│             └─ 绩效评分统计
│
└─ 合规性统计 ─┬─ 双人制执行率
               ├─ 多人验收率
               ├─ 付款及时率
               └─ 流程合规率
```

**财务管理统计**：
```
财务统计分析：
├─ 预算执行分析 ─┬─ 预算使用率
│               ├─ 预算超支情况
│               └─ 预算节约情况
│
├─ 付款分析 ─┬─ 付款及时性分析
│           ├─ 付款周期分析
│           ├─ 超期付款统计
│           └─ 部门付款排名
│
└─ 成本分析 ─┬─ 采购成本趋势
             ├─ 供应商价格比较
             └─ 成本节约分析
```

#### 📋 报表生成功能

**标准报表**：
- 📊 **采购执行报表**：采购项目执行情况汇总
- 💰 **财务分析报表**：预算执行和付款情况分析
- 🏢 **供应商分析报表**：供应商绩效和合作情况
- 🔍 **合规性检查报表**：各项制度执行情况检查

**自定义报表**：
```
自定义报表功能：
├─ 查询条件设置 ─ 时间范围、部门、项目类型等
├─ 字段选择 ─ 选择需要显示的数据字段
├─ 排序设置 ─ 设置数据排序方式
├─ 格式设置 ─ 选择报表格式（表格、图表）
└─ 导出功能 ─ 支持Excel、PDF、Word格式导出
```

### 7.4 系统维护

#### 🔧 日常维护
**维护内容**：
- 🗄️ **数据备份**：定期备份系统数据，确保数据安全
- 📝 **日志管理**：清理过期日志，监控系统运行状态
- 🔄 **系统更新**：安装系统补丁，更新功能模块
- 🔍 **性能监控**：监控系统性能，优化运行效率

#### 📊 系统监控
**监控指标**：
```
系统监控指标：
├─ 性能指标 ─┬─ CPU使用率
│           ├─ 内存使用率
│           ├─ 磁盘使用率
│           └─ 网络流量
│
├─ 业务指标 ─┬─ 用户在线数量
│           ├─ 系统响应时间
│           ├─ 错误发生率
│           └─ 业务处理量
│
└─ 安全指标 ─┬─ 登录失败次数
             ├─ 异常访问记录
             ├─ 权限违规操作
             └─ 数据访问审计
```

---

## 8. 常见问题

### 8.1 登录与权限问题

#### 🔐 登录相关问题

**Q1: 忘记密码怎么办？**
```
解决方案：
方案一：自助重置
1. 点击登录页面"忘记密码"链接
2. 输入用户名和注册邮箱
3. 查收邮件中的重置链接
4. 设置新密码

方案二：管理员重置
1. 联系系统管理员
2. 提供身份证明
3. 管理员重置密码
4. 首次登录后修改密码
```

**Q2: 账户被锁定怎么办？**
```
锁定原因：
├─ 密码错误次数过多（通常5次）
├─ 长时间未登录（超过90天）
├─ 管理员手动锁定
└─ 安全策略触发

解决方法：
1. 等待自动解锁（通常30分钟）
2. 联系系统管理员手动解锁
3. 确认账户状态和权限
```

**Q3: 登录后看不到某些菜单？**
```
可能原因：
├─ 权限不足 ─ 联系管理员分配权限
├─ 角色配置问题 ─ 检查角色权限设置
├─ 浏览器缓存 ─ 清除浏览器缓存重新登录
└─ 系统故障 ─ 联系技术支持
```

#### 👥 权限相关问题

**Q4: 如何申请涉密项目访问权限？**
```
申请流程：
1. 向涉密项目管理员提交申请
2. 填写《涉密项目访问权限申请表》
3. 提供必要的身份和资质证明
4. 等待审核批准
5. 获得权限后签署保密协议
```

**Q5: 双人制操作时找不到合适的副操作员？**
```
解决方案：
1. 确认副操作员具有相应权限
2. 确认副操作员当前在线状态
3. 选择其他符合条件的人员
4. 如无合适人员，联系管理员临时授权
```

### 8.2 业务操作问题

#### 📝 数据录入问题

**Q6: 数据录入错误如何修改？**
```
修改方案：
草稿状态 → 直接修改保存
已提交未审核 → 撤回修改后重新提交
审核中 → 联系审核人员，说明情况
已审核通过 → 走变更流程，需要重新审批
已完成 → 一般不允许修改，特殊情况需要领导审批
```

**Q7: 上传的附件无法打开？**
```
检查项目：
├─ 文件格式 ─ 确认是支持的格式（PDF、DOC、XLS等）
├─ 文件大小 ─ 确认未超过大小限制（通常10MB）
├─ 文件完整性 ─ 确认文件未损坏
├─ 浏览器设置 ─ 检查浏览器安全设置
└─ 网络问题 ─ 检查网络连接稳定性
```

#### 💰 财务相关问题

**Q8: 付款金额合计与成交金额不一致？**
```
解决步骤：
1. 检查各期付款金额录入是否正确
2. 确认是否有遗漏的付款期次
3. 检查合同金额是否录入正确
4. 系统会自动校验，根据提示调整
5. 如仍有问题，联系财务人员核实
```

**Q9: 付款预警信息如何处理？**
```
预警处理：
🟡 黄色预警 → 关注进度，准备付款材料
🟠 橙色预警 → 加快办理，优先处理
🔴 红色预警 → 立即处理，避免超期
🚨 紧急预警 → 紧急处理，上报领导
```

### 8.3 技术问题

#### 💻 系统使用问题

**Q10: 页面加载缓慢或无响应？**
```
排查步骤：
1. 检查网络连接是否正常
2. 清除浏览器缓存和Cookie
3. 关闭其他占用资源的程序
4. 尝试刷新页面或重新登录
5. 更换浏览器尝试
6. 联系技术支持
```

**Q11: 数据保存失败？**
```
可能原因及解决方案：
├─ 网络中断 → 检查网络连接，重新保存
├─ 会话超时 → 重新登录后再次操作
├─ 数据验证失败 → 检查必填字段和格式要求
├─ 权限不足 → 确认操作权限，联系管理员
└─ 系统故障 → 联系技术支持
```

**Q12: 如何导出数据？**
```
导出步骤：
1. 进入相应的数据列表页面
2. 设置筛选条件（可选）
3. 点击"导出"按钮
4. 选择导出格式（Excel、PDF等）
5. 等待文件生成
6. 下载生成的文件
```

---

## 9. 技术支持

### 9.1 支持体系

#### 📞 技术支持层级
```
技术支持体系：
一级支持（用户自助）：
├─ 用户指南查阅
├─ 常见问题自查
├─ 在线帮助文档
└─ 操作视频教程

二级支持（业务支持）：
├─ 业务流程咨询
├─ 操作培训指导
├─ 权限申请协助
└─ 业务问题解答

三级支持（技术支持）：
├─ 系统技术问题
├─ 数据恢复处理
├─ 系统配置调整
└─ 紧急故障处理

四级支持（开发支持）：
├─ 系统功能开发
├─ 重大bug修复
├─ 系统架构调整
└─ 性能优化升级
```

#### 🕐 支持时间
- **工作时间支持**：周一至周五 8:30-17:30
- **紧急支持**：7×24小时（重大故障）
- **响应时间**：
  - 一般问题：4小时内响应
  - 紧急问题：1小时内响应
  - 重大故障：30分钟内响应

### 9.2 联系方式

#### 📧 联系信息
```
技术支持联系方式：
📞 技术支持热线：400-XXX-XXXX
📧 技术支持邮箱：<EMAIL>
💬 在线客服：系统内置客服功能
🎫 工单系统：support.procurement.com

业务咨询联系方式：
📞 业务咨询热线：400-XXX-XXXX (转2)
📧 业务咨询邮箱：<EMAIL>
👨‍💼 业务经理：张经理 138-XXXX-XXXX

系统管理员联系方式：
👨‍💻 系统管理员：李管理员
📞 直线电话：010-XXXX-XXXX
📧 邮箱：<EMAIL>
📱 紧急联系：139-XXXX-XXXX
```

### 9.3 问题反馈

#### 🐛 Bug反馈流程
```
Bug反馈标准流程：
1. 问题描述
   ├─ 详细描述问题现象
   ├─ 提供错误截图或录屏
   ├─ 说明操作步骤
   └─ 提供错误信息

2. 环境信息
   ├─ 操作系统版本
   ├─ 浏览器类型和版本
   ├─ 网络环境
   └─ 用户角色和权限

3. 重现步骤
   ├─ 详细的操作步骤
   ├─ 预期结果
   ├─ 实际结果
   └─ 重现频率

4. 影响评估
   ├─ 影响范围（个人/部门/全系统）
   ├─ 紧急程度（低/中/高/紧急）
   ├─ 业务影响
   └─ 建议解决时间
```

#### 💡 功能建议
```
功能建议提交：
1. 建议描述
   ├─ 功能需求描述
   ├─ 使用场景说明
   ├─ 预期效果
   └─ 参考案例

2. 需求分析
   ├─ 业务价值评估
   ├─ 用户群体分析
   ├─ 实现难度评估
   └─ 优先级建议

3. 提交渠道
   ├─ 邮件提交：<EMAIL>
   ├─ 在线表单：系统内建议反馈功能
   ├─ 电话沟通：业务咨询热线
   └─ 定期调研：用户满意度调查
```

---

## 10. 附录

### 10.1 Windows 10+ 开发环境配置

#### 💻 硬件要求
```
最低配置要求：
├─ 处理器：Intel i3 或 AMD 同等级别
├─ 内存：8GB RAM
├─ 存储：50GB 可用空间
├─ 网络：稳定的网络连接
└─ 显示器：1366×768 分辨率

推荐配置：
├─ 处理器：Intel i5 或 AMD 同等级别
├─ 内存：16GB RAM
├─ 存储：100GB 可用空间（SSD推荐）
├─ 网络：宽带网络连接
└─ 显示器：1920×1080 分辨率
```

#### 🛠️ 软件环境
```
必需软件清单：
开发环境：
├─ JDK 11+ (OpenJDK 推荐)
├─ Node.js 16.x+ (包含 npm)
├─ Git 最新版本
└─ IDE (IntelliJ IDEA / VS Code)

数据库环境：
├─ MySQL 8.0+
├─ Redis 6.0+
├─ MySQL Workbench (可选)
└─ Redis Desktop Manager (可选)

浏览器支持：
├─ Chrome 90+ (推荐)
├─ Firefox 88+
├─ Edge 90+
└─ Safari 14+ (基本支持)
```

### 10.2 系统配置信息

#### 🗄️ 数据库配置
```
开发环境数据库配置：
主机：localhost
端口：3306
用户名：root
密码：root
数据库：procurement_platform
字符集：utf8mb4
排序规则：utf8mb4_unicode_ci

Redis配置：
主机：localhost
端口：6379
密码：(无密码)
数据库：0
```

#### 🌐 网络配置
```
开发环境端口配置：
前端开发服务器：5173 (Vite)
后端API服务：8080 (Spring Boot)
MySQL数据库：3306
Redis缓存：6379
Nginx (生产)：80/443

防火墙设置：
确保以上端口在Windows防火墙中允许通过
```

### 10.3 法规依据与标准

#### 📜 相关法律法规
```
主要法规依据：
├─ 《中华人民共和国政府采购法》
├─ 《政府采购法实施条例》
├─ 《保障中小企业款项支付条例》
├─ 《政府采购货物和服务招标投标管理办法》
└─ 各地方政府采购管理办法

重要条款：
├─ 政府采购法第二十二条：供应商资格要求
├─ 保障中小企业款项支付条例第九条：付款期限
├─ 保障中小企业款项支付条例第十条：付款方式
└─ 政府采购法第七十一条：监督检查
```

#### 📋 技术标准
```
技术标准参考：
安全标准：
├─ GB/T 22239-2019 信息安全技术 网络安全等级保护基本要求
├─ GB/T 25070-2019 信息安全技术 网络安全等级保护安全设计技术要求
└─ ISO/IEC 27001:2013 信息安全管理体系要求

开发标准：
├─ GB/T 8566-2007 信息技术 软件生存周期过程
├─ GB/T 16260.1-2006 软件工程 产品质量
└─ ISO/IEC 25010:2011 软件产品质量要求和评价
```

### 10.4 版本更新记录

#### 📝 版本历史
```
版本更新记录：
v2.0 (2025-06-29)
├─ 重新编写用户指南，内容更加详细完整
├─ 增加Windows 10+环境配置说明
├─ 完善业务流程操作指南
├─ 增加常见问题解答
└─ 优化文档结构和排版

v1.0 (2025-06-01)
├─ 初始版本用户指南
├─ 基础功能说明
├─ 简单操作指导
└─ 基本技术要求
```

#### 🔄 后续更新计划
```
计划更新内容：
短期计划（1-3个月）：
├─ 增加操作视频教程
├─ 完善API接口文档
├─ 增加移动端使用指南
└─ 优化用户体验说明

中期计划（3-6个月）：
├─ 增加高级功能使用指南
├─ 完善系统集成说明
├─ 增加性能优化建议
└─ 完善安全配置指南

长期计划（6-12个月）：
├─ 增加多语言版本
├─ 完善培训教材
├─ 增加最佳实践案例
└─ 建立知识库系统
```

---

## 📞 紧急联系信息

```
紧急情况联系方式：
🚨 系统故障紧急热线：400-XXX-XXXX (24小时)
📧 紧急邮箱：<EMAIL>
👨‍💻 技术负责人：王工程师 138-XXXX-XXXX
👨‍💼 项目负责人：李经理 139-XXXX-XXXX
```

---

**文档结束**

> **重要提示**：本用户指南是采购数字化综合管理平台的官方使用文档，请妥善保管。文档内容会根据系统更新和用户反馈持续完善，请关注最新版本。如有任何疑问或建议，欢迎随时联系我们的技术支持团队。

**版权声明**：本文档版权归采购数字化综合管理平台项目组所有，未经授权不得复制、传播或用于商业用途。

**最后更新**：2025年6月29日
