# 采购数字化综合管理平台API接口文档

**文档版本：** 1.0  
**编写日期：** 2025年6月  
**文档类型：** API接口文档（AID）

## 1. API设计理念与架构

### 1.1 API设计的本质理解

API接口就像是一座城市的交通系统，它不仅要连接城市的各个区域（前端和后端），还要确保交通的顺畅、安全和高效。想象一下，如果把我们的采购平台比作一座现代化的办公大楼，那么API就是这座大楼的电梯系统、门禁系统和通信系统的集合体。

每个API端点就像是大楼中的一个特定服务窗口，比如项目管理窗口、财务处理窗口、合同办理窗口等。访问者（前端应用）需要知道去哪个窗口（API端点）办理什么业务（调用什么功能），需要带什么材料（请求参数），以及能够得到什么服务（响应数据）。

对于采购平台这样涉及敏感业务数据和严格合规要求的系统，API设计必须像银行的服务系统一样严谨。每个接口都需要严格的身份验证、权限控制、数据验证和操作审计。同时，由于涉及双人制操作、涉密项目管控等特殊业务需求，我们的API设计还需要具备特殊的安全机制和业务流程支持。

### 1.2 RESTful设计原则深度解析

我们采用RESTful架构风格设计API，这就像是为城市交通制定统一的交通规则一样，让所有的交通参与者都能够按照相同的规则有序运行。

**资源导向的设计思维**

RESTful设计的核心思想是以资源为中心，而不是以动作为中心。这就像是图书馆的管理系统，我们关注的是书籍、读者、借阅记录这些具体的资源，而不是"借书"、"还书"这些动作。在我们的采购平台中，核心资源包括项目（Project）、用户（User）、合同（Contract）、供应商（Supplier）、付款记录（Payment）等。

每个资源都有其唯一的标识符（URI），就像每本书都有唯一的图书编号一样。比如，项目资源的URI设计为 `/api/projects/{id}`，其中 `{id}` 是项目的唯一标识符。这种设计让资源的访问路径清晰明确，便于理解和维护。

**HTTP动词的语义化使用**

HTTP动词就像是我们对资源进行操作的指令词，每个动词都有其特定的语义和用途。GET动词用于获取资源信息，就像是查阅图书；POST动词用于创建新资源，就像是新书入库；PUT动词用于完整更新资源，就像是更换图书的完整信息；PATCH动词用于部分更新资源，就像是修改图书的某些属性；DELETE动词用于删除资源，就像是图书下架。

在我们的API设计中，每个HTTP动词的使用都严格遵循RESTful规范。比如，`GET /api/projects` 用于获取项目列表，`POST /api/projects` 用于创建新项目，`PUT /api/projects/{id}` 用于更新指定项目的完整信息，`DELETE /api/projects/{id}` 用于删除指定项目。

**状态无关性的设计理念**

RESTful API的无状态特性就像是每次银行交易都是独立的一样，每次API调用都必须包含完整的请求信息，服务器不会保存客户端的状态信息。这种设计虽然增加了每次请求的数据传输量，但大大提升了系统的可扩展性和可靠性。

在我们的设计中，用户的身份验证通过JWT令牌实现，每次请求都需要携带有效的令牌。令牌中包含了用户身份、权限信息和过期时间等关键信息，服务器可以根据令牌独立验证每次请求的合法性。

### 1.3 API版本管理策略

API版本管理就像是软件的版本发布一样重要，需要在保持向后兼容性的同时，为系统的演进提供足够的灵活性。

我们采用URL路径版本管理策略，在API的URL中包含版本号，如 `/api/v1/projects`。这种方式的好处是版本信息清晰明确，便于客户端选择合适的API版本。同时，我们也支持通过HTTP头部指定版本号的方式，为特殊场景提供灵活性。

版本兼容性策略遵循语义化版本规范。主版本号变更表示不兼容的API修改，次版本号变更表示向下兼容的功能性新增，修订版本号变更表示向下兼容的问题修正。我们承诺在同一主版本内保持API的向后兼容性，给客户端充足的时间进行升级适配。

## 2. 认证与授权机制

### 2.1 JWT令牌认证体系

JWT（JSON Web Token）认证就像是现代化的电子身份证系统，它不仅能够证明用户的身份，还能携带用户的权限信息和其他相关数据。

**JWT令牌结构设计**

我们设计的JWT令牌包含三个部分：头部（Header）、载荷（Payload）和签名（Signature）。头部包含令牌类型和签名算法信息；载荷包含用户身份、权限、过期时间等关键信息；签名用于验证令牌的完整性和真实性。

```json
{
  "header": {
    "typ": "JWT",
    "alg": "HS256"
  },
  "payload": {
    "sub": "123456",
    "username": "zhangsan",
    "real_name": "张三",
    "department": "财务科",
    "roles": ["PROJECT_MANAGER", "FINANCIAL_OFFICER"],
    "permissions": ["project:create", "project:update", "payment:approve"],
    "has_classified_permission": true,
    "iat": 1640995200,
    "exp": 1641081600
  }
}
```

**令牌生命周期管理**

令牌的生命周期管理就像是门票的有效期管理一样，需要在安全性和用户体验之间找到平衡点。我们设计了双令牌机制：访问令牌（Access Token）用于API调用验证，有效期较短（2小时）；刷新令牌（Refresh Token）用于获取新的访问令牌，有效期较长（7天）。

当访问令牌过期时，客户端可以使用刷新令牌获取新的访问令牌，避免用户频繁登录。当刷新令牌也过期时，用户需要重新登录。这种设计既保证了安全性，又提供了良好的用户体验。

### 2.2 基于角色的权限控制

我们的权限控制系统就像是公司的职位体系一样，不同的角色拥有不同的职责和权限。

**角色权限矩阵设计**

```json
{
  "roles": {
    "SYSTEM_ADMIN": {
      "name": "系统管理员",
      "permissions": [
        "user:*", "role:*", "system:*"
      ]
    },
    "PROJECT_MANAGER": {
      "name": "项目管理员", 
      "permissions": [
        "project:create", "project:update", "project:view",
        "contract:create", "contract:update", "contract:view"
      ]
    },
    "FINANCIAL_OFFICER": {
      "name": "财务人员",
      "permissions": [
        "payment:create", "payment:approve", "payment:view",
        "budget:view", "financial_report:view"
      ]
    },
    "PROCUREMENT_STAFF": {
      "name": "采购人员",
      "permissions": [
        "project:view", "supplier:*", "quotation:*"
      ]
    },
    "AUDITOR": {
      "name": "审计人员",
      "permissions": [
        "audit_log:view", "dual_operation:view", "report:view"
      ]
    }
  }
}
```

**动态权限验证机制**

权限验证就像是多重安全检查一样，需要在多个层面进行验证。首先验证用户身份的真实性，然后验证用户是否具有访问特定资源的权限，最后验证用户是否具有执行特定操作的权限。

```java
@RestController
@RequestMapping("/api/v1/projects")
public class ProjectController {
    
    @PreAuthorize("hasPermission('project', 'view')")
    @GetMapping("/{id}")
    public ResponseEntity<ProjectVO> getProject(@PathVariable Long id) {
        // 获取项目详情的实现
    }
    
    @PreAuthorize("hasPermission('project', 'create')")
    @PostMapping
    public ResponseEntity<ProjectVO> createProject(@RequestBody ProjectCreateDTO projectDTO) {
        // 创建项目的实现
    }
    
    @PreAuthorize("hasPermission('project', 'update') and @projectService.isProjectOwner(#id)")
    @PutMapping("/{id}")
    public ResponseEntity<ProjectVO> updateProject(
            @PathVariable Long id, 
            @RequestBody ProjectUpdateDTO projectDTO) {
        // 更新项目的实现
    }
}
```

### 2.3 涉密项目特殊权限控制

涉密项目的权限控制就像是军事基地的安全管理一样，需要更加严格和细致的控制机制。

**涉密权限分级管理**

我们建立了分级的涉密权限管理体系，就像军队的保密等级一样。不同等级的涉密项目需要相应等级的权限才能访问。权限等级包括普通权限、机密权限和绝密权限。

```java
@RestController
@RequestMapping("/api/v1/classified-projects")
@PreAuthorize("hasRole('CLASSIFIED_USER')")
public class ClassifiedProjectController {
    
    @PreAuthorize("hasClassifiedPermission('SECRET')")
    @GetMapping("/{id}")
    public ResponseEntity<ClassifiedProjectVO> getClassifiedProject(@PathVariable Long id) {
        // 特殊的权限验证逻辑
        if (!classifiedPermissionService.canAccessProject(id, getCurrentUser())) {
            throw new AccessDeniedException("无权访问该涉密项目");
        }
        
        // 获取涉密项目详情
        ClassifiedProjectVO project = classifiedProjectService.getProject(id);
        
        // 记录访问日志
        auditService.logClassifiedAccess(id, getCurrentUser(), "VIEW");
        
        return ResponseEntity.ok(project);
    }
}
```

## 3. 核心业务API设计

### 3.1 项目管理API

项目管理API是整个系统的核心，就像是建筑工程的总指挥部一样，需要协调和管理各种复杂的业务流程。

**项目CRUD操作API**

```yaml
# 获取项目列表
GET /api/v1/projects
Parameters:
  - page: 页码 (默认: 1)
  - size: 页面大小 (默认: 20)
  - project_name: 项目名称 (可选)
  - project_type: 项目类型 (可选)
  - project_status: 项目状态 (可选)
  - date_range: 日期范围 (可选)
Response: 200 OK
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "content": [
      {
        "id": 1,
        "project_name": "办公设备采购项目",
        "project_type": "货物",
        "budget_amount": 500000.00,
        "transaction_amount": 480000.00,
        "project_status": "CONTRACTED",
        "demand_department": "行政科",
        "created_time": "2025-06-01T10:00:00",
        "is_classified": false
      }
    ],
    "page": 1,
    "size": 20,
    "total_elements": 50,
    "total_pages": 3
  },
  "timestamp": "2025-06-26T14:30:00"
}
```

**项目状态变更API**

项目状态变更是一个特别重要的操作，因为它可能触发双人制操作的验证流程。

```yaml
# 变更项目状态
POST /api/v1/projects/{id}/status-change
Parameters:
  - id: 项目ID
Request Body:
{
  "new_status": "IMPLEMENTING",
  "change_reason": "项目审核通过，开始进入采购实施阶段",
  "supporting_documents": ["approval_doc_url"]
}
Response: 200 OK
{
  "code": 200,
  "message": "状态变更申请已提交",
  "data": {
    "operation_id": 12345,
    "operation_type": "PROJECT_STATUS_CHANGE",
    "status": "PENDING_DUAL_CONFIRMATION",
    "first_operator": "张三",
    "pending_second_operator": true,
    "estimated_completion_time": "2025-06-26T16:30:00"
  }
}
```

### 3.2 双人制操作API

双人制操作API是系统合规性的重要保障，就像银行的双重签字制度一样关键。

**发起双人制操作**

```yaml
# 发起双人制操作
POST /api/v1/dual-operations
Request Body:
{
  "operation_type": "PROJECT_STATUS_CHANGE",
  "business_id": 123,
  "business_type": "PROJECT",
  "operation_content": {
    "project_id": 123,
    "old_status": "APPROVED",
    "new_status": "IMPLEMENTING",
    "reason": "项目审核通过，开始采购实施"
  },
  "operation_description": "项目状态从审核通过变更为采购实施"
}
Response: 201 Created
{
  "code": 201,
  "message": "双人制操作已发起，等待第二人确认",
  "data": {
    "operation_id": 12345,
    "operation_type": "PROJECT_STATUS_CHANGE",
    "business_id": 123,
    "status": "PENDING",
    "first_operator": {
      "id": 1001,
      "name": "张三",
      "department": "采购科"
    },
    "operation_time": "2025-06-26T14:30:00",
    "expires_at": "2025-06-27T14:30:00"
  }
}
```

**确认双人制操作**

```yaml
# 确认双人制操作
POST /api/v1/dual-operations/{operation_id}/confirm
Parameters:
  - operation_id: 操作ID
Request Body:
{
  "is_confirmed": true,
  "confirmation_note": "已核实项目审核文件，同意状态变更",
  "confirmation_evidence": ["review_evidence_url"]
}
Response: 200 OK
{
  "code": 200,
  "message": "双人制操作确认成功，业务操作已执行",
  "data": {
    "operation_id": 12345,
    "status": "CONFIRMED",
    "second_operator": {
      "id": 1002,
      "name": "李四",
      "department": "采购科"
    },
    "confirmation_time": "2025-06-26T15:45:00",
    "business_execution_result": {
      "success": true,
      "project_status": "IMPLEMENTING",
      "message": "项目状态变更成功"
    }
  }
}
```

### 3.3 财务管理API

财务管理API涉及资金安全，需要特别严格的验证和控制机制。

**付款计划管理**

```yaml
# 创建付款计划
POST /api/v1/projects/{project_id}/payment-plans
Parameters:
  - project_id: 项目ID
Request Body:
{
  "contract_id": 456,
  "payment_plans": [
    {
      "plan_sequence": 1,
      "plan_amount": 240000.00,
      "plan_date": "2025-07-15",
      "payment_condition": "合同签订后30天内支付50%"
    },
    {
      "plan_sequence": 2,
      "plan_amount": 240000.00,
      "plan_date": "2025-09-15",
      "payment_condition": "货物验收合格后支付余款"
    }
  ]
}
Response: 201 Created
{
  "code": 201,
  "message": "付款计划创建成功",
  "data": {
    "payment_plans": [
      {
        "id": 1001,
        "plan_sequence": 1,
        "plan_amount": 240000.00,
        "plan_date": "2025-07-15",
        "plan_status": "PLANNED"
      },
      {
        "id": 1002,
        "plan_sequence": 2,
        "plan_amount": 240000.00,
        "plan_date": "2025-09-15",
        "plan_status": "PLANNED"
      }
    ],
    "total_amount": 480000.00,
    "contract_amount_verification": {
      "is_matched": true,
      "contract_amount": 480000.00,
      "plan_total_amount": 480000.00
    }
  }
}
```

**付款记录API**

```yaml
# 创建付款记录
POST /api/v1/projects/{project_id}/payment-records
Request Body:
{
  "plan_id": 1001,
  "payment_amount": 240000.00,
  "payment_date": "2025-07-15",
  "payment_method": "银行转账",
  "payment_note": "首期付款，合同签订后30天",
  "voucher_number": "FZ-2025-0715-001"
}
Response: 201 Created
{
  "code": 201,
  "message": "付款记录创建成功，等待审批",
  "data": {
    "payment_record_id": 2001,
    "payment_amount": 240000.00,
    "payment_status": "PENDING_APPROVAL",
    "approval_workflow": {
      "current_step": "DEPARTMENT_APPROVAL",
      "next_approver": "科室负责人",
      "estimated_approval_time": "2025-07-16T10:00:00"
    },
    "amount_verification": {
      "project_total_paid": 240000.00,
      "project_remaining": 240000.00,
      "contract_amount": 480000.00,
      "is_within_limit": true
    }
  }
}
```

### 3.4 工作日计算API

工作日计算API是付款期限管理的核心支撑，就像精密的时钟一样需要绝对准确。

```yaml
# 计算付款期限
POST /api/v1/workdays/calculate-deadline
Request Body:
{
  "start_date": "2025-06-26",
  "workdays": 30,
  "calendar_type": "STANDARD"
}
Response: 200 OK
{
  "code": 200,
  "message": "工作日计算完成",
  "data": {
    "start_date": "2025-06-26",
    "workdays_required": 30,
    "deadline_date": "2025-08-15",
    "calculation_details": {
      "total_calendar_days": 50,
      "weekend_days": 14,
      "holiday_days": 6,
      "working_days_counted": 30
    },
    "holidays_included": [
      {
        "date": "2025-07-01",
        "name": "建党节",
        "type": "NATIONAL"
      }
    ]
  }
}

# 验证日期是否为工作日
GET /api/v1/workdays/is-workday?date=2025-06-26
Response: 200 OK
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "date": "2025-06-26",
    "is_workday": true,
    "day_of_week": "星期四",
    "day_type": "NORMAL_WORKDAY"
  }
}
```

## 4. 数据传输格式规范

### 4.1 统一响应格式

统一的响应格式就像是标准化的包装盒一样，让接收者能够以相同的方式处理不同的内容。

**成功响应格式**

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 具体的业务数据
  },
  "timestamp": "2025-06-26T14:30:00",
  "request_id": "req_123456789"
}
```

**分页响应格式**

```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "content": [
      // 数据列表
    ],
    "page": 1,
    "size": 20,
    "total_elements": 100,
    "total_pages": 5,
    "is_first": true,
    "is_last": false,
    "has_next": true,
    "has_previous": false
  },
  "timestamp": "2025-06-26T14:30:00"
}
```

### 4.2 错误处理规范

错误处理就像是医生的诊断报告一样，需要准确地描述问题的性质和解决方案。

**标准错误响应格式**

```json
{
  "code": 400,
  "message": "请求参数错误",
  "error": {
    "error_type": "VALIDATION_ERROR",
    "error_code": "E40001",
    "error_details": [
      {
        "field": "budget_amount",
        "message": "预算金额必须大于0",
        "value": "-1000"
      }
    ]
  },
  "timestamp": "2025-06-26T14:30:00",
  "request_id": "req_123456789"
}
```

**业务错误响应格式**

```json
{
  "code": 422,
  "message": "业务规则验证失败",
  "error": {
    "error_type": "BUSINESS_RULE_ERROR",
    "error_code": "E42201",
    "error_message": "付款总额不能超过合同金额",
    "error_context": {
      "contract_amount": 480000.00,
      "total_paid": 240000.00,
      "current_payment": 250000.00,
      "exceeded_amount": 10000.00
    }
  },
  "timestamp": "2025-06-26T14:30:00"
}
```

### 4.3 数据验证规范

数据验证就像是质量检查员一样，需要确保每一个进入系统的数据都符合标准要求。

**请求参数验证**

```java
@PostMapping("/projects")
public ResponseEntity<ProjectVO> createProject(@Valid @RequestBody ProjectCreateDTO projectDTO) {
    // 创建项目逻辑
}

// DTO类中的验证注解
public class ProjectCreateDTO {
    
    @NotBlank(message = "项目名称不能为空")
    @Length(max = 255, message = "项目名称长度不能超过255个字符")
    private String projectName;
    
    @NotNull(message = "项目类型不能为空")
    @Enumerated(EnumType.STRING)
    private ProjectType projectType;
    
    @NotNull(message = "预算金额不能为空")
    @DecimalMin(value = "0.01", message = "预算金额必须大于0")
    @Digits(integer = 13, fraction = 2, message = "预算金额格式不正确")
    private BigDecimal budgetAmount;
    
    @NotBlank(message = "需求部门不能为空")
    private String demandDepartment;
    
    // 自定义验证逻辑
    @AssertTrue(message = "政府采购项目不能同时标记为涉密项目")
    public boolean isValidGovernmentClassifiedCombination() {
        return !(isGovernmentProcurement && isClassified);
    }
}
```

## 5. 安全机制实现

### 5.1 请求签名验证

请求签名验证就像是古代的信物验证一样，确保请求确实来自可信的源头。

**签名算法实现**

```java
@Component
public class RequestSignatureValidator {
    
    private final String SECRET_KEY = "your-secret-key";
    
    public boolean validateSignature(HttpServletRequest request, String signature) {
        // 构建签名字符串
        String signatureString = buildSignatureString(request);
        
        // 计算期望的签名
        String expectedSignature = calculateSignature(signatureString);
        
        // 比较签名
        return MessageDigest.isEqual(
            signature.getBytes(StandardCharsets.UTF_8),
            expectedSignature.getBytes(StandardCharsets.UTF_8)
        );
    }
    
    private String buildSignatureString(HttpServletRequest request) {
        StringBuilder builder = new StringBuilder();
        
        // 添加HTTP方法
        builder.append(request.getMethod()).append("\n");
        
        // 添加请求路径
        builder.append(request.getRequestURI()).append("\n");
        
        // 添加时间戳
        String timestamp = request.getHeader("X-Timestamp");
        builder.append(timestamp).append("\n");
        
        // 添加请求体的哈希值（对于POST/PUT请求）
        if ("POST".equals(request.getMethod()) || "PUT".equals(request.getMethod())) {
            String bodyHash = calculateBodyHash(request);
            builder.append(bodyHash).append("\n");
        }
        
        return builder.toString();
    }
    
    private String calculateSignature(String signatureString) {
        try {
            Mac mac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKey = new SecretKeySpec(
                SECRET_KEY.getBytes(StandardCharsets.UTF_8), 
                "HmacSHA256"
            );
            mac.init(secretKey);
            
            byte[] signature = mac.doFinal(signatureString.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(signature);
        } catch (Exception e) {
            throw new SecurityException("签名计算失败", e);
        }
    }
}
```

### 5.2 接口限流机制

接口限流就像是交通流量控制一样，防止系统因为过多的请求而拥堵或崩溃。

**令牌桶限流实现**

```java
@Component
public class RateLimitingService {
    
    private final Map<String, TokenBucket> userBuckets = new ConcurrentHashMap<>();
    private final Map<String, TokenBucket> ipBuckets = new ConcurrentHashMap<>();
    
    @Value("${api.rate-limit.user.requests-per-minute:60}")
    private int userRequestsPerMinute;
    
    @Value("${api.rate-limit.ip.requests-per-minute:100}")
    private int ipRequestsPerMinute;
    
    public boolean isAllowed(String userId, String clientIp) {
        // 用户级别限流
        TokenBucket userBucket = userBuckets.computeIfAbsent(userId, 
            k -> createTokenBucket(userRequestsPerMinute));
        
        // IP级别限流
        TokenBucket ipBucket = ipBuckets.computeIfAbsent(clientIp,
            k -> createTokenBucket(ipRequestsPerMinute));
        
        return userBucket.tryConsume(1) && ipBucket.tryConsume(1);
    }
    
    private TokenBucket createTokenBucket(int requestsPerMinute) {
        return TokenBucket.builder()
            .capacity(requestsPerMinute)
            .refillTokens(requestsPerMinute)
            .refillPeriod(Duration.ofMinutes(1))
            .build();
    }
}

@RestControllerAdvice
public class RateLimitingInterceptor implements HandlerInterceptor {
    
    @Autowired
    private RateLimitingService rateLimitingService;
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        String userId = getCurrentUserId(request);
        String clientIp = getClientIpAddress(request);
        
        if (!rateLimitingService.isAllowed(userId, clientIp)) {
            response.setStatus(HttpStatus.TOO_MANY_REQUESTS.value());
            response.setContentType(MediaType.APPLICATION_JSON_VALUE);
            
            try {
                String errorResponse = objectMapper.writeValueAsString(
                    ApiResponse.error(429, "请求过于频繁，请稍后再试")
                );
                response.getWriter().write(errorResponse);
            } catch (IOException e) {
                // 记录错误日志
            }
            
            return false;
        }
        
        return true;
    }
}
```

## 6. API文档生成与维护

### 6.1 Swagger/OpenAPI规范

API文档就像是产品的使用说明书一样，需要详细、准确、易于理解。

**Swagger配置**

```java
@Configuration
@EnableSwagger2
public class SwaggerConfig {
    
    @Bean
    public Docket createRestApi() {
        return new Docket(DocumentationType.SWAGGER_2)
            .apiInfo(apiInfo())
            .select()
            .apis(RequestHandlerSelectors.basePackage("com.procurement.platform.controller"))
            .paths(PathSelectors.regex("/api/.*"))
            .build()
            .securitySchemes(securitySchemes())
            .securityContexts(securityContexts());
    }
    
    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
            .title("采购数字化综合管理平台API文档")
            .description("提供完整的采购业务流程API服务")
            .version("1.0.0")
            .contact(new Contact("技术支持", "https://support.procurement.local", "<EMAIL>"))
            .build();
    }
    
    private List<SecurityScheme> securitySchemes() {
        return Arrays.asList(
            new ApiKey("Authorization", "Authorization", "header")
        );
    }
}
```

**API注解示例**

```java
@RestController
@RequestMapping("/api/v1/projects")
@Api(tags = "项目管理", description = "采购项目的全生命周期管理API")
public class ProjectController {
    
    @ApiOperation(
        value = "创建采购项目",
        notes = "创建新的采购项目，支持双人制操作验证",
        response = ProjectVO.class
    )
    @ApiResponses({
        @ApiResponse(code = 201, message = "项目创建成功"),
        @ApiResponse(code = 400, message = "请求参数错误"),
        @ApiResponse(code = 403, message = "权限不足"),
        @ApiResponse(code = 422, message = "业务规则验证失败")
    })
    @PostMapping
    public ResponseEntity<ProjectVO> createProject(
            @ApiParam(value = "项目创建信息", required = true)
            @Valid @RequestBody ProjectCreateDTO projectDTO) {
        
        ProjectVO createdProject = projectService.createProject(projectDTO);
        return ResponseEntity.status(HttpStatus.CREATED).body(createdProject);
    }
    
    @ApiOperation(
        value = "查询项目列表",
        notes = "支持多条件查询和分页，返回当前用户有权限查看的项目列表"
    )
    @GetMapping
    public ResponseEntity<PageResponse<ProjectVO>> getProjects(
            @ApiParam(value = "页码", defaultValue = "1") @RequestParam(defaultValue = "1") Integer page,
            @ApiParam(value = "页面大小", defaultValue = "20") @RequestParam(defaultValue = "20") Integer size,
            @ApiParam(value = "项目名称") @RequestParam(required = false) String projectName,
            @ApiParam(value = "项目类型") @RequestParam(required = false) String projectType,
            @ApiParam(value = "项目状态") @RequestParam(required = false) String projectStatus) {
        
        ProjectQueryDTO queryDTO = ProjectQueryDTO.builder()
            .projectName(projectName)
            .projectType(projectType)
            .projectStatus(projectStatus)
            .build();
            
        Pageable pageable = PageRequest.of(page - 1, size);
        Page<ProjectVO> projects = projectService.queryProjects(queryDTO, pageable);
        
        return ResponseEntity.ok(PageResponse.of(projects));
    }
}
```

### 6.2 API测试示例

API测试示例就像是产品的演示样品一样，让使用者能够快速理解如何正确使用API。

**Postman测试集合示例**

```json
{
  "info": {
    "name": "采购平台API测试集合",
    "description": "完整的API测试用例集合，包含认证、业务操作等"
  },
  "auth": {
    "type": "bearer",
    "bearer": [
      {
        "key": "token",
        "value": "{{access_token}}",
        "type": "string"
      }
    ]
  },
  "event": [
    {
      "listen": "prerequest",
      "script": {
        "exec": [
          "// 自动获取访问令牌",
          "if (!pm.globals.get('access_token') || pm.globals.get('token_expires') < Date.now()) {",
          "    pm.sendRequest({",
          "        url: pm.environment.get('base_url') + '/api/v1/auth/login',",
          "        method: 'POST',",
          "        header: {'Content-Type': 'application/json'},",
          "        body: {",
          "            mode: 'raw',",
          "            raw: JSON.stringify({",
          "                username: pm.environment.get('test_username'),",
          "                password: pm.environment.get('test_password')",
          "            })",
          "        }",
          "    }, (err, res) => {",
          "        if (res.code === 200) {",
          "            const data = res.json().data;",
          "            pm.globals.set('access_token', data.access_token);",
          "            pm.globals.set('refresh_token', data.refresh_token);",
          "            pm.globals.set('token_expires', Date.now() + (data.expires_in * 1000));",
          "        }",
          "    });",
          "}"
        ]
      }
    }
  ],
  "item": [
    {
      "name": "项目管理API测试",
      "item": [
        {
          "name": "创建项目",
          "request": {
            "method": "POST",
            "header": [],
            "body": {
              "mode": "raw",
              "raw": "{\n    \"project_name\": \"办公设备采购项目\",\n    \"project_type\": \"GOODS\",\n    \"budget_amount\": 500000.00,\n    \"procurement_method\": \"PUBLIC_TENDER\",\n    \"organization_form\": \"SELF_ORGANIZED\",\n    \"demand_department\": \"行政科\",\n    \"procurement_department\": \"采购科\",\n    \"is_government_procurement\": true,\n    \"is_classified\": false\n}",
              "options": {
                "raw": {
                  "language": "json"
                }
              }
            },
            "url": {
              "raw": "{{base_url}}/api/v1/projects",
              "host": ["{{base_url}}"],
              "path": ["api", "v1", "projects"]
            }
          },
          "event": [
            {
              "listen": "test",
              "script": {
                "exec": [
                  "pm.test('项目创建成功', function () {",
                  "    pm.response.to.have.status(201);",
                  "    const response = pm.response.json();",
                  "    pm.expect(response.code).to.eql(201);",
                  "    pm.expect(response.data).to.have.property('id');",
                  "    pm.globals.set('created_project_id', response.data.id);",
                  "});",
                  "",
                  "pm.test('返回数据格式正确', function () {",
                  "    const response = pm.response.json();",
                  "    pm.expect(response.data).to.have.property('project_name');",
                  "    pm.expect(response.data).to.have.property('project_status');",
                  "    pm.expect(response.data.project_status).to.eql('CREATED');",
                  "});"
                ]
              }
            }
          ]
        }
      ]
    }
  ]
}
```

## 7. 监控与日志

### 7.1 API监控指标

API监控就像是系统的健康体检一样，需要持续关注各种关键指标的变化。

**关键监控指标**

我们监控的关键指标包括响应时间、请求量、错误率、可用性等。这些指标就像是身体的生命体征一样，能够反映系统的健康状况。

```java
@Component
@Slf4j
public class ApiMetricsCollector {
    
    private final MeterRegistry meterRegistry;
    private final Timer requestTimer;
    private final Counter requestCounter;
    private final Counter errorCounter;
    
    public ApiMetricsCollector(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.requestTimer = Timer.builder("api.request.duration")
            .description("API请求响应时间")
            .register(meterRegistry);
        this.requestCounter = Counter.builder("api.request.total")
            .description("API请求总数")
            .register(meterRegistry);
        this.errorCounter = Counter.builder("api.request.errors")
            .description("API请求错误数")
            .register(meterRegistry);
    }
    
    public void recordRequest(String method, String uri, int statusCode, long duration) {
        // 记录请求总数
        requestCounter.increment(
            Tags.of(
                "method", method,
                "uri", uri,
                "status", String.valueOf(statusCode)
            )
        );
        
        // 记录响应时间
        requestTimer.record(duration, TimeUnit.MILLISECONDS,
            Tags.of("method", method, "uri", uri)
        );
        
        // 记录错误请求
        if (statusCode >= 400) {
            errorCounter.increment(
                Tags.of(
                    "method", method,
                    "uri", uri,
                    "status", String.valueOf(statusCode)
                )
            );
        }
    }
}
```

### 7.2 结构化日志设计

结构化日志就像是精心整理的档案一样，让我们能够快速找到需要的信息。

**API访问日志格式**

```json
{
  "timestamp": "2025-06-26T14:30:00.123Z",
  "level": "INFO",
  "logger": "API_ACCESS",
  "trace_id": "abc123def456",
  "span_id": "def456ghi789",
  "user_id": 1001,
  "username": "zhangsan",
  "client_ip": "*************",
  "user_agent": "Mozilla/5.0...",
  "method": "POST",
  "uri": "/api/v1/projects",
  "query_params": {},
  "request_size": 1024,
  "response_status": 201,
  "response_size": 512,
  "duration_ms": 156,
  "business_context": {
    "operation_type": "PROJECT_CREATE",
    "business_id": 12345,
    "business_result": "SUCCESS"
  }
}
```

通过这样全面而详细的API接口文档，我们不仅为开发团队提供了清晰的接口规范，更建立了一套完整的API管理体系。这个体系就像是现代化的服务大厅，不仅提供了优质的服务，还确保了服务的安全、稳定和高效。每个API接口都经过精心设计，既满足了采购平台的特殊业务需求，又遵循了现代API设计的最佳实践。