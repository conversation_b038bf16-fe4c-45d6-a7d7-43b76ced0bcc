-- =============================================
-- 采购数字化综合管理平台表结构创建脚本
-- =============================================

USE procurement_platform;

-- =============================================
-- 1. 用户与权限管理表
-- =============================================

-- 用户表
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码（加密）',
    real_name VARCHAR(100) NOT NULL COMMENT '真实姓名',
    employee_id VARCHAR(50) UNIQUE COMMENT '工号',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    department VARCHAR(100) COMMENT '部门',
    position VARCHAR(100) COMMENT '职位',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    last_login_time DATETIME COMMENT '最后登录时间',
    last_login_ip VARCHAR(50) COMMENT '最后登录IP',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人ID',
    update_by BIGINT COMMENT '更新人ID',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    version INT DEFAULT 1 COMMENT '版本号（乐观锁）',
    remark TEXT COMMENT '备注',
    INDEX idx_username (username),
    INDEX idx_employee_id (employee_id),
    INDEX idx_department (department),
    INDEX idx_status (status),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 角色表
CREATE TABLE roles (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '角色ID',
    role_code VARCHAR(50) NOT NULL UNIQUE COMMENT '角色编码',
    role_name VARCHAR(100) NOT NULL COMMENT '角色名称',
    description TEXT COMMENT '角色描述',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人ID',
    update_by BIGINT COMMENT '更新人ID',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    version INT DEFAULT 1 COMMENT '版本号（乐观锁）',
    remark TEXT COMMENT '备注',
    INDEX idx_role_code (role_code),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色表';

-- 权限表
CREATE TABLE permissions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '权限ID',
    permission_code VARCHAR(100) NOT NULL UNIQUE COMMENT '权限编码',
    permission_name VARCHAR(100) NOT NULL COMMENT '权限名称',
    permission_type VARCHAR(20) NOT NULL COMMENT '权限类型：MENU-菜单，BUTTON-按钮，API-接口',
    parent_id BIGINT DEFAULT 0 COMMENT '父权限ID',
    path VARCHAR(200) COMMENT '权限路径',
    component VARCHAR(200) COMMENT '组件路径',
    icon VARCHAR(100) COMMENT '图标',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人ID',
    update_by BIGINT COMMENT '更新人ID',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    version INT DEFAULT 1 COMMENT '版本号（乐观锁）',
    remark TEXT COMMENT '备注',
    INDEX idx_permission_code (permission_code),
    INDEX idx_parent_id (parent_id),
    INDEX idx_permission_type (permission_type),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限表';

-- 用户角色关联表
CREATE TABLE user_roles (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '关联ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by BIGINT COMMENT '创建人ID',
    UNIQUE KEY uk_user_role (user_id, role_id),
    INDEX idx_user_id (user_id),
    INDEX idx_role_id (role_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户角色关联表';

-- 角色权限关联表
CREATE TABLE role_permissions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '关联ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    permission_id BIGINT NOT NULL COMMENT '权限ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by BIGINT COMMENT '创建人ID',
    UNIQUE KEY uk_role_permission (role_id, permission_id),
    INDEX idx_role_id (role_id),
    INDEX idx_permission_id (permission_id),
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色权限关联表';

-- =============================================
-- 2. 供应商管理表
-- =============================================

-- 供应商表
CREATE TABLE suppliers (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '供应商ID',
    supplier_code VARCHAR(50) NOT NULL UNIQUE COMMENT '供应商编码',
    supplier_name VARCHAR(200) NOT NULL COMMENT '供应商名称',
    supplier_type VARCHAR(50) COMMENT '供应商类型：ENTERPRISE-企业，INDIVIDUAL-个体',
    legal_person VARCHAR(100) COMMENT '法定代表人',
    contact_person VARCHAR(100) COMMENT '联系人',
    contact_phone VARCHAR(20) COMMENT '联系电话',
    contact_email VARCHAR(100) COMMENT '联系邮箱',
    address TEXT COMMENT '地址',
    business_license VARCHAR(100) COMMENT '营业执照号',
    tax_number VARCHAR(100) COMMENT '税务登记号',
    bank_name VARCHAR(200) COMMENT '开户银行',
    bank_account VARCHAR(100) COMMENT '银行账号',
    business_scope TEXT COMMENT '经营范围',
    qualification_level VARCHAR(50) COMMENT '资质等级',
    credit_rating VARCHAR(20) COMMENT '信用等级',
    cooperation_status VARCHAR(20) DEFAULT 'NORMAL' COMMENT '合作状态：NORMAL-正常，BLACKLIST-黑名单，SUSPENDED-暂停',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人ID',
    update_by BIGINT COMMENT '更新人ID',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    version INT DEFAULT 1 COMMENT '版本号（乐观锁）',
    remark TEXT COMMENT '备注',
    INDEX idx_supplier_code (supplier_code),
    INDEX idx_supplier_name (supplier_name),
    INDEX idx_supplier_type (supplier_type),
    INDEX idx_cooperation_status (cooperation_status),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='供应商表';

-- 供应商资质文件表
CREATE TABLE supplier_qualifications (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '资质ID',
    supplier_id BIGINT NOT NULL COMMENT '供应商ID',
    qualification_type VARCHAR(100) NOT NULL COMMENT '资质类型',
    qualification_name VARCHAR(200) NOT NULL COMMENT '资质名称',
    qualification_number VARCHAR(100) COMMENT '资质证书号',
    issue_date DATE COMMENT '颁发日期',
    expire_date DATE COMMENT '到期日期',
    issuing_authority VARCHAR(200) COMMENT '颁发机构',
    file_path VARCHAR(500) COMMENT '文件路径',
    file_name VARCHAR(200) COMMENT '文件名称',
    status TINYINT DEFAULT 1 COMMENT '状态：0-无效，1-有效',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人ID',
    update_by BIGINT COMMENT '更新人ID',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    version INT DEFAULT 1 COMMENT '版本号（乐观锁）',
    remark TEXT COMMENT '备注',
    INDEX idx_supplier_id (supplier_id),
    INDEX idx_qualification_type (qualification_type),
    INDEX idx_expire_date (expire_date),
    INDEX idx_status (status),
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='供应商资质文件表';

-- =============================================
-- 3. 采购需求管理表
-- =============================================

-- 采购需求表
CREATE TABLE procurement_requirements (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '需求ID',
    requirement_no VARCHAR(50) NOT NULL UNIQUE COMMENT '需求编号',
    requirement_name VARCHAR(200) NOT NULL COMMENT '需求名称',
    requirement_type VARCHAR(50) NOT NULL COMMENT '需求类型：GOODS-货物，SERVICE-服务，ENGINEERING-工程',
    department VARCHAR(100) NOT NULL COMMENT '需求部门',
    applicant_id BIGINT NOT NULL COMMENT '申请人ID',
    applicant_name VARCHAR(100) NOT NULL COMMENT '申请人姓名',
    urgency_level VARCHAR(20) DEFAULT 'NORMAL' COMMENT '紧急程度：LOW-低，NORMAL-普通，HIGH-高，URGENT-紧急',
    expected_amount DECIMAL(15,2) COMMENT '预期金额',
    expected_date DATE COMMENT '期望完成日期',
    usage_purpose TEXT NOT NULL COMMENT '用途说明',
    technical_requirements TEXT COMMENT '技术要求',
    quality_standards TEXT COMMENT '质量标准',
    delivery_requirements TEXT COMMENT '交付要求',
    is_classified TINYINT DEFAULT 0 COMMENT '是否涉密：0-否，1-是',
    classified_level VARCHAR(20) COMMENT '涉密级别：GENERAL-一般敏感，BUSINESS-商业秘密，WORK-工作秘密，STATE-国家秘密',
    approval_status VARCHAR(20) DEFAULT 'PENDING' COMMENT '审批状态：PENDING-待审批，APPROVED-已通过，REJECTED-已拒绝',
    approver_id BIGINT COMMENT '审批人ID',
    approver_name VARCHAR(100) COMMENT '审批人姓名',
    approval_time DATETIME COMMENT '审批时间',
    approval_opinion TEXT COMMENT '审批意见',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人ID',
    update_by BIGINT COMMENT '更新人ID',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    version INT DEFAULT 1 COMMENT '版本号（乐观锁）',
    remark TEXT COMMENT '备注',
    INDEX idx_requirement_no (requirement_no),
    INDEX idx_requirement_type (requirement_type),
    INDEX idx_department (department),
    INDEX idx_applicant_id (applicant_id),
    INDEX idx_urgency_level (urgency_level),
    INDEX idx_is_classified (is_classified),
    INDEX idx_approval_status (approval_status),
    INDEX idx_create_time (create_time),
    FOREIGN KEY (applicant_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='采购需求表';

-- 需求明细表
CREATE TABLE requirement_items (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '明细ID',
    requirement_id BIGINT NOT NULL COMMENT '需求ID',
    item_name VARCHAR(200) NOT NULL COMMENT '物品名称',
    item_specification TEXT COMMENT '规格型号',
    item_brand VARCHAR(100) COMMENT '品牌要求',
    quantity DECIMAL(10,2) NOT NULL COMMENT '数量',
    unit VARCHAR(20) NOT NULL COMMENT '单位',
    estimated_price DECIMAL(10,2) COMMENT '预估单价',
    estimated_total DECIMAL(15,2) COMMENT '预估总价',
    technical_params TEXT COMMENT '技术参数',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人ID',
    update_by BIGINT COMMENT '更新人ID',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    version INT DEFAULT 1 COMMENT '版本号（乐观锁）',
    INDEX idx_requirement_id (requirement_id),
    INDEX idx_item_name (item_name),
    FOREIGN KEY (requirement_id) REFERENCES procurement_requirements(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='需求明细表';

-- =============================================
-- 4. 采购项目管理表
-- =============================================

-- 采购项目表
CREATE TABLE procurement_projects (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '项目ID',
    project_no VARCHAR(50) NOT NULL UNIQUE COMMENT '项目编号',
    project_name VARCHAR(200) NOT NULL COMMENT '项目名称',
    requirement_id BIGINT NOT NULL COMMENT '关联需求ID',
    project_type VARCHAR(50) NOT NULL COMMENT '项目类型：GOODS-货物，SERVICE-服务，ENGINEERING-工程',
    procurement_method VARCHAR(50) NOT NULL COMMENT '采购方式：PUBLIC_TENDER-公开招标，INVITE_TENDER-邀请招标，COMPETITIVE_NEGOTIATION-竞争性磋商，SINGLE_SOURCE-单一来源',
    procurement_organization VARCHAR(50) COMMENT '采购组织形式：SELF-自行采购，AGENCY-委托代理',
    is_government_procurement TINYINT DEFAULT 0 COMMENT '是否政府采购：0-否，1-是',
    budget_amount DECIMAL(15,2) NOT NULL COMMENT '预算金额',
    is_classified TINYINT DEFAULT 0 COMMENT '是否涉密：0-否，1-是',
    classified_level VARCHAR(20) COMMENT '涉密级别',
    primary_purchaser_id BIGINT NOT NULL COMMENT '主采购员ID',
    primary_purchaser_name VARCHAR(100) NOT NULL COMMENT '主采购员姓名',
    secondary_purchaser_id BIGINT NOT NULL COMMENT '副采购员ID',
    secondary_purchaser_name VARCHAR(100) NOT NULL COMMENT '副采购员姓名',
    supervisor_id BIGINT COMMENT '履约监督员ID',
    supervisor_name VARCHAR(100) COMMENT '履约监督员姓名',
    project_status VARCHAR(20) DEFAULT 'CREATED' COMMENT '项目状态：CREATED-已创建，QUOTING-报价中，QUOTED-已报价，CONTRACTED-已签约，COMPLETED-已完成，CANCELLED-已取消',
    start_date DATE COMMENT '开始日期',
    end_date DATE COMMENT '结束日期',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人ID',
    update_by BIGINT COMMENT '更新人ID',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    version INT DEFAULT 1 COMMENT '版本号（乐观锁）',
    remark TEXT COMMENT '备注',
    INDEX idx_project_no (project_no),
    INDEX idx_requirement_id (requirement_id),
    INDEX idx_project_type (project_type),
    INDEX idx_procurement_method (procurement_method),
    INDEX idx_is_classified (is_classified),
    INDEX idx_primary_purchaser_id (primary_purchaser_id),
    INDEX idx_secondary_purchaser_id (secondary_purchaser_id),
    INDEX idx_project_status (project_status),
    INDEX idx_create_time (create_time),
    FOREIGN KEY (requirement_id) REFERENCES procurement_requirements(id),
    FOREIGN KEY (primary_purchaser_id) REFERENCES users(id),
    FOREIGN KEY (secondary_purchaser_id) REFERENCES users(id),
    FOREIGN KEY (supervisor_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='采购项目表';

SELECT '采购需求和项目管理表创建完成！' as '状态';
