<template>
  <div class="project-management">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>项目管理</span>
          <el-button type="primary">
            <el-icon><Plus /></el-icon>
            创建项目
          </el-button>
        </div>
      </template>
      
      <el-table :data="projectList" style="width: 100%">
        <el-table-column prop="projectNo" label="项目编号" />
        <el-table-column prop="projectName" label="项目名称" />
        <el-table-column prop="projectType" label="项目类型" />
        <el-table-column prop="procurementMethod" label="采购方式" />
        <el-table-column prop="budgetAmount" label="预算金额" width="120">
          <template #default="{ row }">
            ¥{{ row.budgetAmount?.toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column prop="projectStatus" label="项目状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.projectStatus)">
              {{ getStatusText(row.projectStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button type="primary" size="small">查看</el-button>
            <el-button type="success" size="small">管理</el-button>
            <el-button type="warning" size="small">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const projectList = ref([
  {
    id: 1,
    projectNo: 'PROJ202506290001',
    projectName: '办公用品采购项目',
    projectType: 'GOODS',
    procurementMethod: 'COMPETITIVE_NEGOTIATION',
    budgetAmount: 50000,
    projectStatus: 'CREATED',
    createTime: '2025-06-29 11:00:00'
  },
  {
    id: 2,
    projectNo: 'PROJ202506290002',
    projectName: '计算机设备采购项目',
    projectType: 'GOODS',
    procurementMethod: 'PUBLIC_TENDER',
    budgetAmount: 200000,
    projectStatus: 'QUOTING',
    createTime: '2025-06-29 12:00:00'
  }
])

const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    'CREATED': 'info',
    'QUOTING': 'warning',
    'QUOTED': 'primary',
    'CONTRACTED': 'success',
    'COMPLETED': 'success',
    'CANCELLED': 'danger'
  }
  return colorMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'CREATED': '已创建',
    'QUOTING': '报价中',
    'QUOTED': '已报价',
    'CONTRACTED': '已签约',
    'COMPLETED': '已完成',
    'CANCELLED': '已取消'
  }
  return textMap[status] || status
}
</script>

<style lang="scss" scoped>
.project-management {
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}
</style>
