package com.procurement.controller;

import com.procurement.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 认证控制器
 * 
 * <AUTHOR> Platform Team
 * @version 1.0.0
 * @since 2025-06-29
 */
@Slf4j
@RestController
@RequestMapping("/api/auth")
@CrossOrigin(origins = "http://localhost:3000")
public class AuthController {

    /**
     * 用户登录
     */
    @PostMapping("/login")
    public Result<Map<String, Object>> login(@RequestBody Map<String, String> loginRequest) {
        String username = loginRequest.get("username");
        String password = loginRequest.get("password");
        
        log.info("用户登录请求: username={}", username);
        
        // 简单的用户验证（实际项目中应该查询数据库）
        if ("admin".equals(username) && "admin123".equals(password)) {
            Map<String, Object> data = new HashMap<>();
            data.put("token", "mock-jwt-token-" + System.currentTimeMillis());
            
            // 用户信息
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("id", 1);
            userInfo.put("username", "admin");
            userInfo.put("realName", "系统管理员");
            userInfo.put("employeeId", "ADMIN001");
            userInfo.put("email", "<EMAIL>");
            userInfo.put("phone", "13800000000");
            userInfo.put("department", "信息中心");
            userInfo.put("position", "系统管理员");
            userInfo.put("roles", new String[]{"ADMIN"});
            userInfo.put("lastLoginTime", LocalDateTime.now());
            
            data.put("userInfo", userInfo);
            data.put("permissions", new String[]{"*"});
            
            return Result.success("登录成功", data);
        } else if ("procurement01".equals(username) && "admin123".equals(password)) {
            Map<String, Object> data = new HashMap<>();
            data.put("token", "mock-jwt-token-" + System.currentTimeMillis());
            
            // 用户信息
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("id", 2);
            userInfo.put("username", "procurement01");
            userInfo.put("realName", "张采购");
            userInfo.put("employeeId", "PROC001");
            userInfo.put("email", "<EMAIL>");
            userInfo.put("phone", "13800000001");
            userInfo.put("department", "后勤服务中心");
            userInfo.put("position", "采购员");
            userInfo.put("roles", new String[]{"PROCUREMENT_STAFF"});
            userInfo.put("lastLoginTime", LocalDateTime.now());
            
            data.put("userInfo", userInfo);
            data.put("permissions", new String[]{"PROCUREMENT:*"});
            
            return Result.success("登录成功", data);
        } else {
            return Result.error(401, "用户名或密码错误");
        }
    }

    /**
     * 获取用户信息
     */
    @GetMapping("/userinfo")
    public Result<Map<String, Object>> getUserInfo(@RequestHeader(value = "Authorization", required = false) String token) {
        log.info("获取用户信息请求: token={}", token);
        
        // 模拟根据token获取用户信息
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("id", 1);
        userInfo.put("username", "admin");
        userInfo.put("realName", "系统管理员");
        userInfo.put("employeeId", "ADMIN001");
        userInfo.put("email", "<EMAIL>");
        userInfo.put("phone", "13800000000");
        userInfo.put("department", "信息中心");
        userInfo.put("position", "系统管理员");
        userInfo.put("roles", new String[]{"ADMIN"});
        userInfo.put("lastLoginTime", LocalDateTime.now());
        
        Map<String, Object> data = new HashMap<>();
        data.put("userInfo", userInfo);
        data.put("permissions", new String[]{"*"});
        
        return Result.success("获取用户信息成功", data);
    }

    /**
     * 用户登出
     */
    @PostMapping("/logout")
    public Result<String> logout(@RequestHeader(value = "Authorization", required = false) String token) {
        log.info("用户登出请求: token={}", token);
        return Result.success("登出成功");
    }
}
