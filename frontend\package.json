{"name": "procurement-platform-frontend", "version": "1.0.0", "description": "采购数字化综合管理平台前端", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "element-plus": "^2.4.4", "axios": "^1.6.2", "@element-plus/icons-vue": "^2.3.1", "dayjs": "^1.11.10", "js-cookie": "^3.0.5", "nprogress": "^0.2.0", "echarts": "^5.4.3", "vue-echarts": "^6.6.1"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.2", "vite": "^5.0.10", "vue-tsc": "^1.8.25", "typescript": "^5.3.3", "@types/node": "^20.10.6", "@types/js-cookie": "^3.0.6", "@types/nprogress": "^0.2.3", "eslint": "^8.56.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/eslint-config-prettier": "^9.0.0", "prettier": "^3.1.1", "sass": "^1.69.7", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.26.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}