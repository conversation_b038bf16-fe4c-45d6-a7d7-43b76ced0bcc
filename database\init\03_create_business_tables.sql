-- =============================================
-- 采购数字化综合管理平台业务表创建脚本（续）
-- =============================================

USE procurement_platform;

-- =============================================
-- 5. 供应商报价管理表
-- =============================================

-- 供应商报价表
CREATE TABLE supplier_quotes (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '报价ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    supplier_id BIGINT NOT NULL COMMENT '供应商ID',
    quote_no VARCHAR(50) NOT NULL COMMENT '报价编号',
    quote_amount DECIMAL(15,2) NOT NULL COMMENT '报价总金额',
    quote_date DATE NOT NULL COMMENT '报价日期',
    delivery_period INT COMMENT '交付周期（天）',
    payment_terms TEXT COMMENT '付款条件',
    warranty_period INT COMMENT '质保期（月）',
    technical_proposal TEXT COMMENT '技术方案',
    service_commitment TEXT COMMENT '服务承诺',
    quote_validity_days INT DEFAULT 30 COMMENT '报价有效期（天）',
    is_selected TINYINT DEFAULT 0 COMMENT '是否中标：0-否，1-是',
    selection_reason TEXT COMMENT '中标/落标原因',
    quote_status VARCHAR(20) DEFAULT 'SUBMITTED' COMMENT '报价状态：SUBMITTED-已提交，EVALUATED-已评估，SELECTED-已中标，REJECTED-已落标',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人ID',
    update_by BIGINT COMMENT '更新人ID',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    version INT DEFAULT 1 COMMENT '版本号（乐观锁）',
    remark TEXT COMMENT '备注',
    INDEX idx_project_id (project_id),
    INDEX idx_supplier_id (supplier_id),
    INDEX idx_quote_no (quote_no),
    INDEX idx_is_selected (is_selected),
    INDEX idx_quote_status (quote_status),
    INDEX idx_quote_date (quote_date),
    FOREIGN KEY (project_id) REFERENCES procurement_projects(id) ON DELETE CASCADE,
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='供应商报价表';

-- 报价明细表
CREATE TABLE quote_items (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '报价明细ID',
    quote_id BIGINT NOT NULL COMMENT '报价ID',
    item_name VARCHAR(200) NOT NULL COMMENT '物品名称',
    item_specification TEXT COMMENT '规格型号',
    item_brand VARCHAR(100) COMMENT '品牌',
    quantity DECIMAL(10,2) NOT NULL COMMENT '数量',
    unit VARCHAR(20) NOT NULL COMMENT '单位',
    unit_price DECIMAL(10,2) NOT NULL COMMENT '单价',
    total_price DECIMAL(15,2) NOT NULL COMMENT '总价',
    technical_params TEXT COMMENT '技术参数',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人ID',
    update_by BIGINT COMMENT '更新人ID',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    version INT DEFAULT 1 COMMENT '版本号（乐观锁）',
    INDEX idx_quote_id (quote_id),
    INDEX idx_item_name (item_name),
    FOREIGN KEY (quote_id) REFERENCES supplier_quotes(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='报价明细表';

-- =============================================
-- 6. 合同管理表
-- =============================================

-- 合同表
CREATE TABLE contracts (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '合同ID',
    contract_no VARCHAR(50) NOT NULL UNIQUE COMMENT '合同编号',
    contract_name VARCHAR(200) NOT NULL COMMENT '合同名称',
    project_id BIGINT NOT NULL COMMENT '关联项目ID',
    supplier_id BIGINT NOT NULL COMMENT '供应商ID',
    quote_id BIGINT COMMENT '关联报价ID',
    contract_type VARCHAR(50) NOT NULL COMMENT '合同类型：GOODS-货物，SERVICE-服务，ENGINEERING-工程',
    contract_amount DECIMAL(15,2) NOT NULL COMMENT '合同金额',
    currency VARCHAR(10) DEFAULT 'CNY' COMMENT '币种',
    signing_date DATE NOT NULL COMMENT '签订日期',
    effective_date DATE NOT NULL COMMENT '生效日期',
    delivery_date DATE COMMENT '交付日期',
    completion_date DATE COMMENT '完成日期',
    warranty_period INT COMMENT '质保期（月）',
    payment_method VARCHAR(50) COMMENT '付款方式：LUMP_SUM-一次性付款，INSTALLMENT-分期付款',
    contract_terms TEXT COMMENT '合同条款',
    delivery_terms TEXT COMMENT '交付条款',
    quality_standards TEXT COMMENT '质量标准',
    breach_liability TEXT COMMENT '违约责任',
    dispute_resolution TEXT COMMENT '争议解决',
    contract_status VARCHAR(20) DEFAULT 'DRAFT' COMMENT '合同状态：DRAFT-草稿，SIGNED-已签订，PERFORMING-履行中，COMPLETED-已完成，TERMINATED-已终止',
    performance_status VARCHAR(20) DEFAULT 'NOT_STARTED' COMMENT '履约状态：NOT_STARTED-未开始，IN_PROGRESS-进行中，COMPLETED-已完成，OVERDUE-已逾期',
    supervisor_id BIGINT COMMENT '履约监督员ID',
    supervisor_name VARCHAR(100) COMMENT '履约监督员姓名',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人ID',
    update_by BIGINT COMMENT '更新人ID',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    version INT DEFAULT 1 COMMENT '版本号（乐观锁）',
    remark TEXT COMMENT '备注',
    INDEX idx_contract_no (contract_no),
    INDEX idx_project_id (project_id),
    INDEX idx_supplier_id (supplier_id),
    INDEX idx_contract_type (contract_type),
    INDEX idx_signing_date (signing_date),
    INDEX idx_contract_status (contract_status),
    INDEX idx_performance_status (performance_status),
    INDEX idx_supervisor_id (supervisor_id),
    FOREIGN KEY (project_id) REFERENCES procurement_projects(id),
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
    FOREIGN KEY (quote_id) REFERENCES supplier_quotes(id),
    FOREIGN KEY (supervisor_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='合同表';

-- 合同履约记录表
CREATE TABLE contract_performance_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '履约记录ID',
    contract_id BIGINT NOT NULL COMMENT '合同ID',
    record_date DATE NOT NULL COMMENT '记录日期',
    performance_content TEXT NOT NULL COMMENT '履约内容',
    performance_progress DECIMAL(5,2) COMMENT '履约进度（百分比）',
    quality_status VARCHAR(20) COMMENT '质量状况：GOOD-良好，NORMAL-一般，POOR-较差',
    schedule_status VARCHAR(20) COMMENT '进度状况：ON_TIME-按时，DELAYED-延期，AHEAD-提前',
    issues_found TEXT COMMENT '发现问题',
    corrective_actions TEXT COMMENT '纠正措施',
    supervisor_opinion TEXT COMMENT '监督意见',
    record_by BIGINT NOT NULL COMMENT '记录人ID',
    record_by_name VARCHAR(100) NOT NULL COMMENT '记录人姓名',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人ID',
    update_by BIGINT COMMENT '更新人ID',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    version INT DEFAULT 1 COMMENT '版本号（乐观锁）',
    INDEX idx_contract_id (contract_id),
    INDEX idx_record_date (record_date),
    INDEX idx_record_by (record_by),
    FOREIGN KEY (contract_id) REFERENCES contracts(id) ON DELETE CASCADE,
    FOREIGN KEY (record_by) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='合同履约记录表';

-- =============================================
-- 7. 验收管理表
-- =============================================

-- 验收记录表
CREATE TABLE acceptance_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '验收记录ID',
    acceptance_no VARCHAR(50) NOT NULL UNIQUE COMMENT '验收编号',
    contract_id BIGINT NOT NULL COMMENT '合同ID',
    acceptance_type VARCHAR(20) NOT NULL COMMENT '验收类型：PRELIMINARY-初验，FINAL-终验，PARTIAL-部分验收',
    acceptance_date DATE NOT NULL COMMENT '验收日期',
    acceptance_location VARCHAR(200) COMMENT '验收地点',
    acceptance_result VARCHAR(20) NOT NULL COMMENT '验收结果：PASSED-通过，FAILED-不通过，CONDITIONAL-有条件通过',
    acceptance_opinion TEXT COMMENT '验收意见',
    quality_evaluation TEXT COMMENT '质量评价',
    quantity_check_result TEXT COMMENT '数量核查结果',
    technical_check_result TEXT COMMENT '技术检查结果',
    issues_found TEXT COMMENT '发现问题',
    rectification_requirements TEXT COMMENT '整改要求',
    acceptance_status VARCHAR(20) DEFAULT 'IN_PROGRESS' COMMENT '验收状态：IN_PROGRESS-进行中，COMPLETED-已完成，SUSPENDED-已暂停',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人ID',
    update_by BIGINT COMMENT '更新人ID',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    version INT DEFAULT 1 COMMENT '版本号（乐观锁）',
    remark TEXT COMMENT '备注',
    INDEX idx_acceptance_no (acceptance_no),
    INDEX idx_contract_id (contract_id),
    INDEX idx_acceptance_date (acceptance_date),
    INDEX idx_acceptance_result (acceptance_result),
    INDEX idx_acceptance_status (acceptance_status),
    FOREIGN KEY (contract_id) REFERENCES contracts(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='验收记录表';

-- 验收人员表（多人验收制）
CREATE TABLE acceptance_participants (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '参与人ID',
    acceptance_id BIGINT NOT NULL COMMENT '验收记录ID',
    participant_id BIGINT NOT NULL COMMENT '参与人用户ID',
    participant_name VARCHAR(100) NOT NULL COMMENT '参与人姓名',
    participant_role VARCHAR(50) NOT NULL COMMENT '参与角色：ORGANIZER-组织者，INSPECTOR-检查员，WITNESS-见证人',
    department VARCHAR(100) COMMENT '所属部门',
    signature_status TINYINT DEFAULT 0 COMMENT '签字状态：0-未签字，1-已签字',
    signature_time DATETIME COMMENT '签字时间',
    signature_opinion TEXT COMMENT '签字意见',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_acceptance_id (acceptance_id),
    INDEX idx_participant_id (participant_id),
    INDEX idx_signature_status (signature_status),
    FOREIGN KEY (acceptance_id) REFERENCES acceptance_records(id) ON DELETE CASCADE,
    FOREIGN KEY (participant_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='验收人员表';

SELECT '合同管理和验收管理表创建完成！' as '状态';
