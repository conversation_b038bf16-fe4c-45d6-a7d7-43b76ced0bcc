<template>
  <div class="role-management">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>角色管理</span>
          <el-button type="primary">
            <el-icon><Plus /></el-icon>
            新增角色
          </el-button>
        </div>
      </template>
      
      <el-table :data="roleList" style="width: 100%">
        <el-table-column prop="roleCode" label="角色编码" />
        <el-table-column prop="roleName" label="角色名称" />
        <el-table-column prop="description" label="描述" />
        <el-table-column prop="status" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button type="primary" size="small">编辑</el-button>
            <el-button type="warning" size="small">权限</el-button>
            <el-button type="danger" size="small">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import type { Role } from '@/types/user'

const roleList = ref<Role[]>([
  {
    id: 1,
    roleCode: 'ADMIN',
    roleName: '系统管理员',
    description: '系统管理员，拥有所有权限',
    status: 1,
    createTime: '2025-06-29 10:00:00'
  },
  {
    id: 2,
    roleCode: 'PROCUREMENT_STAFF',
    roleName: '采购员',
    description: '采购实施人员，执行具体采购工作',
    status: 1,
    createTime: '2025-06-29 10:00:00'
  }
])

onMounted(() => {
  // 加载角色列表
})
</script>

<style lang="scss" scoped>
.role-management {
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}
</style>
