@echo off
chcp 65001 >nul
echo ================================
echo Java环境检查脚本
echo 采购数字化综合管理平台
echo ================================

echo.
echo [1] 检查Java版本...
java -version 2>nul
if %errorlevel% neq 0 (
    echo ❌ Java未正确安装或未添加到PATH
    echo.
    echo 请按照以下步骤安装Java：
    echo 1. 下载OpenJDK 21 (LTS): https://adoptium.net/
    echo 2. 选择Windows x64 .msi安装包
    echo 3. 安装到默认路径
    echo 4. 配置环境变量JAVA_HOME
    echo 5. 将%%JAVA_HOME%%\bin添加到PATH
    echo.
    goto :end
) else (
    echo ✅ Java安装正常
)

echo.
echo [2] 检查Java编译器...
javac -version 2>nul
if %errorlevel% neq 0 (
    echo ❌ Java编译器未找到
    echo 请确保安装的是JDK而不是JRE
) else (
    echo ✅ Java编译器正常
)

echo.
echo [3] 检查JAVA_HOME环境变量...
if defined JAVA_HOME (
    echo ✅ JAVA_HOME已设置: %JAVA_HOME%
    if exist "%JAVA_HOME%\bin\java.exe" (
        echo ✅ JAVA_HOME路径正确
    ) else (
        echo ❌ JAVA_HOME路径不正确，请检查路径设置
    )
) else (
    echo ❌ JAVA_HOME环境变量未设置
    echo 请设置JAVA_HOME环境变量指向JDK安装目录
)

echo.
echo [4] 检查Java版本详情...
java -version 2>&1 | findstr "version" >nul
if %errorlevel% equ 0 (
    echo Java版本信息：
    java -version
    echo.
    echo 检查版本是否为11或更高版本...
    for /f "tokens=3" %%i in ('java -version 2^>^&1 ^| findstr "version"') do (
        set JAVA_VERSION=%%i
    )
    echo 当前Java版本: %JAVA_VERSION%
)

echo.
echo [5] 检查Maven兼容性...
echo 检查Java版本是否支持Maven...
java -version 2>&1 | findstr "21\|17\|11" >nul
if %errorlevel% equ 0 (
    echo ✅ Java版本支持Maven构建
    java -version 2>&1 | findstr "21" >nul
    if %errorlevel% equ 0 (
        echo ✅ 推荐：使用最新的Java 21 LTS版本
    )
) else (
    echo ⚠️  建议使用Java 21、17或11 LTS版本
    echo ⚠️  推荐使用Java 21 (最新LTS版本)
)

echo.
echo ================================
echo Java环境检查完成
echo ================================

:end
echo.
echo 如果Java安装有问题，请参考以下解决方案：
echo.
echo 问题1：java命令不被识别
echo 解决：检查JAVA_HOME和PATH环境变量设置
echo.
echo 问题2：版本不匹配  
echo 解决：确保安装的是JDK 11或更高版本
echo.
echo 问题3：javac命令不存在
echo 解决：确保安装的是JDK而不是JRE
echo.
echo 安装完成后请重新打开命令提示符窗口
echo.
pause
