@echo off
echo ================================
echo Java Environment Check
echo Procurement Platform
echo ================================
echo.

echo [1] Checking Java version...
java -version
if errorlevel 1 (
    echo.
    echo ERROR: Java is not installed or not in PATH
    echo.
    echo Please install Java 21:
    echo 1. Visit https://adoptium.net/
    echo 2. Download OpenJDK 21 LTS Windows x64 .msi
    echo 3. Run installer
    echo 4. Restart command prompt
    echo.
    goto end
) else (
    echo SUCCESS: Java is installed
)

echo.
echo [2] Checking Java compiler...
javac -version
if errorlevel 1 (
    echo ERROR: Java compiler not found, install JDK not JRE
) else (
    echo SUCCESS: Java compiler is available
)

echo.
echo [3] Checking JAVA_HOME...
if "%JAVA_HOME%"=="" (
    echo WARNING: JAVA_HOME is not set
) else (
    echo SUCCESS: JAVA_HOME = %JAVA_HOME%
)

echo.
echo [4] Checking version compatibility...
java -version 2>&1 | find "21." >nul
if not errorlevel 1 (
    echo SUCCESS: Java 21 - Best version
    goto compatible
)

java -version 2>&1 | find "17." >nul
if not errorlevel 1 (
    echo SUCCESS: Java 17 - Compatible version
    goto compatible
)

java -version 2>&1 | find "11." >nul
if not errorlevel 1 (
    echo SUCCESS: Java 11 - Minimum version
    goto compatible
)

echo WARNING: Recommend Java 21/17/11 LTS versions
goto end

:compatible
echo SUCCESS: Version is compatible with Maven and Spring Boot

:end
echo.
echo ================================
echo Check Complete
echo ================================
echo.
echo If there are issues:
echo 1. Install Java 21 LTS
echo 2. Restart command prompt
echo 3. Run this script again
echo.
pause
