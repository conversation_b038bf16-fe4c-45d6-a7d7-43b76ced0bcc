# Java 21 (LTS) 安装指南
## Windows 10+ 环境下安装OpenJDK 21

**推荐版本**：OpenJDK 21 (LTS) - 最新长期支持版本  
**适用系统**：Windows 10+ (64位)  
**更新日期**：2025年6月29日

---

## 🎯 为什么选择Java 21？

### Java版本对比
| 版本 | 发布时间 | LTS | 支持到期 | 推荐度 |
|------|----------|-----|----------|--------|
| Java 8 | 2014年3月 | ✅ | 2030年12月 | ⚠️ 过时 |
| Java 11 | 2018年9月 | ✅ | 2026年9月 | ✅ 稳定 |
| Java 17 | 2021年9月 | ✅ | 2029年9月 | ✅ 推荐 |
| **Java 21** | **2023年9月** | **✅** | **2031年9月** | **🌟 最佳** |

### Java 21 的优势
- 🚀 **性能提升**：相比Java 11性能提升15-20%
- 🔧 **新特性**：虚拟线程、模式匹配、记录类等
- 🛡️ **安全性**：最新的安全补丁和漏洞修复
- 📈 **长期支持**：支持到2031年，投资回报更高
- 🔄 **向后兼容**：完全兼容Java 11/17代码

---

## 📥 下载与安装

### Step 1: 下载OpenJDK 21

**官方下载地址**：https://adoptium.net/

**下载步骤**：
1. 访问 https://adoptium.net/
2. 选择 **"Temurin 21 (LTS)"**
3. 操作系统选择：**Windows**
4. 架构选择：**x64**
5. 包类型选择：**JDK** (不是JRE)
6. 格式选择：**.msi** (推荐，自动安装)
7. 点击 **"Download"** 下载

**文件信息**：
- 文件名：`OpenJDK21U-jdk_x64_windows_hotspot_21.0.x_x.msi`
- 大小：约 180MB
- 安装后大小：约 300MB

### Step 2: 安装OpenJDK 21

**安装步骤**：
1. **运行安装程序**
   - 双击下载的 `.msi` 文件
   - 如果出现安全提示，点击"运行"

2. **安装向导**
   - 欢迎界面：点击 **"Next"**
   - 许可协议：勾选 **"I accept"**，点击 **"Next"**
   - 安装路径：建议使用默认路径
     ```
     C:\Program Files\Eclipse Adoptium\jdk-21.0.x.x-hotspot\
     ```
   - 功能选择：保持默认选择，确保勾选：
     - ✅ **"Add to PATH"** (自动添加到环境变量)
     - ✅ **"Associate .jar files"** (关联jar文件)
     - ✅ **"Set JAVA_HOME"** (自动设置JAVA_HOME)

3. **完成安装**
   - 点击 **"Install"** 开始安装
   - 等待安装完成（约2-3分钟）
   - 点击 **"Finish"** 完成安装

### Step 3: 验证安装

**打开命令提示符**：
- 按 `Win + R`，输入 `cmd`，按回车
- 或者在开始菜单搜索"命令提示符"

**验证命令**：
```bash
# 检查Java版本
java -version

# 检查Java编译器
javac -version

# 检查JAVA_HOME环境变量
echo %JAVA_HOME%
```

**预期输出**：
```
C:\Users\<USER>\Users\YourName>javac -version
javac 21.0.1

C:\Users\<USER>\Program Files\Eclipse Adoptium\jdk-*********-hotspot
```

---

## ⚙️ 手动配置环境变量（如果自动配置失败）

### 配置JAVA_HOME

1. **打开环境变量设置**
   - 右键 **"此电脑"** → **"属性"**
   - 点击 **"高级系统设置"**
   - 点击 **"环境变量"**

2. **新建JAVA_HOME变量**
   - 在 **"系统变量"** 区域点击 **"新建"**
   - 变量名：`JAVA_HOME`
   - 变量值：`C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot`
     (请根据实际安装路径调整版本号)

3. **编辑PATH变量**
   - 在 **"系统变量"** 中找到 **"Path"** 变量
   - 点击 **"编辑"**
   - 点击 **"新建"**
   - 添加：`%JAVA_HOME%\bin`
   - 点击 **"确定"** 保存所有设置

4. **重启命令提示符**
   - 关闭所有命令提示符窗口
   - 重新打开命令提示符
   - 重新运行验证命令

---

## 🔧 使用验证脚本

运行我们提供的Java检查脚本：

```bash
# 在项目根目录运行
check_java.bat
```

这个脚本会自动检查：
- ✅ Java版本是否正确
- ✅ Java编译器是否可用
- ✅ JAVA_HOME环境变量是否设置
- ✅ Maven兼容性检查

---

## 🚀 测试Spring Boot项目

安装完成后，测试采购管理平台项目：

```bash
# 进入后端目录
cd backend

# 编译项目（首次运行会下载依赖）
mvn clean compile

# 启动项目
mvn spring-boot:run
```

**预期结果**：
```
=================================
采购数字化综合管理平台启动成功！
访问地址：http://localhost:8080
API文档：http://localhost:8080/swagger-ui.html
=================================
```

---

## ❓ 常见问题解决

### Q1: java命令不被识别
**错误信息**：`'java' 不是内部或外部命令`

**解决方案**：
1. 检查JAVA_HOME环境变量是否设置正确
2. 检查PATH环境变量是否包含 `%JAVA_HOME%\bin`
3. 重启命令提示符窗口
4. 如果仍然不行，重启计算机

### Q2: 版本显示不正确
**问题**：显示的不是Java 21版本

**解决方案**：
1. 检查是否有多个Java版本安装
2. 确保JAVA_HOME指向Java 21安装目录
3. 在PATH中，Java 21的路径应该在其他Java版本之前

### Q3: Maven编译失败
**错误信息**：`Unsupported major.minor version`

**解决方案**：
1. 确保Maven使用的是Java 21
2. 运行 `mvn -version` 检查Maven使用的Java版本
3. 如果不正确，重新配置JAVA_HOME

### Q4: IntelliJ IDEA不识别Java 21
**解决方案**：
1. 打开 **File** → **Project Structure**
2. 在 **Project** 标签页设置：
   - Project SDK: 选择Java 21
   - Project language level: 21
3. 在 **Modules** 标签页设置：
   - Language level: 21

---

## 🎯 下一步操作

Java 21安装完成后，您可以：

1. ✅ **启动后端项目**：`mvn spring-boot:run`
2. ✅ **安装其他开发工具**：Node.js、MySQL、Redis
3. ✅ **开始业务功能开发**：用户认证、采购管理等模块
4. ✅ **配置IDE**：IntelliJ IDEA项目设置

---

## 📞 技术支持

如果在安装过程中遇到问题：

1. **查看错误日志**：记录具体的错误信息
2. **检查系统要求**：确保Windows 10+ 64位系统
3. **重新下载**：确保下载的是完整的安装包
4. **联系支持**：提供详细的错误信息和系统环境

**推荐资源**：
- OpenJDK官网：https://openjdk.org/
- Adoptium官网：https://adoptium.net/
- Java 21新特性：https://openjdk.org/projects/jdk/21/

---

**安装完成标志**：
- ✅ `java -version` 显示 21.x.x 版本
- ✅ `javac -version` 显示 21.x.x 版本  
- ✅ `echo %JAVA_HOME%` 显示正确路径
- ✅ Spring Boot项目能够正常启动
