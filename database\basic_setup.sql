-- =============================================
-- 采购数字化综合管理平台基础数据库初始化
-- 兼容MySQL 9.3，简化版本
-- =============================================

-- 创建数据库
DROP DATABASE IF EXISTS procurement_platform;
CREATE DATABASE procurement_platform 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE procurement_platform;

-- =============================================
-- 用户表
-- =============================================
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    real_name VARCHAR(100) NOT NULL,
    employee_id VARCHAR(50) UNIQUE,
    email VARCHAR(100),
    phone VARCHAR(20),
    department VARCHAR(100),
    position VARCHAR(100),
    status TINYINT DEFAULT 1,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    remark TEXT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 角色表
-- =============================================
CREATE TABLE roles (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    role_code VARCHAR(50) NOT NULL UNIQUE,
    role_name VARCHAR(100) NOT NULL,
    description TEXT,
    status TINYINT DEFAULT 1,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 权限表
-- =============================================
CREATE TABLE permissions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    permission_code VARCHAR(100) NOT NULL UNIQUE,
    permission_name VARCHAR(100) NOT NULL,
    permission_type VARCHAR(20) NOT NULL,
    parent_id BIGINT DEFAULT 0,
    status TINYINT DEFAULT 1,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 用户角色关联表
-- =============================================
CREATE TABLE user_roles (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_user_role (user_id, role_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 角色权限关联表
-- =============================================
CREATE TABLE role_permissions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    role_id BIGINT NOT NULL,
    permission_id BIGINT NOT NULL,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_role_permission (role_id, permission_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 插入初始数据
-- =============================================

-- 插入默认用户（密码：admin123，已加密）
INSERT INTO users (username, password, real_name, employee_id, email, phone, department, position, status, remark) VALUES
('admin', '$2a$10$7JB720yubVSOfvVWdBYoOeWpyAcXMsEiUHUOWpylsHVSuqiKVoFla', '系统管理员', 'ADMIN001', '<EMAIL>', '13800000000', '信息中心', '系统管理员', 1, '默认系统管理员'),
('procurement01', '$2a$10$7JB720yubVSOfvVWdBYoOeWpyAcXMsEiUHUOWpylsHVSuqiKVoFla', '张采购', 'PROC001', '<EMAIL>', '13800000001', '后勤服务中心', '采购员', 1, '主采购员'),
('procurement02', '$2a$10$7JB720yubVSOfvVWdBYoOeWpyAcXMsEiUHUOWpylsHVSuqiKVoFla', '李采购', 'PROC002', '<EMAIL>', '13800000002', '后勤服务中心', '采购员', 1, '副采购员');

-- 插入基础角色
INSERT INTO roles (role_code, role_name, description, status) VALUES
('ADMIN', '系统管理员', '系统管理员，拥有所有权限', 1),
('PROCUREMENT_STAFF', '采购员', '采购实施人员，执行具体采购工作', 1),
('FINANCE_STAFF', '财务人员', '财务处理人员，负责付款处理', 1);

-- 插入基础权限
INSERT INTO permissions (permission_code, permission_name, permission_type, parent_id, status) VALUES
('SYSTEM', '系统管理', 'MENU', 0, 1),
('PROCUREMENT', '采购管理', 'MENU', 0, 1),
('FINANCE', '财务管理', 'MENU', 0, 1);

-- 分配用户角色
INSERT INTO user_roles (user_id, role_id) VALUES
(1, 1),
(2, 2),
(3, 2);

-- 分配角色权限
INSERT INTO role_permissions (role_id, permission_id) VALUES
(1, 1),
(1, 2),
(1, 3),
(2, 2),
(3, 3);

-- =============================================
-- 验证数据
-- =============================================
SELECT '数据库初始化完成！' as status;
SELECT '默认管理员账户：admin/admin123' as login_info;
SELECT COUNT(*) as user_count FROM users;
SELECT COUNT(*) as role_count FROM roles;
SELECT COUNT(*) as permission_count FROM permissions;

-- 显示用户信息
SELECT id, username, real_name, department, position FROM users;
