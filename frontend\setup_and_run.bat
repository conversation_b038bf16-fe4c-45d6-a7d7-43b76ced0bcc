@echo off
echo ================================
echo 采购数字化综合管理平台前端项目
echo ================================
echo.

echo 检查Node.js环境...
node --version
if errorlevel 1 (
    echo 错误：Node.js环境未配置
    pause
    exit /b 1
)

echo 检查npm环境...
npm --version
if errorlevel 1 (
    echo 错误：npm环境未配置
    pause
    exit /b 1
)
echo.

echo 当前目录：%CD%
echo.

echo [1/3] 安装项目依赖...
npm install
if errorlevel 1 (
    echo 错误：依赖安装失败
    pause
    exit /b 1
)
echo ✅ 依赖安装成功
echo.

echo [2/3] 检查项目配置...
if not exist "src\main.ts" (
    echo 错误：项目文件不完整
    pause
    exit /b 1
)
echo ✅ 项目配置检查通过
echo.

echo [3/3] 启动开发服务器...
echo 前端地址：http://localhost:3000
echo 后端地址：http://localhost:8080
echo 请确保后端服务已启动
echo.
echo 按 Ctrl+C 停止服务
echo.
npm run dev
