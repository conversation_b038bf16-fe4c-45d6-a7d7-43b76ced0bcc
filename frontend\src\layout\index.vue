<template>
  <div class="layout-container">
    <el-container>
      <!-- 侧边栏 -->
      <el-aside :width="isCollapse ? '64px' : '240px'" class="sidebar">
        <div class="logo">
          <div class="logo-icon">📋</div>
          <span v-show="!isCollapse">采购管理平台</span>
        </div>
        
        <el-menu
          :default-active="activeMenu"
          :collapse="isCollapse"
          :unique-opened="true"
          router
          class="sidebar-menu"
        >
          <el-menu-item index="/dashboard">
            <el-icon><House /></el-icon>
            <template #title>工作台</template>
          </el-menu-item>
          
          <el-sub-menu index="/system">
            <template #title>
              <el-icon><Setting /></el-icon>
              <span>系统管理</span>
            </template>
            <el-menu-item index="/system/user">
              <el-icon><User /></el-icon>
              <template #title>用户管理</template>
            </el-menu-item>
            <el-menu-item index="/system/role">
              <el-icon><UserFilled /></el-icon>
              <template #title>角色管理</template>
            </el-menu-item>
          </el-sub-menu>
          
          <el-sub-menu index="/procurement">
            <template #title>
              <el-icon><ShoppingCart /></el-icon>
              <span>采购管理</span>
            </template>
            <el-menu-item index="/procurement/requirement">
              <el-icon><Document /></el-icon>
              <template #title>需求管理</template>
            </el-menu-item>
            <el-menu-item index="/procurement/project">
              <el-icon><Folder /></el-icon>
              <template #title>项目管理</template>
            </el-menu-item>
            <el-menu-item index="/procurement/implementation">
              <el-icon><Operation /></el-icon>
              <template #title>采购实施</template>
            </el-menu-item>
            <el-menu-item index="/procurement/tracking">
              <el-icon><View /></el-icon>
              <template #title>采购跟踪</template>
            </el-menu-item>
          </el-sub-menu>

          <el-sub-menu index="/supplier">
            <template #title>
              <el-icon><OfficeBuilding /></el-icon>
              <span>供应商管理</span>
            </template>
            <el-menu-item index="/supplier/register">
              <el-icon><Plus /></el-icon>
              <template #title>供应商注册</template>
            </el-menu-item>
            <el-menu-item index="/supplier/qualification">
              <el-icon><Medal /></el-icon>
              <template #title>资质管理</template>
            </el-menu-item>
            <el-menu-item index="/supplier/evaluation">
              <el-icon><Star /></el-icon>
              <template #title>绩效评估</template>
            </el-menu-item>
          </el-sub-menu>

          <el-sub-menu index="/contract">
            <template #title>
              <el-icon><DocumentCopy /></el-icon>
              <span>合同管理</span>
            </template>
            <el-menu-item index="/contract/signing">
              <el-icon><Edit /></el-icon>
              <template #title>合同签订</template>
            </el-menu-item>
            <el-menu-item index="/contract/supervision">
              <el-icon><Monitor /></el-icon>
              <template #title>履约监督</template>
            </el-menu-item>
            <el-menu-item index="/contract/acceptance">
              <el-icon><Select /></el-icon>
              <template #title>验收管理</template>
            </el-menu-item>
          </el-sub-menu>

          <el-sub-menu index="/finance">
            <template #title>
              <el-icon><Money /></el-icon>
              <span>财务管理</span>
            </template>
            <el-menu-item index="/finance/budget">
              <el-icon><Wallet /></el-icon>
              <template #title>预算控制</template>
            </el-menu-item>
            <el-menu-item index="/finance/payment">
              <el-icon><CreditCard /></el-icon>
              <template #title>分期付款</template>
            </el-menu-item>
            <el-menu-item index="/finance/approval">
              <el-icon><CircleCheck /></el-icon>
              <template #title>付款审批</template>
            </el-menu-item>
          </el-sub-menu>

          <el-sub-menu index="/classified">
            <template #title>
              <el-icon><Lock /></el-icon>
              <span>涉密项目</span>
            </template>
            <el-menu-item index="/classified/permission">
              <el-icon><Key /></el-icon>
              <template #title>权限管理</template>
            </el-menu-item>
            <el-menu-item index="/classified/log">
              <el-icon><List /></el-icon>
              <template #title>访问日志</template>
            </el-menu-item>
          </el-sub-menu>
        </el-menu>
      </el-aside>
      
      <!-- 主内容区 -->
      <el-container>
        <!-- 顶部导航 -->
        <el-header class="header">
          <div class="header-left">
            <el-button
              text
              @click="toggleCollapse"
            >
              <el-icon size="20">
                <Expand v-if="isCollapse" />
                <Fold v-else />
              </el-icon>
            </el-button>
            
            <el-breadcrumb separator="/">
              <el-breadcrumb-item
                v-for="item in breadcrumbs"
                :key="item.path"
                :to="item.path"
              >
                {{ item.title }}
              </el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          
          <div class="header-right">
            <el-dropdown @command="handleCommand">
              <div class="user-info">
                <el-avatar :size="32" :src="userStore.userInfo?.avatar">
                  {{ userStore.userInfo?.realName?.charAt(0) }}
                </el-avatar>
                <span class="username">{{ userStore.userInfo?.realName }}</span>
                <el-icon><ArrowDown /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">个人中心</el-dropdown-item>
                  <el-dropdown-item command="settings">系统设置</el-dropdown-item>
                  <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>
        
        <!-- 主内容 -->
        <el-main class="main-content">
          <router-view />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessageBox, ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'
import {
  House,
  Setting,
  User,
  UserFilled,
  ShoppingCart,
  Document,
  Folder,
  Operation,
  View,
  OfficeBuilding,
  Plus,
  Medal,
  Star,
  DocumentCopy,
  Edit,
  Monitor,
  Select,
  Money,
  Wallet,
  CreditCard,
  CircleCheck,
  Lock,
  Key,
  List,
  Expand,
  Fold
} from '@element-plus/icons-vue'

const route = useRoute()
const userStore = useUserStore()

const isCollapse = ref(false)

const activeMenu = computed(() => route.path)

const breadcrumbs = computed(() => {
  const matched = route.matched.filter(item => item.meta && item.meta.title)
  return matched.map(item => ({
    path: item.path,
    title: item.meta.title
  }))
})

const toggleCollapse = () => {
  isCollapse.value = !isCollapse.value
}

const handleCommand = async (command: string) => {
  switch (command) {
    case 'profile':
      ElMessage.info('个人中心功能开发中...')
      break
    case 'settings':
      ElMessage.info('系统设置功能开发中...')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm(
          '确定要退出登录吗？',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        await userStore.logout()
        ElMessage.success('退出登录成功')
      } catch (error) {
        // 用户取消
      }
      break
  }
}
</script>

<style lang="scss" scoped>
.layout-container {
  height: 100vh;
}

.sidebar {
  background: #304156;
  transition: width 0.3s;
  
  .logo {
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 20px;
    background: #2b3a4b;
    
    .logo-icon {
      width: 32px;
      height: 32px;
      font-size: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    span {
      margin-left: 12px;
      color: #fff;
      font-size: 16px;
      font-weight: 600;
    }
  }
  
  .sidebar-menu {
    border: none;
    background: #304156;
    
    :deep(.el-menu-item) {
      color: #bfcbd9;
      
      &:hover {
        background: #263445;
        color: #fff;
      }
      
      &.is-active {
        background: #409eff;
        color: #fff;
      }
    }
    
    :deep(.el-sub-menu__title) {
      color: #bfcbd9;

      &:hover {
        background: #263445;
        color: #fff;
      }
    }

    :deep(.el-sub-menu) {
      .el-menu-item {
        background: #1f2d3d !important;
        color: #bfcbd9 !important;

        &:hover {
          background: #001528 !important;
          color: #fff !important;
        }

        &.is-active {
          background: #409eff !important;
          color: #fff !important;
        }
      }
    }
  }
}

.header {
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  
  .header-left {
    display: flex;
    align-items: center;
    gap: 20px;
  }
  
  .header-right {
    .user-info {
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;
      padding: 8px;
      border-radius: 4px;
      transition: background-color 0.3s;
      
      &:hover {
        background: #f5f7fa;
      }
      
      .username {
        font-size: 14px;
        color: #606266;
      }
    }
  }
}

.main-content {
  background: #f0f2f5;
  padding: 20px;
}
</style>
