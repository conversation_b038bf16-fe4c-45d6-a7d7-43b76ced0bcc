<template>
  <div class="layout-container">
    <el-container>
      <!-- 侧边栏 -->
      <el-aside :width="isCollapse ? '64px' : '240px'" class="sidebar">
        <div class="logo">
          <div class="logo-icon">📋</div>
          <span v-show="!isCollapse">采购管理平台</span>
        </div>
        
        <el-menu
          :default-active="activeMenu"
          :collapse="isCollapse"
          :unique-opened="true"
          router
          class="sidebar-menu"
        >
          <el-menu-item index="/dashboard">
            <el-icon><House /></el-icon>
            <template #title>工作台</template>
          </el-menu-item>
          
          <el-sub-menu index="/system">
            <template #title>
              <el-icon><Setting /></el-icon>
              <span>系统管理</span>
            </template>
            <el-menu-item index="/system/user">
              <el-icon><User /></el-icon>
              <template #title>用户管理</template>
            </el-menu-item>
            <el-menu-item index="/system/role">
              <el-icon><UserFilled /></el-icon>
              <template #title>角色管理</template>
            </el-menu-item>
          </el-sub-menu>
          
          <el-sub-menu index="/procurement">
            <template #title>
              <el-icon><ShoppingCart /></el-icon>
              <span>采购管理</span>
            </template>
            <el-menu-item index="/procurement/requirement">
              <el-icon><Document /></el-icon>
              <template #title>需求管理</template>
            </el-menu-item>
            <el-menu-item index="/procurement/project">
              <el-icon><Folder /></el-icon>
              <template #title>项目管理</template>
            </el-menu-item>
            <el-menu-item index="/procurement/implementation">
              <el-icon><Operation /></el-icon>
              <template #title>采购实施</template>
            </el-menu-item>
            <el-menu-item index="/procurement/tracking">
              <el-icon><View /></el-icon>
              <template #title>采购跟踪</template>
            </el-menu-item>
          </el-sub-menu>

          <el-sub-menu index="/supplier">
            <template #title>
              <el-icon><OfficeBuilding /></el-icon>
              <span>供应商管理</span>
            </template>
            <el-menu-item index="/supplier/register">
              <el-icon><Plus /></el-icon>
              <template #title>供应商注册</template>
            </el-menu-item>
            <el-menu-item index="/supplier/qualification">
              <el-icon><Medal /></el-icon>
              <template #title>资质管理</template>
            </el-menu-item>
            <el-menu-item index="/supplier/evaluation">
              <el-icon><Star /></el-icon>
              <template #title>绩效评估</template>
            </el-menu-item>
          </el-sub-menu>

          <el-sub-menu index="/contract">
            <template #title>
              <el-icon><DocumentCopy /></el-icon>
              <span>合同管理</span>
            </template>
            <el-menu-item index="/contract/signing">
              <el-icon><Edit /></el-icon>
              <template #title>合同签订</template>
            </el-menu-item>
            <el-menu-item index="/contract/supervision">
              <el-icon><Monitor /></el-icon>
              <template #title>履约监督</template>
            </el-menu-item>
            <el-menu-item index="/contract/acceptance">
              <el-icon><Select /></el-icon>
              <template #title>验收管理</template>
            </el-menu-item>
          </el-sub-menu>

          <el-sub-menu index="/finance">
            <template #title>
              <el-icon><Money /></el-icon>
              <span>财务管理</span>
            </template>
            <el-menu-item index="/finance/budget">
              <el-icon><Wallet /></el-icon>
              <template #title>预算控制</template>
            </el-menu-item>
            <el-menu-item index="/finance/payment">
              <el-icon><CreditCard /></el-icon>
              <template #title>分期付款</template>
            </el-menu-item>
            <el-menu-item index="/finance/approval">
              <el-icon><CircleCheck /></el-icon>
              <template #title>付款审批</template>
            </el-menu-item>
          </el-sub-menu>

          <el-sub-menu index="/classified">
            <template #title>
              <el-icon><Lock /></el-icon>
              <span>涉密项目</span>
            </template>
            <el-menu-item index="/classified/permission">
              <el-icon><Key /></el-icon>
              <template #title>权限管理</template>
            </el-menu-item>
            <el-menu-item index="/classified/log">
              <el-icon><List /></el-icon>
              <template #title>访问日志</template>
            </el-menu-item>
          </el-sub-menu>

          <el-sub-menu index="/monitor">
            <template #title>
              <el-icon><Monitor /></el-icon>
              <span>监督预警</span>
            </template>
            <el-menu-item index="/monitor/warning">
              <el-icon><Warning /></el-icon>
              <template #title>预警管理</template>
            </el-menu-item>
            <el-menu-item index="/monitor/risk">
              <el-icon><WarnTriangleFilled /></el-icon>
              <template #title>风险监控</template>
            </el-menu-item>
            <el-menu-item index="/monitor/audit">
              <el-icon><View /></el-icon>
              <template #title>审计监督</template>
            </el-menu-item>
            <el-menu-item index="/monitor/compliance">
              <el-icon><Shield /></el-icon>
              <template #title>合规检查</template>
            </el-menu-item>
          </el-sub-menu>

          <el-sub-menu index="/analytics">
            <template #title>
              <el-icon><TrendCharts /></el-icon>
              <span>统计分析</span>
            </template>
            <el-menu-item index="/analytics/dashboard">
              <el-icon><DataAnalysis /></el-icon>
              <template #title>数据分析</template>
            </el-menu-item>
            <el-menu-item index="/analytics/report">
              <el-icon><Document /></el-icon>
              <template #title>报表中心</template>
            </el-menu-item>
            <el-menu-item index="/analytics/performance">
              <el-icon><Odometer /></el-icon>
              <template #title>绩效分析</template>
            </el-menu-item>
            <el-menu-item index="/analytics/cost">
              <el-icon><PieChart /></el-icon>
              <template #title>成本分析</template>
            </el-menu-item>
          </el-sub-menu>
        </el-menu>
      </el-aside>
      
      <!-- 主内容区 -->
      <el-container>
        <!-- 顶部导航 -->
        <el-header class="header">
          <div class="header-left">
            <el-button
              text
              @click="toggleCollapse"
            >
              <el-icon size="20">
                <Expand v-if="isCollapse" />
                <Fold v-else />
              </el-icon>
            </el-button>
            
            <el-breadcrumb separator="/">
              <el-breadcrumb-item
                v-for="item in breadcrumbs"
                :key="item.path"
                :to="item.path"
              >
                {{ item.title }}
              </el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          
          <div class="header-right">
            <el-dropdown @command="handleCommand">
              <div class="user-info">
                <el-avatar :size="32" :src="userStore.userInfo?.avatar">
                  {{ userStore.userInfo?.realName?.charAt(0) }}
                </el-avatar>
                <span class="username">{{ userStore.userInfo?.realName }}</span>
                <el-icon><ArrowDown /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">个人中心</el-dropdown-item>
                  <el-dropdown-item command="settings">系统设置</el-dropdown-item>
                  <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>
        
        <!-- 主内容 -->
        <el-main class="main-content">
          <router-view />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessageBox, ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'
import {
  House,
  Setting,
  User,
  UserFilled,
  ShoppingCart,
  Document,
  Folder,
  Operation,
  View,
  OfficeBuilding,
  Plus,
  Medal,
  Star,
  DocumentCopy,
  Edit,
  Monitor,
  Select,
  Money,
  Wallet,
  CreditCard,
  CircleCheck,
  Lock,
  Key,
  List,
  Expand,
  Fold
} from '@element-plus/icons-vue'

const route = useRoute()
const userStore = useUserStore()

const isCollapse = ref(false)

const activeMenu = computed(() => route.path)

const breadcrumbs = computed(() => {
  const matched = route.matched.filter(item => item.meta && item.meta.title)
  return matched.map(item => ({
    path: item.path,
    title: item.meta.title
  }))
})

const toggleCollapse = () => {
  isCollapse.value = !isCollapse.value
}

const handleCommand = async (command: string) => {
  switch (command) {
    case 'profile':
      ElMessage.info('个人中心功能开发中...')
      break
    case 'settings':
      ElMessage.info('系统设置功能开发中...')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm(
          '确定要退出登录吗？',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        await userStore.logout()
        ElMessage.success('退出登录成功')
      } catch (error) {
        // 用户取消
      }
      break
  }
}
</script>

<style lang="scss" scoped>
.layout-container {
  height: 100vh;
}

.sidebar {
  background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
  transition: all 0.3s ease;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);

  .logo {
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 24px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: rgba(255, 255, 255, 0.1);
    }

    .logo-icon {
      width: 36px;
      height: 36px;
      font-size: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(255, 255, 255, 0.15);
      border-radius: 8px;
      backdrop-filter: blur(10px);
    }

    span {
      margin-left: 16px;
      color: #fff;
      font-size: 18px;
      font-weight: 700;
      letter-spacing: 0.5px;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }
  }
  
  .sidebar-menu {
    border: none;
    background: transparent;
    padding: 8px 0;

    :deep(.el-menu-item) {
      color: rgba(255, 255, 255, 0.8);
      margin: 4px 12px;
      border-radius: 8px;
      transition: all 0.3s ease;
      font-weight: 500;

      &:hover {
        background: rgba(255, 255, 255, 0.1);
        color: #fff;
        transform: translateX(4px);
      }

      &.is-active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #fff;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);

        .el-icon {
          transform: scale(1.1);
        }
      }

      .el-icon {
        transition: transform 0.3s ease;
        margin-right: 8px;
      }
    }

    :deep(.el-sub-menu__title) {
      color: rgba(255, 255, 255, 0.8);
      margin: 4px 12px;
      border-radius: 8px;
      transition: all 0.3s ease;
      font-weight: 500;

      &:hover {
        background: rgba(255, 255, 255, 0.1);
        color: #fff;
        transform: translateX(4px);
      }

      .el-icon {
        transition: transform 0.3s ease;
        margin-right: 8px;
      }
    }

    :deep(.el-sub-menu) {
      .el-menu-item {
        background: transparent !important;
        color: rgba(255, 255, 255, 0.7) !important;
        margin: 2px 24px !important;
        border-radius: 6px !important;
        font-size: 14px !important;

        &:hover {
          background: rgba(255, 255, 255, 0.08) !important;
          color: #fff !important;
          transform: translateX(8px) !important;
        }

        &.is-active {
          background: linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%) !important;
          color: #fff !important;
          box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3) !important;
        }
      }

      .el-sub-menu__icon-arrow {
        transition: transform 0.3s ease;
      }

      &.is-opened .el-sub-menu__icon-arrow {
        transform: rotateZ(180deg);
      }
    }
  }
}

.header {
  background: linear-gradient(135deg, #fff 0%, #f8fafc 100%);
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);

  .header-left {
    display: flex;
    align-items: center;
    gap: 24px;

    .el-button {
      border: none;
      background: rgba(102, 126, 234, 0.1);
      color: #667eea;
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(102, 126, 234, 0.2);
        transform: scale(1.05);
      }
    }

    .el-breadcrumb {
      font-weight: 500;

      :deep(.el-breadcrumb__item) {
        .el-breadcrumb__inner {
          color: #64748b;
          transition: color 0.3s ease;

          &:hover {
            color: #667eea;
          }
        }

        &:last-child .el-breadcrumb__inner {
          color: #1e293b;
          font-weight: 600;
        }
      }
    }
  }

  .header-right {
    .user-info {
      display: flex;
      align-items: center;
      gap: 12px;
      cursor: pointer;
      padding: 12px 16px;
      border-radius: 12px;
      transition: all 0.3s ease;
      background: rgba(102, 126, 234, 0.05);
      border: 1px solid rgba(102, 126, 234, 0.1);

      &:hover {
        background: rgba(102, 126, 234, 0.1);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
      }

      .el-avatar {
        border: 2px solid rgba(102, 126, 234, 0.2);
        transition: all 0.3s ease;
      }

      .username {
        font-size: 15px;
        color: #1e293b;
        font-weight: 600;
        letter-spacing: 0.3px;
      }

      .el-icon {
        color: #667eea;
        transition: transform 0.3s ease;
      }

      &:hover .el-icon {
        transform: rotate(180deg);
      }
    }
  }
}

.main-content {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 24px;
  min-height: calc(100vh - 64px);

  :deep(.el-card) {
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(226, 232, 240, 0.8);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    }
  }
}
</style>
