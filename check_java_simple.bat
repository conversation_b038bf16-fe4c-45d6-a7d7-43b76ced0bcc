@echo off
echo ================================
echo Java环境检查脚本
echo 采购数字化综合管理平台
echo ================================
echo.

echo [1] 检查Java版本...
java -version
if errorlevel 1 (
    echo.
    echo ❌ Java未安装或未正确配置
    echo.
    echo 请安装Java 21:
    echo 1. 访问 https://adoptium.net/
    echo 2. 下载 OpenJDK 21 LTS Windows x64 .msi
    echo 3. 运行安装程序
    echo 4. 重启命令提示符
    echo.
    goto end
)
echo ✅ Java已安装

echo.
echo [2] 检查Java编译器...
javac -version
if errorlevel 1 (
    echo ❌ Java编译器未找到，请安装JDK而不是JRE
) else (
    echo ✅ Java编译器正常
)

echo.
echo [3] 检查JAVA_HOME...
if "%JAVA_HOME%"=="" (
    echo ❌ JAVA_HOME未设置
) else (
    echo ✅ JAVA_HOME: %JAVA_HOME%
)

echo.
echo [4] 检查版本兼容性...
java -version 2>&1 | find "21." >nul
if not errorlevel 1 (
    echo ✅ Java 21 - 最佳版本
    goto compatible
)

java -version 2>&1 | find "17." >nul
if not errorlevel 1 (
    echo ✅ Java 17 - 兼容版本
    goto compatible
)

java -version 2>&1 | find "11." >nul
if not errorlevel 1 (
    echo ✅ Java 11 - 最低版本
    goto compatible
)

echo ⚠️ 建议使用Java 21/17/11 LTS版本
goto end

:compatible
echo ✅ 版本兼容Maven和Spring Boot

:end
echo.
echo ================================
echo 检查完成
echo ================================
echo.
echo 如果有问题，请：
echo 1. 安装Java 21 LTS
echo 2. 重启命令提示符
echo 3. 重新运行此脚本
echo.
pause
