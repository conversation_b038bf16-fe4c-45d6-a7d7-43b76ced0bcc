import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { LoginForm, UserInfo } from '@/types/user'
import { login, getUserInfo, logout } from '@/api/user'
import { getToken, setToken, removeToken } from '@/utils/auth'
import router from '@/router'

export const useUserStore = defineStore('user', () => {
  const token = ref<string>(getToken() || '')
  const userInfo = ref<UserInfo | null>(null)
  const permissions = ref<string[]>([])

  // 登录
  const userLogin = async (loginForm: LoginForm) => {
    try {
      const response = await login(loginForm)
      const { token: newToken } = response.data
      
      token.value = newToken
      setToken(newToken)
      
      // 获取用户信息
      await getUserInfo()
      
      return response
    } catch (error) {
      throw error
    }
  }

  // 获取用户信息
  const getUserInfoAction = async () => {
    try {
      const response = await getUserInfo()
      const { userInfo: info, permissions: perms } = response.data
      
      userInfo.value = info
      permissions.value = perms
      
      return response
    } catch (error) {
      throw error
    }
  }

  // 登出
  const userLogout = async () => {
    try {
      await logout()
    } catch (error) {
      console.error('登出接口调用失败:', error)
    } finally {
      token.value = ''
      userInfo.value = null
      permissions.value = []
      removeToken()
      router.push('/login')
    }
  }

  // 初始化用户信息
  const initUserInfo = async () => {
    if (token.value && !userInfo.value) {
      try {
        await getUserInfoAction()
      } catch (error) {
        console.error('初始化用户信息失败:', error)
        userLogout()
      }
    }
  }

  // 检查权限
  const hasPermission = (permission: string): boolean => {
    return permissions.value.includes(permission) || permissions.value.includes('*')
  }

  // 检查角色
  const hasRole = (role: string): boolean => {
    return userInfo.value?.roles?.includes(role) || false
  }

  return {
    token,
    userInfo,
    permissions,
    login: userLogin,
    getUserInfo: getUserInfoAction,
    logout: userLogout,
    initUserInfo,
    hasPermission,
    hasRole
  }
})
