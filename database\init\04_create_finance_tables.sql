-- =============================================
-- 采购数字化综合管理平台财务管理和特殊功能表
-- =============================================

USE procurement_platform;

-- =============================================
-- 8. 财务管理表
-- =============================================

-- 付款计划表
CREATE TABLE payment_plans (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '付款计划ID',
    plan_no VARCHAR(50) NOT NULL UNIQUE COMMENT '计划编号',
    contract_id BIGINT NOT NULL COMMENT '合同ID',
    total_amount DECIMAL(15,2) NOT NULL COMMENT '合同总金额',
    payment_method VARCHAR(50) NOT NULL COMMENT '付款方式：LUMP_SUM-一次性，INSTALLMENT-分期',
    installment_count INT DEFAULT 1 COMMENT '分期数量',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人ID',
    update_by BIGINT COMMENT '更新人ID',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    version INT DEFAULT 1 COMMENT '版本号（乐观锁）',
    remark TEXT COMMENT '备注',
    INDEX idx_plan_no (plan_no),
    INDEX idx_contract_id (contract_id),
    FOREIGN KEY (contract_id) REFERENCES contracts(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='付款计划表';

-- 付款明细表
CREATE TABLE payment_details (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '付款明细ID',
    plan_id BIGINT NOT NULL COMMENT '付款计划ID',
    installment_no INT NOT NULL COMMENT '期数',
    payment_amount DECIMAL(15,2) NOT NULL COMMENT '付款金额',
    payment_ratio DECIMAL(5,2) NOT NULL COMMENT '付款比例（百分比）',
    planned_date DATE NOT NULL COMMENT '计划付款日期',
    payment_condition TEXT COMMENT '付款条件',
    payment_status VARCHAR(20) DEFAULT 'PENDING' COMMENT '付款状态：PENDING-待付款，PROCESSING-处理中，PAID-已付款，OVERDUE-已逾期',
    due_date DATE NOT NULL COMMENT '应付款日期',
    working_days_limit INT DEFAULT 30 COMMENT '付款期限（工作日）',
    warning_days VARCHAR(50) DEFAULT '10,5,3,1' COMMENT '预警天数设置',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人ID',
    update_by BIGINT COMMENT '更新人ID',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    version INT DEFAULT 1 COMMENT '版本号（乐观锁）',
    INDEX idx_plan_id (plan_id),
    INDEX idx_installment_no (installment_no),
    INDEX idx_payment_status (payment_status),
    INDEX idx_due_date (due_date),
    INDEX idx_planned_date (planned_date),
    FOREIGN KEY (plan_id) REFERENCES payment_plans(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='付款明细表';

-- 付款记录表
CREATE TABLE payment_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '付款记录ID',
    payment_no VARCHAR(50) NOT NULL UNIQUE COMMENT '付款编号',
    detail_id BIGINT NOT NULL COMMENT '付款明细ID',
    contract_id BIGINT NOT NULL COMMENT '合同ID',
    supplier_id BIGINT NOT NULL COMMENT '供应商ID',
    payment_amount DECIMAL(15,2) NOT NULL COMMENT '实际付款金额',
    payment_date DATE NOT NULL COMMENT '实际付款日期',
    payment_method VARCHAR(50) COMMENT '付款方式：BANK_TRANSFER-银行转账，CHECK-支票，CASH-现金',
    bank_account VARCHAR(100) COMMENT '收款账户',
    transaction_no VARCHAR(100) COMMENT '交易流水号',
    invoice_no VARCHAR(100) COMMENT '发票号码',
    invoice_amount DECIMAL(15,2) COMMENT '发票金额',
    tax_amount DECIMAL(15,2) COMMENT '税额',
    applicant_id BIGINT NOT NULL COMMENT '申请人ID',
    applicant_name VARCHAR(100) NOT NULL COMMENT '申请人姓名',
    approver_id BIGINT COMMENT '审批人ID',
    approver_name VARCHAR(100) COMMENT '审批人姓名',
    approval_time DATETIME COMMENT '审批时间',
    approval_opinion TEXT COMMENT '审批意见',
    payment_status VARCHAR(20) DEFAULT 'APPLIED' COMMENT '付款状态：APPLIED-已申请，APPROVED-已审批，PAID-已付款，REJECTED-已拒绝',
    is_overdue TINYINT DEFAULT 0 COMMENT '是否逾期：0-否，1-是',
    overdue_days INT DEFAULT 0 COMMENT '逾期天数',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人ID',
    update_by BIGINT COMMENT '更新人ID',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    version INT DEFAULT 1 COMMENT '版本号（乐观锁）',
    remark TEXT COMMENT '备注',
    INDEX idx_payment_no (payment_no),
    INDEX idx_detail_id (detail_id),
    INDEX idx_contract_id (contract_id),
    INDEX idx_supplier_id (supplier_id),
    INDEX idx_payment_date (payment_date),
    INDEX idx_payment_status (payment_status),
    INDEX idx_is_overdue (is_overdue),
    INDEX idx_applicant_id (applicant_id),
    INDEX idx_approver_id (approver_id),
    FOREIGN KEY (detail_id) REFERENCES payment_details(id),
    FOREIGN KEY (contract_id) REFERENCES contracts(id),
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
    FOREIGN KEY (applicant_id) REFERENCES users(id),
    FOREIGN KEY (approver_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='付款记录表';

-- =============================================
-- 9. 双人制操作记录表
-- =============================================

-- 双人制操作记录表
CREATE TABLE dual_person_operations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '操作记录ID',
    operation_no VARCHAR(50) NOT NULL UNIQUE COMMENT '操作编号',
    operation_type VARCHAR(50) NOT NULL COMMENT '操作类型：PROJECT_CREATE-项目创建，CONTRACT_SIGN-合同签订，PAYMENT_APPROVE-付款审批',
    business_id BIGINT NOT NULL COMMENT '业务ID（项目ID、合同ID等）',
    business_type VARCHAR(50) NOT NULL COMMENT '业务类型：PROJECT-项目，CONTRACT-合同，PAYMENT-付款',
    primary_operator_id BIGINT NOT NULL COMMENT '主操作员ID',
    primary_operator_name VARCHAR(100) NOT NULL COMMENT '主操作员姓名',
    secondary_operator_id BIGINT NOT NULL COMMENT '副操作员ID',
    secondary_operator_name VARCHAR(100) NOT NULL COMMENT '副操作员姓名',
    operation_content TEXT NOT NULL COMMENT '操作内容',
    primary_operation_time DATETIME COMMENT '主操作员操作时间',
    secondary_operation_time DATETIME COMMENT '副操作员操作时间',
    operation_status VARCHAR(20) DEFAULT 'PENDING' COMMENT '操作状态：PENDING-待确认，CONFIRMED-已确认，TIMEOUT-已超时，CANCELLED-已取消',
    timeout_minutes INT DEFAULT 30 COMMENT '超时时间（分钟）',
    primary_signature TEXT COMMENT '主操作员电子签名',
    secondary_signature TEXT COMMENT '副操作员电子签名',
    ip_address VARCHAR(50) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人ID',
    update_by BIGINT COMMENT '更新人ID',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    version INT DEFAULT 1 COMMENT '版本号（乐观锁）',
    remark TEXT COMMENT '备注',
    INDEX idx_operation_no (operation_no),
    INDEX idx_operation_type (operation_type),
    INDEX idx_business_id (business_id),
    INDEX idx_business_type (business_type),
    INDEX idx_primary_operator_id (primary_operator_id),
    INDEX idx_secondary_operator_id (secondary_operator_id),
    INDEX idx_operation_status (operation_status),
    INDEX idx_create_time (create_time),
    FOREIGN KEY (primary_operator_id) REFERENCES users(id),
    FOREIGN KEY (secondary_operator_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='双人制操作记录表';

-- =============================================
-- 10. 涉密项目管理表
-- =============================================

-- 涉密项目权限表
CREATE TABLE classified_project_permissions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '权限ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    permission_level VARCHAR(20) NOT NULL COMMENT '权限级别：READ-只读，WRITE-读写，ADMIN-管理',
    granted_by BIGINT NOT NULL COMMENT '授权人ID',
    granted_time DATETIME NOT NULL COMMENT '授权时间',
    expire_time DATETIME COMMENT '过期时间',
    permission_status VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '权限状态：ACTIVE-有效，EXPIRED-已过期，REVOKED-已撤销',
    access_reason TEXT COMMENT '访问原因',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人ID',
    update_by BIGINT COMMENT '更新人ID',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    version INT DEFAULT 1 COMMENT '版本号（乐观锁）',
    UNIQUE KEY uk_project_user (project_id, user_id),
    INDEX idx_project_id (project_id),
    INDEX idx_user_id (user_id),
    INDEX idx_permission_level (permission_level),
    INDEX idx_permission_status (permission_status),
    INDEX idx_granted_by (granted_by),
    FOREIGN KEY (project_id) REFERENCES procurement_projects(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (granted_by) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='涉密项目权限表';

-- 涉密项目访问日志表
CREATE TABLE classified_access_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    user_name VARCHAR(100) NOT NULL COMMENT '用户姓名',
    access_type VARCHAR(20) NOT NULL COMMENT '访问类型：VIEW-查看，EDIT-编辑，DOWNLOAD-下载，PRINT-打印',
    access_content TEXT COMMENT '访问内容',
    ip_address VARCHAR(50) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    access_time DATETIME NOT NULL COMMENT '访问时间',
    session_id VARCHAR(100) COMMENT '会话ID',
    operation_result VARCHAR(20) DEFAULT 'SUCCESS' COMMENT '操作结果：SUCCESS-成功，FAILED-失败，DENIED-拒绝',
    risk_level VARCHAR(20) DEFAULT 'LOW' COMMENT '风险级别：LOW-低，MEDIUM-中，HIGH-高',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_project_id (project_id),
    INDEX idx_user_id (user_id),
    INDEX idx_access_type (access_type),
    INDEX idx_access_time (access_time),
    INDEX idx_ip_address (ip_address),
    INDEX idx_risk_level (risk_level),
    FOREIGN KEY (project_id) REFERENCES procurement_projects(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='涉密项目访问日志表';

-- =============================================
-- 11. 系统日志表
-- =============================================

-- 系统操作日志表
CREATE TABLE system_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
    log_type VARCHAR(20) NOT NULL COMMENT '日志类型：LOGIN-登录，LOGOUT-登出，OPERATION-操作，ERROR-错误',
    user_id BIGINT COMMENT '用户ID',
    user_name VARCHAR(100) COMMENT '用户姓名',
    operation VARCHAR(100) NOT NULL COMMENT '操作名称',
    method VARCHAR(10) COMMENT '请求方法',
    request_url VARCHAR(500) COMMENT '请求URL',
    request_params TEXT COMMENT '请求参数',
    response_result TEXT COMMENT '响应结果',
    ip_address VARCHAR(50) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    execution_time BIGINT COMMENT '执行时间（毫秒）',
    status VARCHAR(20) DEFAULT 'SUCCESS' COMMENT '状态：SUCCESS-成功，FAILED-失败，ERROR-错误',
    error_message TEXT COMMENT '错误信息',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_log_type (log_type),
    INDEX idx_user_id (user_id),
    INDEX idx_operation (operation),
    INDEX idx_status (status),
    INDEX idx_create_time (create_time),
    INDEX idx_ip_address (ip_address),
    FOREIGN KEY (user_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统操作日志表';

SELECT '财务管理和特殊功能表创建完成！' as '状态';
