# 采购数字化综合管理平台 - 项目结构说明

## 📁 项目目录结构

```
caigoupingtai/                          # 项目根目录
├── docs/                               # 项目文档
│   ├── 1. 采购数字化综合管理平台业务需求书（完善版）.md
│   ├── 2. 采购数字化综合管理平台业务需求规格说明书.md
│   ├── 3. 采购数字化综合管理平台技术方案设计书.md
│   ├── 4. 采购数字化综合管理平台系统概要设计说明书.md
│   ├── 5. 采购数字化综合管理平台详细设计说明书.md
│   ├── 6. 采购数字化综合管理平台系统测试计划文档.md
│   ├── 7. 采购数字化综合管理平台数据库实施文档.md
│   ├── 8. 采购数字化综合管理平台API接口文档.md
│   ├── 9. 采购数字化综合管理平台项目实施计划.md
│   ├── User Guidelines - 采购数字化综合管理平台用户指南.md
│   ├── Windows开发环境安装指南.md
│   └── 项目结构说明.md
├── backend/                            # 后端项目 (Spring Boot)
│   ├── src/
│   │   ├── main/
│   │   │   ├── java/
│   │   │   │   └── com/
│   │   │   │       └── procurement/
│   │   │   │           ├── ProcurementApplication.java
│   │   │   │           ├── config/          # 配置类
│   │   │   │           ├── controller/      # 控制器层
│   │   │   │           ├── service/         # 业务逻辑层
│   │   │   │           ├── mapper/          # 数据访问层
│   │   │   │           ├── entity/          # 实体类
│   │   │   │           ├── dto/             # 数据传输对象
│   │   │   │           ├── vo/              # 视图对象
│   │   │   │           ├── enums/           # 枚举类
│   │   │   │           ├── utils/           # 工具类
│   │   │   │           └── security/        # 安全相关
│   │   │   └── resources/
│   │   │       ├── application.yml         # 主配置文件
│   │   │       ├── application-dev.yml     # 开发环境配置
│   │   │       ├── application-prod.yml    # 生产环境配置
│   │   │       ├── mapper/                 # MyBatis XML文件
│   │   │       └── static/                 # 静态资源
│   │   └── test/                           # 测试代码
│   ├── pom.xml                             # Maven配置文件
│   └── README.md                           # 后端项目说明
├── frontend/                               # 前端项目 (Vue.js 3)
│   ├── public/                             # 公共资源
│   │   ├── index.html
│   │   └── favicon.ico
│   ├── src/
│   │   ├── main.js                         # 入口文件
│   │   ├── App.vue                         # 根组件
│   │   ├── components/                     # 公共组件
│   │   │   ├── common/                     # 通用组件
│   │   │   ├── layout/                     # 布局组件
│   │   │   └── business/                   # 业务组件
│   │   ├── views/                          # 页面组件
│   │   │   ├── login/                      # 登录页面
│   │   │   ├── dashboard/                  # 仪表板
│   │   │   ├── procurement/                # 采购管理
│   │   │   ├── supplier/                   # 供应商管理
│   │   │   ├── contract/                   # 合同管理
│   │   │   ├── finance/                    # 财务管理
│   │   │   ├── report/                     # 报表统计
│   │   │   └── system/                     # 系统管理
│   │   ├── router/                         # 路由配置
│   │   ├── store/                          # 状态管理 (Pinia)
│   │   ├── api/                            # API接口
│   │   ├── utils/                          # 工具函数
│   │   ├── styles/                         # 样式文件
│   │   └── assets/                         # 静态资源
│   ├── package.json                        # 依赖配置
│   ├── vite.config.js                      # Vite配置
│   ├── .env.development                    # 开发环境变量
│   ├── .env.production                     # 生产环境变量
│   └── README.md                           # 前端项目说明
├── database/                               # 数据库相关
│   ├── init/                               # 初始化脚本
│   │   ├── 01_create_database.sql          # 创建数据库
│   │   ├── 02_create_tables.sql            # 创建表结构
│   │   ├── 03_init_data.sql                # 初始化数据
│   │   └── 04_create_indexes.sql           # 创建索引
│   ├── migrations/                         # 数据库迁移脚本
│   └── backup/                             # 数据库备份
├── docker/                                 # Docker相关
│   ├── docker-compose.yml                 # Docker编排文件
│   ├── mysql/                              # MySQL配置
│   ├── redis/                              # Redis配置
│   └── nginx/                              # Nginx配置
├── scripts/                                # 脚本文件
│   ├── build.bat                           # 构建脚本
│   ├── start.bat                           # 启动脚本
│   ├── stop.bat                            # 停止脚本
│   └── deploy.bat                          # 部署脚本
├── tests/                                  # 集成测试
│   ├── api/                                # API测试
│   ├── e2e/                                # 端到端测试
│   └── performance/                        # 性能测试
├── .gitignore                              # Git忽略文件
├── README.md                               # 项目总体说明
├── check_environment.bat                   # 环境检查脚本
└── 项目结构说明.md                          # 本文件
```

## 🎯 目录说明

### 📚 docs/ - 项目文档
包含所有项目相关的文档，包括需求分析、技术设计、用户指南等。

### 🔧 backend/ - 后端项目
基于Spring Boot 2.7.x构建的后端服务，采用分层架构设计：
- **controller**: REST API控制器
- **service**: 业务逻辑层
- **mapper**: 数据访问层 (MyBatis Plus)
- **entity**: 数据库实体类
- **dto**: 数据传输对象
- **vo**: 视图对象
- **config**: 配置类 (Security, Redis, Database等)

### 🎨 frontend/ - 前端项目
基于Vue.js 3.x + Element Plus构建的前端应用：
- **views**: 页面组件，按业务模块组织
- **components**: 可复用组件
- **api**: HTTP请求封装
- **router**: 路由配置
- **store**: 状态管理 (Pinia)
- **utils**: 工具函数

### 🗄️ database/ - 数据库
包含数据库初始化脚本、迁移脚本和备份文件：
- **init**: 数据库初始化脚本
- **migrations**: 版本迁移脚本
- **backup**: 数据库备份文件

### 🐳 docker/ - 容器化
Docker相关配置文件，支持容器化部署：
- **docker-compose.yml**: 服务编排
- **mysql/**: MySQL容器配置
- **redis/**: Redis容器配置
- **nginx/**: Nginx反向代理配置

### 📜 scripts/ - 自动化脚本
Windows批处理脚本，简化开发和部署流程：
- **build.bat**: 项目构建
- **start.bat**: 服务启动
- **stop.bat**: 服务停止
- **deploy.bat**: 部署脚本

## 🚀 开发流程

### 1. 环境准备
```bash
# 1. 运行环境检查
check_environment.bat

# 2. 克隆项目 (如果使用Git)
git clone <repository-url>
cd caigoupingtai
```

### 2. 后端开发
```bash
# 进入后端目录
cd backend

# 安装依赖 (Maven会自动下载)
mvn clean install

# 启动后端服务
mvn spring-boot:run
```

### 3. 前端开发
```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 4. 数据库初始化
```bash
# 连接MySQL数据库
mysql -u root -p

# 执行初始化脚本
source database/init/01_create_database.sql
source database/init/02_create_tables.sql
source database/init/03_init_data.sql
source database/init/04_create_indexes.sql
```

## 🔗 服务端口

| 服务 | 端口 | 说明 |
|------|------|------|
| 前端开发服务器 | 5173 | Vite开发服务器 |
| 后端API服务 | 8080 | Spring Boot应用 |
| MySQL数据库 | 3306 | 主数据库 |
| Redis缓存 | 6379 | 缓存服务 |
| Nginx (生产) | 80/443 | Web服务器 |

## 📝 开发规范

### 代码规范
- **Java**: 遵循阿里巴巴Java开发手册
- **JavaScript**: 使用ESLint + Prettier
- **Vue**: 遵循Vue.js官方风格指南
- **SQL**: 使用下划线命名法

### Git规范
- **分支命名**: feature/功能名, bugfix/问题描述, hotfix/紧急修复
- **提交信息**: type(scope): description
  - feat: 新功能
  - fix: 修复bug
  - docs: 文档更新
  - style: 代码格式调整
  - refactor: 代码重构
  - test: 测试相关
  - chore: 构建过程或辅助工具的变动

### 文档规范
- 所有API接口必须有完整的文档说明
- 重要业务逻辑必须有注释说明
- 数据库表结构变更必须有迁移脚本

## 🛠️ 下一步操作

1. **运行环境检查**: `check_environment.bat`
2. **创建项目目录**: 按照上述结构创建目录
3. **初始化Git仓库**: `git init`
4. **开始后端开发**: 创建Spring Boot项目
5. **开始前端开发**: 创建Vue.js项目
6. **数据库设计**: 创建数据库表结构

---

**项目结构说明完成**

> 这个结构设计考虑了项目的完整生命周期，从开发到部署的各个环节。可以根据实际需要进行调整。
