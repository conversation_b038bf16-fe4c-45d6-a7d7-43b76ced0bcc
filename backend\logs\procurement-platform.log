2025-06-29 16:49:11.938 [main] INFO  com.procurement.ProcurementApplication - Starting ProcurementApplication using Java 21.0.7 on InterNet with PID 7232 (D:\caigoupingtai\backend\target\classes started by HUAWEI in D:\caigoupingtai\backend)
2025-06-29 16:49:11.940 [main] DEBUG com.procurement.ProcurementApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-06-29 16:49:11.940 [main] INFO  com.procurement.ProcurementApplication - The following 1 profile is active: "dev"
2025-06-29 16:49:12.453 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-29 16:49:12.469 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-29 16:49:12.498 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 JPA repository interfaces.
2025-06-29 16:49:12.515 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-29 16:49:12.515 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-29 16:49:12.538 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 6 ms. Found 0 Redis repository interfaces.
2025-06-29 16:49:12.670 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.procurement.mapper]' package. Please check your configuration.
2025-06-29 16:49:13.172 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-29 16:49:13.185 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-29 16:49:13.185 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-06-29 16:49:13.328 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-06-29 16:49:13.328 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1336 ms
2025-06-29 16:49:13.415 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-29 16:49:13.784 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-29 16:49:13.856 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-29 16:49:13.894 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.15.Final
2025-06-29 16:49:14.025 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-29 16:49:14.142 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-06-29 16:49:14.366 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-29 16:49:14.374 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-29 16:49:14.753 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-29 16:49:14.828 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-29 16:49:14.911 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 5 mappings in 'requestMappingHandlerMapping'
2025-06-29 16:49:14.935 [main] DEBUG o.s.web.servlet.handler.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /uploads/**, /swagger-ui/**] in 'resourceHandlerMapping'
2025-06-29 16:49:14.948 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-29 16:49:15.262 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 112ae712-543f-4b5a-9ec0-50d96e7898cd

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-29 16:49:15.354 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [authenticated] for any request
2025-06-29 16:49:15.381 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@583b4af4, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@72c175f1, org.springframework.security.web.context.SecurityContextPersistenceFilter@1e033801, org.springframework.security.web.header.HeaderWriterFilter@123d0816, org.springframework.security.web.csrf.CsrfFilter@149238fe, org.springframework.security.web.authentication.logout.LogoutFilter@4abfa2ff, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@4fe3f9ef, org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter@7d60bd5a, org.springframework.security.web.authentication.ui.DefaultLogoutPageGeneratingFilter@9fd3b61, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@1477d4e6, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@57ed8f95, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1c25deb0, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4a34de5e, org.springframework.security.web.session.SessionManagementFilter@4e210016, org.springframework.security.web.access.ExceptionTranslationFilter@2f64f99f, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@32f45e15]
2025-06-29 16:49:15.463 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/api'
2025-06-29 16:49:15.463 [main] INFO  com.procurement.ProcurementApplication - Started ProcurementApplication in 3.998 seconds (JVM running for 4.322)
2025-06-29 16:51:19.690 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-29 16:51:19.692 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-29 16:51:19.709 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
