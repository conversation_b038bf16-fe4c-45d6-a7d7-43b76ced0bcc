-- =============================================
-- 采购数字化综合管理平台索引优化脚本
-- =============================================

USE procurement_platform;

-- =============================================
-- 1. 复合索引优化
-- =============================================

-- 用户表复合索引
CREATE INDEX idx_users_dept_status ON users(department, status);
CREATE INDEX idx_users_status_create_time ON users(status, create_time);

-- 采购需求表复合索引
CREATE INDEX idx_requirement_dept_status ON procurement_requirements(department, approval_status);
CREATE INDEX idx_requirement_classified_status ON procurement_requirements(is_classified, approval_status);
CREATE INDEX idx_requirement_type_urgency ON procurement_requirements(requirement_type, urgency_level);
CREATE INDEX idx_requirement_applicant_time ON procurement_requirements(applicant_id, create_time);

-- 采购项目表复合索引
CREATE INDEX idx_project_status_type ON procurement_projects(project_status, project_type);
CREATE INDEX idx_project_classified_status ON procurement_projects(is_classified, project_status);
CREATE INDEX idx_project_purchaser_status ON procurement_projects(primary_purchaser_id, project_status);
CREATE INDEX idx_project_date_range ON procurement_projects(start_date, end_date);

-- 供应商表复合索引
CREATE INDEX idx_supplier_type_status ON suppliers(supplier_type, cooperation_status);
CREATE INDEX idx_supplier_status_create ON suppliers(status, create_time);

-- 报价表复合索引
CREATE INDEX idx_quote_project_status ON supplier_quotes(project_id, quote_status);
CREATE INDEX idx_quote_supplier_selected ON supplier_quotes(supplier_id, is_selected);
CREATE INDEX idx_quote_date_status ON supplier_quotes(quote_date, quote_status);

-- 合同表复合索引
CREATE INDEX idx_contract_status_type ON contracts(contract_status, contract_type);
CREATE INDEX idx_contract_performance_status ON contracts(performance_status, contract_status);
CREATE INDEX idx_contract_supplier_status ON contracts(supplier_id, contract_status);
CREATE INDEX idx_contract_date_range ON contracts(signing_date, effective_date);
CREATE INDEX idx_contract_supervisor_status ON contracts(supervisor_id, performance_status);

-- 付款相关复合索引
CREATE INDEX idx_payment_detail_status_due ON payment_details(payment_status, due_date);
CREATE INDEX idx_payment_record_status_date ON payment_records(payment_status, payment_date);
CREATE INDEX idx_payment_record_overdue ON payment_records(is_overdue, overdue_days);
CREATE INDEX idx_payment_record_applicant_status ON payment_records(applicant_id, payment_status);

-- 验收表复合索引
CREATE INDEX idx_acceptance_contract_status ON acceptance_records(contract_id, acceptance_status);
CREATE INDEX idx_acceptance_date_result ON acceptance_records(acceptance_date, acceptance_result);
CREATE INDEX idx_acceptance_participant_signature ON acceptance_participants(acceptance_id, signature_status);

-- 双人制操作复合索引
CREATE INDEX idx_dual_operation_type_status ON dual_person_operations(operation_type, operation_status);
CREATE INDEX idx_dual_operation_business ON dual_person_operations(business_type, business_id);
CREATE INDEX idx_dual_operation_primary_time ON dual_person_operations(primary_operator_id, primary_operation_time);

-- 涉密项目复合索引
CREATE INDEX idx_classified_permission_user_status ON classified_project_permissions(user_id, permission_status);
CREATE INDEX idx_classified_access_project_time ON classified_access_logs(project_id, access_time);
CREATE INDEX idx_classified_access_user_type ON classified_access_logs(user_id, access_type);

-- 系统日志复合索引
CREATE INDEX idx_system_log_type_time ON system_logs(log_type, create_time);
CREATE INDEX idx_system_log_user_time ON system_logs(user_id, create_time);
CREATE INDEX idx_system_log_status_time ON system_logs(status, create_time);

-- =============================================
-- 2. 全文索引（用于搜索功能）
-- =============================================

-- 需求表全文索引
ALTER TABLE procurement_requirements ADD FULLTEXT(requirement_name, usage_purpose, technical_requirements);

-- 项目表全文索引
ALTER TABLE procurement_projects ADD FULLTEXT(project_name, remark);

-- 供应商表全文索引
ALTER TABLE suppliers ADD FULLTEXT(supplier_name, business_scope, remark);

-- 合同表全文索引
ALTER TABLE contracts ADD FULLTEXT(contract_name, contract_terms, remark);

-- =============================================
-- 3. 分区表优化（针对大数据量表）
-- =============================================

-- 系统日志表按月分区（示例，需要重建表）
/*
ALTER TABLE system_logs PARTITION BY RANGE (YEAR(create_time) * 100 + MONTH(create_time)) (
    PARTITION p202501 VALUES LESS THAN (202502),
    PARTITION p202502 VALUES LESS THAN (202503),
    PARTITION p202503 VALUES LESS THAN (202504),
    PARTITION p202504 VALUES LESS THAN (202505),
    PARTITION p202505 VALUES LESS THAN (202506),
    PARTITION p202506 VALUES LESS THAN (202507),
    PARTITION p202507 VALUES LESS THAN (202508),
    PARTITION p202508 VALUES LESS THAN (202509),
    PARTITION p202509 VALUES LESS THAN (202510),
    PARTITION p202510 VALUES LESS THAN (202511),
    PARTITION p202511 VALUES LESS THAN (202512),
    PARTITION p202512 VALUES LESS THAN (202601),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
*/

-- 涉密访问日志表按月分区（示例）
/*
ALTER TABLE classified_access_logs PARTITION BY RANGE (YEAR(access_time) * 100 + MONTH(access_time)) (
    PARTITION p202501 VALUES LESS THAN (202502),
    PARTITION p202502 VALUES LESS THAN (202503),
    PARTITION p202503 VALUES LESS THAN (202504),
    PARTITION p202504 VALUES LESS THAN (202505),
    PARTITION p202505 VALUES LESS THAN (202506),
    PARTITION p202506 VALUES LESS THAN (202507),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
*/

-- =============================================
-- 4. 性能优化建议
-- =============================================

-- 查看表状态和索引使用情况
SELECT 
    TABLE_NAME as '表名',
    TABLE_ROWS as '行数',
    AVG_ROW_LENGTH as '平均行长度',
    DATA_LENGTH as '数据大小',
    INDEX_LENGTH as '索引大小',
    (DATA_LENGTH + INDEX_LENGTH) as '总大小'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'procurement_platform' 
ORDER BY (DATA_LENGTH + INDEX_LENGTH) DESC;

-- 检查未使用的索引（需要在运行一段时间后执行）
/*
SELECT 
    s.TABLE_SCHEMA,
    s.TABLE_NAME,
    s.INDEX_NAME,
    s.COLUMN_NAME
FROM information_schema.STATISTICS s
LEFT JOIN performance_schema.table_io_waits_summary_by_index_usage t 
    ON s.TABLE_SCHEMA = t.OBJECT_SCHEMA 
    AND s.TABLE_NAME = t.OBJECT_NAME 
    AND s.INDEX_NAME = t.INDEX_NAME
WHERE s.TABLE_SCHEMA = 'procurement_platform'
    AND t.INDEX_NAME IS NULL
    AND s.INDEX_NAME != 'PRIMARY'
ORDER BY s.TABLE_NAME, s.INDEX_NAME;
*/

-- =============================================
-- 5. 定期维护脚本
-- =============================================

-- 分析表统计信息（建议定期执行）
ANALYZE TABLE users, roles, permissions, user_roles, role_permissions;
ANALYZE TABLE suppliers, supplier_qualifications;
ANALYZE TABLE procurement_requirements, requirement_items;
ANALYZE TABLE procurement_projects;
ANALYZE TABLE supplier_quotes, quote_items;
ANALYZE TABLE contracts, contract_performance_records;
ANALYZE TABLE acceptance_records, acceptance_participants;
ANALYZE TABLE payment_plans, payment_details, payment_records;
ANALYZE TABLE dual_person_operations;
ANALYZE TABLE classified_project_permissions, classified_access_logs;
ANALYZE TABLE system_logs;

-- 优化表（建议在低峰期执行）
-- OPTIMIZE TABLE system_logs;
-- OPTIMIZE TABLE classified_access_logs;

SELECT '索引创建和优化完成！' as '状态';
SELECT '建议定期执行ANALYZE TABLE和OPTIMIZE TABLE命令' as '维护提醒';
