@echo off
chcp 65001 >nul
echo ================================
echo 检查开发环境安装状态
echo 采购数字化综合管理平台
echo ================================

echo.
echo [1] 检查 Java 环境...
java -version 2>nul
if %errorlevel% neq 0 (
    echo ❌ Java 未正确安装
    echo    请安装 JDK 11+ 并配置环境变量
) else (
    echo ✅ Java 安装正常
)

echo.
echo [2] 检查 Node.js 环境...
node --version 2>nul
if %errorlevel% neq 0 (
    echo ❌ Node.js 未正确安装
    echo    请安装 Node.js 16.x+ 版本
) else (
    echo ✅ Node.js 安装正常
    node --version
)

echo.
echo [3] 检查 npm 环境...
npm --version 2>nul
if %errorlevel% neq 0 (
    echo ❌ npm 未正确安装
    echo    npm 通常随 Node.js 一起安装
) else (
    echo ✅ npm 安装正常
    npm --version
)

echo.
echo [4] 检查 MySQL 环境...
mysql --version 2>nul
if %errorlevel% neq 0 (
    echo ❌ MySQL 未正确安装或未添加到PATH
    echo    请安装 MySQL 8.0+ 并配置环境变量
) else (
    echo ✅ MySQL 安装正常
    mysql --version
)

echo.
echo [5] 检查 Git 环境...
git --version 2>nul
if %errorlevel% neq 0 (
    echo ❌ Git 未正确安装
    echo    请安装 Git for Windows
) else (
    echo ✅ Git 安装正常
    git --version
)

echo.
echo [6] 检查 Redis 环境...
redis-cli ping 2>nul
if %errorlevel% neq 0 (
    echo ❌ Redis 未正确安装或未启动
    echo    请安装 Redis 或启动 Docker 容器
) else (
    echo ✅ Redis 安装正常并运行中
)

echo.
echo [7] 检查端口占用情况...
echo 检查关键端口是否可用...
netstat -an | findstr ":8080" >nul
if %errorlevel% equ 0 (
    echo ⚠️  端口 8080 已被占用 (Spring Boot 默认端口)
) else (
    echo ✅ 端口 8080 可用
)

netstat -an | findstr ":3306" >nul
if %errorlevel% equ 0 (
    echo ✅ 端口 3306 已使用 (MySQL 服务运行中)
) else (
    echo ⚠️  端口 3306 未使用 (MySQL 可能未启动)
)

netstat -an | findstr ":6379" >nul
if %errorlevel% equ 0 (
    echo ✅ 端口 6379 已使用 (Redis 服务运行中)
) else (
    echo ⚠️  端口 6379 未使用 (Redis 可能未启动)
)

echo.
echo ================================
echo 环境检查完成
echo ================================
echo.
echo 如果有 ❌ 标记的项目，请参考 "Windows开发环境安装指南.md" 进行安装
echo 如果所有项目都显示 ✅，您可以开始项目开发了！
echo.
pause
