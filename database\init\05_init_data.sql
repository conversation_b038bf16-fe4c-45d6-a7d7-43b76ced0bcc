-- =============================================
-- 采购数字化综合管理平台初始化数据脚本
-- =============================================

USE procurement_platform;

-- =============================================
-- 1. 初始化权限数据
-- =============================================

-- 插入基础权限
INSERT INTO permissions (permission_code, permission_name, permission_type, parent_id, path, component, icon, sort_order, status, remark) VALUES
-- 系统管理
('SYSTEM', '系统管理', 'MENU', 0, '/system', 'Layout', 'system', 1, 1, '系统管理菜单'),
('SYSTEM:USER', '用户管理', 'MENU', 1, '/system/user', 'system/user/index', 'user', 1, 1, '用户管理'),
('SYSTEM:USER:LIST', '用户查询', 'BUTTON', 2, '', '', '', 1, 1, '用户查询权限'),
('SYSTEM:USER:ADD', '用户新增', 'BUTTON', 2, '', '', '', 2, 1, '用户新增权限'),
('SYSTEM:USER:EDIT', '用户编辑', 'BUTTON', 2, '', '', '', 3, 1, '用户编辑权限'),
('SYSTEM:USER:DELETE', '用户删除', 'BUTTON', 2, '', '', '', 4, 1, '用户删除权限'),
('SYSTEM:ROLE', '角色管理', 'MENU', 1, '/system/role', 'system/role/index', 'role', 2, 1, '角色管理'),
('SYSTEM:ROLE:LIST', '角色查询', 'BUTTON', 7, '', '', '', 1, 1, '角色查询权限'),
('SYSTEM:ROLE:ADD', '角色新增', 'BUTTON', 7, '', '', '', 2, 1, '角色新增权限'),
('SYSTEM:ROLE:EDIT', '角色编辑', 'BUTTON', 7, '', '', '', 3, 1, '角色编辑权限'),
('SYSTEM:ROLE:DELETE', '角色删除', 'BUTTON', 7, '', '', '', 4, 1, '角色删除权限'),

-- 采购管理
('PROCUREMENT', '采购管理', 'MENU', 0, '/procurement', 'Layout', 'procurement', 2, 1, '采购管理菜单'),
('PROCUREMENT:REQUIREMENT', '需求管理', 'MENU', 12, '/procurement/requirement', 'procurement/requirement/index', 'requirement', 1, 1, '需求管理'),
('PROCUREMENT:REQUIREMENT:LIST', '需求查询', 'BUTTON', 13, '', '', '', 1, 1, '需求查询权限'),
('PROCUREMENT:REQUIREMENT:ADD', '需求申报', 'BUTTON', 13, '', '', '', 2, 1, '需求申报权限'),
('PROCUREMENT:REQUIREMENT:EDIT', '需求编辑', 'BUTTON', 13, '', '', '', 3, 1, '需求编辑权限'),
('PROCUREMENT:REQUIREMENT:APPROVE', '需求审批', 'BUTTON', 13, '', '', '', 4, 1, '需求审批权限'),
('PROCUREMENT:PROJECT', '项目管理', 'MENU', 12, '/procurement/project', 'procurement/project/index', 'project', 2, 1, '项目管理'),
('PROCUREMENT:PROJECT:LIST', '项目查询', 'BUTTON', 17, '', '', '', 1, 1, '项目查询权限'),
('PROCUREMENT:PROJECT:ADD', '项目创建', 'BUTTON', 17, '', '', '', 2, 1, '项目创建权限（双人制）'),
('PROCUREMENT:PROJECT:EDIT', '项目编辑', 'BUTTON', 17, '', '', '', 3, 1, '项目编辑权限'),

-- 供应商管理
('SUPPLIER', '供应商管理', 'MENU', 0, '/supplier', 'Layout', 'supplier', 3, 1, '供应商管理菜单'),
('SUPPLIER:LIST', '供应商查询', 'BUTTON', 21, '', '', '', 1, 1, '供应商查询权限'),
('SUPPLIER:ADD', '供应商新增', 'BUTTON', 21, '', '', '', 2, 1, '供应商新增权限'),
('SUPPLIER:EDIT', '供应商编辑', 'BUTTON', 21, '', '', '', 3, 1, '供应商编辑权限'),
('SUPPLIER:QUOTE', '报价管理', 'BUTTON', 21, '', '', '', 4, 1, '报价管理权限'),

-- 合同管理
('CONTRACT', '合同管理', 'MENU', 0, '/contract', 'Layout', 'contract', 4, 1, '合同管理菜单'),
('CONTRACT:LIST', '合同查询', 'BUTTON', 25, '', '', '', 1, 1, '合同查询权限'),
('CONTRACT:ADD', '合同签订', 'BUTTON', 25, '', '', '', 2, 1, '合同签订权限'),
('CONTRACT:EDIT', '合同编辑', 'BUTTON', 25, '', '', '', 3, 1, '合同编辑权限'),
('CONTRACT:PERFORMANCE', '履约监督', 'BUTTON', 25, '', '', '', 4, 1, '履约监督权限'),
('CONTRACT:ACCEPTANCE', '验收管理', 'BUTTON', 25, '', '', '', 5, 1, '验收管理权限（多人制）'),

-- 财务管理
('FINANCE', '财务管理', 'MENU', 0, '/finance', 'Layout', 'finance', 5, 1, '财务管理菜单'),
('FINANCE:PAYMENT', '付款管理', 'BUTTON', 30, '', '', '', 1, 1, '付款管理权限'),
('FINANCE:PAYMENT:APPLY', '付款申请', 'BUTTON', 30, '', '', '', 2, 1, '付款申请权限'),
('FINANCE:PAYMENT:APPROVE', '付款审批', 'BUTTON', 30, '', '', '', 3, 1, '付款审批权限'),

-- 涉密项目管理
('CLASSIFIED', '涉密项目', 'MENU', 0, '/classified', 'Layout', 'classified', 6, 1, '涉密项目管理'),
('CLASSIFIED:ACCESS', '权限管理', 'BUTTON', 34, '', '', '', 1, 1, '涉密项目权限管理'),
('CLASSIFIED:LOG', '访问日志', 'BUTTON', 34, '', '', '', 2, 1, '涉密项目访问日志');

-- =============================================
-- 2. 初始化角色数据
-- =============================================

-- 插入基础角色
INSERT INTO roles (role_code, role_name, description, status, remark) VALUES
('ADMIN', '系统管理员', '系统管理员，拥有所有权限', 1, '系统管理员角色'),
('PROCUREMENT_MANAGER', '采购主管', '采购部门主管，负责采购业务管理', 1, '采购主管角色'),
('PROCUREMENT_STAFF', '采购员', '采购实施人员，执行具体采购工作', 1, '采购员角色'),
('FINANCE_MANAGER', '财务主管', '财务部门主管，负责财务审批', 1, '财务主管角色'),
('FINANCE_STAFF', '财务人员', '财务处理人员，负责付款处理', 1, '财务人员角色'),
('SUPERVISOR', '履约监督员', '专门负责合同履约监督', 1, '履约监督员角色'),
('AUDITOR', '内审人员', '内部审计人员，负责合规检查', 1, '内审人员角色'),
('DEPARTMENT_HEAD', '部门负责人', '各部门负责人，负责需求审批', 1, '部门负责人角色'),
('DEPARTMENT_STAFF', '部门人员', '各部门普通人员，负责需求申报', 1, '部门人员角色'),
('CLASSIFIED_ADMIN', '涉密项目管理员', '涉密项目管理员，负责涉密项目权限管理', 1, '涉密项目管理员角色');

-- =============================================
-- 3. 初始化用户数据
-- =============================================

-- 插入默认管理员用户（密码：admin123，需要加密）
INSERT INTO users (username, password, real_name, employee_id, email, phone, department, position, status, remark) VALUES
('admin', '$2a$10$7JB720yubVSOfvVWdBYoOeWpyAcXMsEiUHUOWpylsHVSuqiKVoFla', '系统管理员', 'ADMIN001', '<EMAIL>', '13800000000', '信息中心', '系统管理员', 1, '默认系统管理员'),
('procurement01', '$2a$10$7JB720yubVSOfvVWdBYoOeWpyAcXMsEiUHUOWpylsHVSuqiKVoFla', '张采购', 'PROC001', '<EMAIL>', '13800000001', '后勤服务中心', '采购员', 1, '主采购员'),
('procurement02', '$2a$10$7JB720yubVSOfvVWdBYoOeWpyAcXMsEiUHUOWpylsHVSuqiKVoFla', '李采购', 'PROC002', '<EMAIL>', '13800000002', '后勤服务中心', '采购员', 1, '副采购员'),
('finance01', '$2a$10$7JB720yubVSOfvVWdBYoOeWpyAcXMsEiUHUOWpylsHVSuqiKVoFla', '王财务', 'FIN001', '<EMAIL>', '13800000003', '财务科', '财务主管', 1, '财务主管'),
('supervisor01', '$2a$10$7JB720yubVSOfvVWdBYoOeWpyAcXMsEiUHUOWpylsHVSuqiKVoFla', '刘监督', 'SUP001', '<EMAIL>', '13800000004', '监察室', '履约监督员', 1, '履约监督员'),
('auditor01', '$2a$10$7JB720yubVSOfvVWdBYoOeWpyAcXMsEiUHUOWpylsHVSuqiKVoFla', '陈内审', 'AUD001', '<EMAIL>', '13800000005', '审计科', '内审员', 1, '内审人员'),
('dept01', '$2a$10$7JB720yubVSOfvVWdBYoOeWpyAcXMsEiUHUOWpylsHVSuqiKVoFla', '赵部长', 'DEPT001', '<EMAIL>', '13800000006', '办公室', '部门负责人', 1, '办公室主任'),
('classified01', '$2a$10$7JB720yubVSOfvVWdBYoOeWpyAcXMsEiUHUOWpylsHVSuqiKVoFla', '孙保密', 'CLS001', '<EMAIL>', '13800000007', '保密办', '涉密管理员', 1, '涉密项目管理员');

-- =============================================
-- 4. 初始化用户角色关联
-- =============================================

-- 分配用户角色
INSERT INTO user_roles (user_id, role_id) VALUES
(1, 1),  -- admin -> 系统管理员
(2, 3),  -- procurement01 -> 采购员
(3, 3),  -- procurement02 -> 采购员
(4, 4),  -- finance01 -> 财务主管
(5, 6),  -- supervisor01 -> 履约监督员
(6, 7),  -- auditor01 -> 内审人员
(7, 8),  -- dept01 -> 部门负责人
(8, 10); -- classified01 -> 涉密项目管理员

-- =============================================
-- 5. 初始化角色权限关联（部分示例）
-- =============================================

-- 系统管理员拥有所有权限（这里只列出部分，实际应该包含所有权限）
INSERT INTO role_permissions (role_id, permission_id) 
SELECT 1, id FROM permissions WHERE permission_code LIKE 'SYSTEM%';

-- 采购员权限
INSERT INTO role_permissions (role_id, permission_id) 
SELECT 3, id FROM permissions WHERE permission_code IN (
    'PROCUREMENT', 'PROCUREMENT:REQUIREMENT', 'PROCUREMENT:REQUIREMENT:LIST',
    'PROCUREMENT:PROJECT', 'PROCUREMENT:PROJECT:LIST', 'PROCUREMENT:PROJECT:ADD',
    'SUPPLIER', 'SUPPLIER:LIST', 'SUPPLIER:QUOTE',
    'CONTRACT', 'CONTRACT:LIST', 'CONTRACT:ADD'
);

-- 财务主管权限
INSERT INTO role_permissions (role_id, permission_id) 
SELECT 4, id FROM permissions WHERE permission_code IN (
    'FINANCE', 'FINANCE:PAYMENT', 'FINANCE:PAYMENT:APPROVE'
);

-- =============================================
-- 6. 初始化供应商示例数据
-- =============================================

-- 插入示例供应商
INSERT INTO suppliers (supplier_code, supplier_name, supplier_type, legal_person, contact_person, contact_phone, contact_email, address, business_license, bank_name, bank_account, business_scope, cooperation_status, status, remark) VALUES
('SUP001', '北京科技有限公司', 'ENTERPRISE', '张总', '李经理', '010-********', '<EMAIL>', '北京市海淀区中关村大街1号', '91110000********9X', '中国银行北京分行', '********90********9', '计算机软硬件销售', 'NORMAL', 1, '优质供应商'),
('SUP002', '上海办公用品公司', 'ENTERPRISE', '王总', '赵经理', '021-********', '<EMAIL>', '上海市浦东新区陆家嘴路100号', '913100009********Y', '工商银行上海分行', '9********09********', '办公用品销售', 'NORMAL', 1, '长期合作伙伴'),
('SUP003', '广州家具制造厂', 'ENTERPRISE', '刘总', '陈经理', '020-********', '<EMAIL>', '广州市天河区珠江路200号', '91440000********1Z', '建设银行广州分行', '****************111', '办公家具制造销售', 'NORMAL', 1, '家具专业供应商');

SELECT '初始化数据插入完成！' as '状态';
SELECT '默认管理员账户：admin/admin123' as '登录信息';
SELECT '请及时修改默认密码！' as '安全提醒';
